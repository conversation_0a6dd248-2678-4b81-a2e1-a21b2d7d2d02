import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { entryId, videoURL } = body;

    // Validate required fields
    if (!entryId || !videoURL) {
      return NextResponse.json(
        { error: "entryId and videoURL are required" },
        { status: 400 }
      );
    }

    // Validate that entryId is a valid string
    if (typeof entryId !== "string" || typeof videoURL !== "string") {
      return NextResponse.json(
        { error: "entryId and videoURL must be strings" },
        { status: 400 }
      );
    }

    // Check if entry exists
    const existingEntry = await db.entries.findUnique({
      where: { id: entryId },
    });

    if (!existingEntry) {
      return NextResponse.json(
        { error: "Entry not found" },
        { status: 404 }
      );
    }

    // Update the entry with the video URL
    const updatedEntry = await db.entries.update({
      where: { id: entryId },
      data: { videoUrl: videoURL },
    });

    return NextResponse.json({
      success: true,
      message: "Entry updated successfully",
      entry: {
        id: updatedEntry.id,
        videoUrl: updatedEntry.videoUrl,
        updatedAt: updatedEntry.updatedAt,
      },
    });

  } catch (error) {
    console.error("Error updating entry:", error);
    
    // Handle Prisma errors
    if (error instanceof Error) {
      if (error.message.includes("Record to update not found")) {
        return NextResponse.json(
          { error: "Entry not found" },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Optional: Add GET method to retrieve entry details
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const entryId = searchParams.get("entryId");

    if (!entryId) {
      return NextResponse.json(
        { error: "entryId parameter is required" },
        { status: 400 }
      );
    }

    const entry = await db.entries.findUnique({
      where: { id: entryId },
      select: {
        id: true,
        videoUrl: true,
        prompt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!entry) {
      return NextResponse.json(
        { error: "Entry not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      entry,
    });

  } catch (error) {
    console.error("Error retrieving entry:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
