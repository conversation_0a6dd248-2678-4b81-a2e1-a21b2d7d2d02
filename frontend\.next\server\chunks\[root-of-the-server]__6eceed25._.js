module.exports = {

"[project]/.next-internal/server/app/api/entries/update/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@opentelemetry/api", () => require("@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": (function(__turbopack_context__) {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/entries/update/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(req) {
    const data = await req.json();
    const { entryId, videoURL } = data;
    // Validate required fields
    if (!entryId || !videoURL) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "entryId and videoURL are required"
        }, {
            status: 400
        });
    }
    // Validate that entryId is a valid string
    if (typeof entryId !== "string" || typeof videoURL !== "string") {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "entryId and videoURL must be strings"
        }, {
            status: 400
        });
    }
    console.log("Updating entry", entryId, "with video URL", videoURL);
// Check if entry exists
//const existingEntry = await db.entries.findUnique({
// where: { id: entryId },
// });
// if (!existingEntry) {
//   return NextResponse.json(
//     { error: "Entry not found" },
//    { status: 404 }
//   );
//  }
//   // Update the entry with the video URL
//   const updatedEntry = await db.entries.update({
//     where: { id: entryId },
//     data: { videoUrl: videoURL },
//   });
//   return NextResponse.json({
//     success: true,
//     message: "Entry updated successfully",
//     entry: {
//       id: updatedEntry.id,
//       videoUrl: updatedEntry.videoUrl,
//       updatedAt: updatedEntry.updatedAt,
//     },
//   });
// } catch (error) {
//   console.error("Error updating entry:", error);
//   // Handle Prisma errors
//   if (error instanceof Error) {
//     if (error.message.includes("Record to update not found")) {
//       return NextResponse.json(
//         { error: "Entry not found" },
//         { status: 404 }
//       );
//     }
//   }
//   return NextResponse.json(
//     { error: "Internal server error" },
//     { status: 500 }
//   );
// }
}
async function GET(req) {
    // try {
    const { searchParams } = new URL(req.url);
    const entryId = searchParams.get("entryId");
    // if (!entryId) {
    //   return NextResponse.json(
    //     { error: "entryId parameter is required" },
    //     { status: 400 }
    //   );
    // }
    // const entry = await db.entries.findUnique({
    //   where: { id: entryId },
    //   select: {
    //     id: true,
    //     videoUrl: true,
    //     prompt: true,
    //     createdAt: true,
    //     updatedAt: true,
    //   },
    // });
    //   if (!entry) {
    //     return NextResponse.json({ error: "Entry not found" }, { status: 404 });
    //   }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: true,
        entryId
    });
// } catch (error) {
//   console.error("Error retrieving entry:", error);
//   return NextResponse.json(
//     { error: "Internal server error" },
//     { status: 500 }
//   );
// }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__6eceed25._.js.map