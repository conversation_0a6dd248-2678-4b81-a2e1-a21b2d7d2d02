{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\r\n        destructive:\r\n          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\r\n        outline: 'text-foreground',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/mvpblocks/faq-2.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { cn } from '@/lib/utils';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { MinusIcon, PlusIcon } from 'lucide-react';\r\n\r\ninterface FaqItem {\r\n  id: string;\r\n  question: string;\r\n  answer: string;\r\n  category: 'general' | 'pricing' | 'technical' | 'support';\r\n}\r\n\r\nconst faqItems: FaqItem[] = [\r\n  {\r\n    id: '1',\r\n    question: 'What is MVPBlocks?',\r\n    answer:\r\n      'MVPBlocks is a collection of ready-to-use UI components built with Next.js and Tailwind CSS. It helps developers quickly build beautiful, responsive websites without starting from scratch.',\r\n    category: 'general',\r\n  },\r\n  {\r\n    id: '2',\r\n    question: 'Is MVPBlocks free to use?',\r\n    answer:\r\n      'Yes, MVPBlocks is completely free and open-source. You can use it for personal and commercial projects without any restrictions or attribution requirements.',\r\n    category: 'general',\r\n  },\r\n  {\r\n    id: '3',\r\n    question: 'Do I need to know Tailwind CSS to use MVPBlocks?',\r\n    answer:\r\n      \"While having Tailwind CSS knowledge is helpful, it's not required. You can simply copy and paste our components into your project and make basic modifications without deep Tailwind expertise.\",\r\n    category: 'technical',\r\n  },\r\n  {\r\n    id: '4',\r\n    question: 'How do I install MVPBlocks?',\r\n    answer:\r\n      \"You don't need to install MVPBlocks as a package. Simply browse our component library, find the components you need, and copy the code into your project. Make sure you have the required dependencies installed.\",\r\n    category: 'technical',\r\n  },\r\n  {\r\n    id: '5',\r\n    question: 'Can I customize the components?',\r\n    answer:\r\n      'Absolutely! All components are built with customization in mind. You can modify colors, spacing, typography, and more using Tailwind classes or by editing the component code directly.',\r\n    category: 'technical',\r\n  },\r\n  {\r\n    id: '6',\r\n    question: 'Do MVPBlocks components work with dark mode?',\r\n    answer:\r\n      \"Yes, all MVPBlocks components are designed to work seamlessly with both light and dark modes. They automatically adapt to your site's theme settings.\",\r\n    category: 'technical',\r\n  },\r\n  {\r\n    id: '7',\r\n    question: 'How often are new components added?',\r\n    answer:\r\n      'We regularly add new components to the library. Our goal is to provide a comprehensive set of components for all common UI patterns and website sections.',\r\n    category: 'general',\r\n  },\r\n  {\r\n    id: '8',\r\n    question: 'How can I contribute to MVPBlocks?',\r\n    answer:\r\n      'We welcome contributions! You can contribute by creating new components, improving existing ones, fixing bugs, or enhancing documentation. Check our GitHub repository for contribution guidelines.',\r\n    category: 'support',\r\n  },\r\n];\r\n\r\nconst categories = [\r\n  { id: 'all', label: 'All' },\r\n  { id: 'general', label: 'General' },\r\n  { id: 'technical', label: 'Technical' },\r\n  { id: 'pricing', label: 'Pricing' },\r\n  { id: 'support', label: 'Support' },\r\n];\r\n\r\nexport default function Faq2() {\r\n  const [activeCategory, setActiveCategory] = useState<string>('all');\r\n  const [expandedId, setExpandedId] = useState<string | null>(null);\r\n\r\n  const filteredFaqs =\r\n    activeCategory === 'all'\r\n      ? faqItems\r\n      : faqItems.filter((item) => item.category === activeCategory);\r\n\r\n  const toggleExpand = (id: string) => {\r\n    setExpandedId(expandedId === id ? null : id);\r\n  };\r\n\r\n  return (\r\n    <section className=\"bg-background py-16\">\r\n      <div className=\"container mx-auto max-w-6xl px-4 md:px-6\">\r\n        <div className=\"mb-12 flex flex-col items-center\">\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"mb-4 border-primary px-3 py-1 text-xs font-medium uppercase tracking-wider\"\r\n          >\r\n            FAQs\r\n          </Badge>\r\n\r\n          <h2 className=\"mb-6 text-center text-4xl font-bold tracking-tight text-foreground md:text-5xl\">\r\n            Frequently Asked Questions\r\n          </h2>\r\n\r\n          <p className=\"max-w-2xl text-center text-muted-foreground\">\r\n            Find answers to common questions about MVPBlocks and how to use our\r\n            components to build your next project.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Category Tabs */}\r\n        <div className=\"mb-10 flex flex-wrap justify-center gap-2\">\r\n          {categories.map((category) => (\r\n            <button\r\n              key={category.id}\r\n              onClick={() => setActiveCategory(category.id)}\r\n              className={cn(\r\n                'rounded-full px-4 py-2 text-sm font-medium transition-all',\r\n                activeCategory === category.id\r\n                  ? 'bg-primary text-primary-foreground'\r\n                  : 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\r\n              )}\r\n            >\r\n              {category.label}\r\n            </button>\r\n          ))}\r\n        </div>\r\n\r\n        {/* FAQ Grid */}\r\n        <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2\">\r\n          <AnimatePresence>\r\n            {filteredFaqs.map((faq, index) => (\r\n              <motion.div\r\n                key={faq.id}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: -20 }}\r\n                transition={{ duration: 0.3, delay: index * 0.05 }}\r\n                className={cn(\r\n                  'h-fit overflow-hidden rounded-xl border border-border',\r\n                  expandedId === faq.id\r\n                    ? 'shadow-3xl bg-card/50'\r\n                    : 'bg-card/50',\r\n                )}\r\n                style={{ minHeight: '88px' }}\r\n              >\r\n                <button\r\n                  onClick={() => toggleExpand(faq.id)}\r\n                  className=\"flex w-full items-center justify-between p-6 text-left\"\r\n                >\r\n                  <h3 className=\"text-lg font-medium text-foreground\">\r\n                    {faq.question}\r\n                  </h3>\r\n                  <div className=\"ml-4 flex-shrink-0\">\r\n                    {expandedId === faq.id ? (\r\n                      <MinusIcon className=\"h-5 w-5 text-primary\" />\r\n                    ) : (\r\n                      <PlusIcon className=\"h-5 w-5 text-primary\" />\r\n                    )}\r\n                  </div>\r\n                </button>\r\n\r\n                <AnimatePresence>\r\n                  {expandedId === faq.id && (\r\n                    <motion.div\r\n                      initial={{ height: 0, opacity: 0 }}\r\n                      animate={{ height: 'auto', opacity: 1 }}\r\n                      exit={{ height: 0, opacity: 0 }}\r\n                      transition={{ duration: 0.3 }}\r\n                      className=\"overflow-hidden\"\r\n                    >\r\n                      <div className=\"border-t border-border px-6 pb-6 pt-2\">\r\n                        <p className=\"text-muted-foreground\">{faq.answer}</p>\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n                </AnimatePresence>\r\n              </motion.div>\r\n            ))}\r\n          </AnimatePresence>\r\n        </div>\r\n\r\n        {/* Contact CTA */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5, duration: 0.5 }}\r\n          className=\"mt-16 text-center\"\r\n        >\r\n          <p className=\"mb-4 text-muted-foreground\">\r\n            Can&apos;t find what you&apos;re looking for?\r\n          </p>\r\n          <a\r\n            href=\"#\"\r\n            className=\"inline-flex items-center justify-center rounded-lg border-2 border-primary px-6 py-3 font-medium text-foreground transition-colors hover:bg-primary hover:text-primary-foreground\"\r\n          >\r\n            Contact Support\r\n          </a>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAeA,MAAM,WAAsB;IAC1B;QACE,IAAI;QACJ,UAAU;QACV,QACE;QACF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QACE;QACF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QACE;QACF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QACE;QACF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QACE;QACF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QACE;QACF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QACE;QACF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QACE;QACF,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IACjB;QAAE,IAAI;QAAO,OAAO;IAAM;IAC1B;QAAE,IAAI;QAAW,OAAO;IAAU;IAClC;QAAE,IAAI;QAAa,OAAO;IAAY;IACtC;QAAE,IAAI;QAAW,OAAO;IAAU;IAClC;QAAE,IAAI;QAAW,OAAO;IAAU;CACnC;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,eACJ,mBAAmB,QACf,WACA,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK;IAElD,MAAM,eAAe,CAAC;QACpB,cAAc,eAAe,KAAK,OAAO;IAC3C;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCACX;;;;;;sCAID,8OAAC;4BAAG,WAAU;sCAAiF;;;;;;sCAI/F,8OAAC;4BAAE,WAAU;sCAA8C;;;;;;;;;;;;8BAO7D,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4BAEC,SAAS,IAAM,kBAAkB,SAAS,EAAE;4BAC5C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6DACA,mBAAmB,SAAS,EAAE,GAC1B,uCACA;sCAGL,SAAS,KAAK;2BATV,SAAS,EAAE;;;;;;;;;;8BAetB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;kCACb,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAK;gCACjD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,eAAe,IAAI,EAAE,GACjB,0BACA;gCAEN,OAAO;oCAAE,WAAW;gCAAO;;kDAE3B,8OAAC;wCACC,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;0DACX,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAI,WAAU;0DACZ,eAAe,IAAI,EAAE,iBACpB,8OAAC,wMAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAErB,8OAAC,sMAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK1B,8OAAC,yLAAA,CAAA,kBAAe;kDACb,eAAe,IAAI,EAAE,kBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ;gDAAG,SAAS;4CAAE;4CACjC,SAAS;gDAAE,QAAQ;gDAAQ,SAAS;4CAAE;4CACtC,MAAM;gDAAE,QAAQ;gDAAG,SAAS;4CAAE;4CAC9B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAyB,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;+BAvCnD,IAAI,EAAE;;;;;;;;;;;;;;;8BAkDnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/ui/marquee.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\nimport { ComponentPropsWithoutRef } from \"react\";\r\n \r\ninterface MarqueeProps extends ComponentPropsWithoutRef<\"div\"> {\r\n  /**\r\n   * Optional CSS class name to apply custom styles\r\n   */\r\n  className?: string;\r\n  /**\r\n   * Whether to reverse the animation direction\r\n   * @default false\r\n   */\r\n  reverse?: boolean;\r\n  /**\r\n   * Whether to pause the animation on hover\r\n   * @default false\r\n   */\r\n  pauseOnHover?: boolean;\r\n  /**\r\n   * Content to be displayed in the marquee\r\n   */\r\n  children: React.ReactNode;\r\n  /**\r\n   * Whether to animate vertically instead of horizontally\r\n   * @default false\r\n   */\r\n  vertical?: boolean;\r\n  /**\r\n   * Number of times to repeat the content\r\n   * @default 4\r\n   */\r\n  repeat?: number;\r\n}\r\n \r\nexport function Marquee({\r\n  className,\r\n  reverse = false,\r\n  pauseOnHover = false,\r\n  children,\r\n  vertical = false,\r\n  repeat = 4,\r\n  ...props\r\n}: MarqueeProps) {\r\n  return (\r\n    <div\r\n      {...props}\r\n      className={cn(\r\n        \"group flex overflow-hidden p-2 [--duration:40s] [--gap:1rem] [gap:var(--gap)]\",\r\n        {\r\n          \"flex-row\": !vertical,\r\n          \"flex-col\": vertical,\r\n        },\r\n        className,\r\n      )}\r\n    >\r\n      {Array(repeat)\r\n        .fill(0)\r\n        .map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className={cn(\"flex shrink-0 justify-around [gap:var(--gap)]\", {\r\n              \"animate-marquee flex-row\": !vertical,\r\n              \"animate-marquee-vertical flex-col\": vertical,\r\n              \"group-hover:[animation-play-state:paused]\": pauseOnHover,\r\n              \"[animation-direction:reverse]\": reverse,\r\n            })}\r\n          >\r\n            {children}\r\n          </div>\r\n        ))}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAkCO,SAAS,QAAQ,EACtB,SAAS,EACT,UAAU,KAAK,EACf,eAAe,KAAK,EACpB,QAAQ,EACR,WAAW,KAAK,EAChB,SAAS,CAAC,EACV,GAAG,OACU;IACb,qBACE,8OAAC;QACE,GAAG,KAAK;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA;YACE,YAAY,CAAC;YACb,YAAY;QACd,GACA;kBAGD,MAAM,QACJ,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;oBAC7D,4BAA4B,CAAC;oBAC7B,qCAAqC;oBACrC,6CAA6C;oBAC7C,iCAAiC;gBACnC;0BAEC;eARI;;;;;;;;;;AAajB", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/mvpblocks/testimonials-marquee.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { motion } from 'framer-motion';\r\nimport { Star } from 'lucide-react';\r\nimport { Marquee } from '@/components/ui/marquee';\r\n\r\nexport function Highlight({\r\n  children,\r\n  className,\r\n}: {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}) {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        'bg-violet-500/10 p-1 py-0.5 font-bold text-violet-500',\r\n        className,\r\n      )}\r\n    >\r\n      {children}\r\n    </span>\r\n  );\r\n}\r\n\r\nexport interface TestimonialCardProps {\r\n  name: string;\r\n  role: string;\r\n  img?: string;\r\n  description: React.ReactNode;\r\n  className?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nexport function TestimonialCard({\r\n  description,\r\n  name,\r\n  img,\r\n  role,\r\n  className,\r\n  ...props // Capture the rest of the props\r\n}: TestimonialCardProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'mb-4 flex w-full cursor-pointer break-inside-avoid flex-col items-center justify-between gap-6 rounded-xl p-4',\r\n        // theme styles\r\n        'border border-border bg-card/50 shadow-sm',\r\n        // hover effect\r\n        'transition-all duration-300 hover:-translate-y-0.5 hover:shadow-md',\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <div className=\"select-none text-sm font-normal text-muted-foreground\">\r\n        {description}\r\n        <div className=\"flex flex-row py-1\">\r\n          <Star className=\"size-4 fill-violet-500 text-violet-500\" />\r\n          <Star className=\"size-4 fill-violet-500 text-violet-500\" />\r\n          <Star className=\"size-4 fill-violet-500 text-violet-500\" />\r\n          <Star className=\"size-4 fill-violet-500 text-violet-500\" />\r\n          <Star className=\"size-4 fill-violet-500 text-violet-500\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex w-full select-none items-center justify-start gap-5\">\r\n        <img\r\n          width={40}\r\n          height={40}\r\n          src={img || ''}\r\n          alt={name}\r\n          className=\"size-10 rounded-full ring-1 ring-violet-500/20 ring-offset-2\"\r\n        />\r\n\r\n        <div>\r\n          <p className=\"font-medium text-foreground\">{name}</p>\r\n          <p className=\"text-xs font-normal text-muted-foreground\">{role}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\nconst testimonials = [\r\n  {\r\n    name: 'Jordan Hayes',\r\n    role: 'CTO at Quantum Innovations',\r\n    img: 'https://randomuser.me/api/portraits/men/22.jpg',\r\n    description: (\r\n      <p>\r\n        NexaUI has completely transformed our development workflow.\r\n        <Highlight>\r\n          The component system saved us weeks of custom coding and design work.\r\n        </Highlight>{' '}\r\n        Our team can now focus on business logic instead of UI details.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Maya Rodriguez',\r\n    role: 'Lead Developer at Skyline Digital',\r\n    img: 'https://randomuser.me/api/portraits/women/33.jpg',\r\n    description: (\r\n      <p>\r\n        I was skeptical at first, but NexaUI proved me wrong.\r\n        <Highlight>\r\n          The accessibility features and responsive design are top-notch.\r\n        </Highlight>{' '}\r\n        It&apos;s rare to find a framework that prioritizes both aesthetics and\r\n        functionality.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Ethan Park',\r\n    role: 'Startup Founder at Elevate Labs',\r\n    img: 'https://randomuser.me/api/portraits/men/32.jpg',\r\n    description: (\r\n      <p>\r\n        As a non-technical founder, NexaUI has been a game-changer for our MVP.\r\n        <Highlight>We launched three months ahead of schedule.</Highlight> The\r\n        modular components allowed us to iterate quickly based on user feedback.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Zoe Bennett',\r\n    role: 'UX Architect at Fusion Systems',\r\n    img: 'https://randomuser.me/api/portraits/women/44.jpg',\r\n    description: (\r\n      <p>\r\n        NexaUI&apos;s attention to detail is impressive.\r\n        <Highlight>\r\n          The micro-interactions and animations create a polished experience.\r\n        </Highlight>{' '}\r\n        It&apos;s become our go-to solution for client projects with tight\r\n        deadlines.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Victor Nguyen',\r\n    role: 'Product Lead at FinEdge',\r\n    img: 'https://randomuser.me/api/portraits/men/55.jpg',\r\n    description: (\r\n      <p>\r\n        Our financial dashboard needed a complete overhaul, and NexaUI\r\n        delivered.\r\n        <Highlight>\r\n          The data visualization components are both beautiful and functional.\r\n        </Highlight>{' '}\r\n        User engagement has increased by 47% since the redesign.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Amara Johnson',\r\n    role: 'Frontend Specialist at Nimbus Tech',\r\n    img: 'https://randomuser.me/api/portraits/women/67.jpg',\r\n    description: (\r\n      <p>\r\n        The documentation for NexaUI is exceptional.\r\n        <Highlight>\r\n          I was able to implement complex UI patterns in just a few hours.\r\n        </Highlight>{' '}\r\n        The TypeScript support is also a major productivity booster.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Leo Tanaka',\r\n    role: 'Creative Technologist at Prism Agency',\r\n    img: 'https://randomuser.me/api/portraits/men/78.jpg',\r\n    description: (\r\n      <p>\r\n        NexaUI has the perfect balance of flexibility and structure.\r\n        <Highlight>\r\n          We can maintain brand consistency while still creating unique\r\n          experiences.\r\n        </Highlight>{' '}\r\n        Our clients are consistently impressed with the results.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Sophia Martinez',\r\n    role: 'E-commerce Director at StyleHub',\r\n    img: 'https://randomuser.me/api/portraits/women/89.jpg',\r\n    description: (\r\n      <p>\r\n        Our conversion rates have increased by 28% since implementing NexaUI.\r\n        <Highlight>\r\n          The checkout flow components are optimized for both desktop and\r\n          mobile.\r\n        </Highlight>{' '}\r\n        The dark mode support was also a huge hit with our customers.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Aiden Wilson',\r\n    role: 'Healthcare Solutions Architect',\r\n    img: 'https://randomuser.me/api/portraits/men/92.jpg',\r\n    description: (\r\n      <p>\r\n        NexaUI&apos;s accessibility features were crucial for our healthcare\r\n        platform.\r\n        <Highlight>\r\n          We passed compliance requirements with minimal additional work.\r\n        </Highlight>{' '}\r\n        The form components are especially well-designed for complex data entry.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    name: 'Olivia Chen',\r\n    role: 'EdTech Product Manager at LearnSphere',\r\n    img: 'https://randomuser.me/api/portraits/women/29.jpg',\r\n    description: (\r\n      <p>\r\n        Our educational platform needed to work for students of all ages and\r\n        abilities.\r\n        <Highlight>\r\n          NexaUI&apos;s inclusive design principles made this possible without\r\n          compromise.\r\n        </Highlight>{' '}\r\n        The interactive components have significantly improved student\r\n        engagement.\r\n      </p>\r\n    ),\r\n  },\r\n];\r\n\r\nexport default function Testimonials() {\r\n  return (\r\n    <section className=\"container relative py-10\">\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute -left-20 top-20 z-10 h-64 w-64 rounded-full bg-violet-500/5 blur-3xl\" />\r\n      <div className=\"absolute -right-20 bottom-20 z-10 h-64 w-64 rounded-full bg-violet-500/5 blur-3xl\" />\r\n\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        <h2 className=\"mb-4 text-center text-4xl font-bold leading-[1.2] tracking-tighter text-foreground md:text-5xl\">\r\n          What Our Users Are Saying\r\n        </h2>\r\n        <h3 className=\"mx-auto mb-8 max-w-lg text-balance text-center text-lg font-medium tracking-tight text-muted-foreground\">\r\n          Don&apos;t just take our word for it. Here&apos;s what{' '}\r\n          <span className=\"bg-gradient-to-r from-violet-500 to-sky-500 bg-clip-text text-transparent\">\r\n            real developers\r\n          </span>{' '}\r\n          are saying about{' '}\r\n          <span className=\"font-semibold text-violet-500\">NexaUI</span>\r\n        </h3>\r\n      </motion.div>\r\n\r\n      <div className=\"relative mt-6 max-h-screen overflow-hidden\">\r\n        <div className=\"gap-4 md:columns-2 xl:columns-3 2xl:columns-4\">\r\n          {Array(Math.ceil(testimonials.length / 3))\r\n            .fill(0)\r\n            .map((_, i) => (\r\n              <Marquee\r\n                vertical\r\n                key={i}\r\n                className={cn({\r\n                  '[--duration:60s]': i === 1,\r\n                  '[--duration:30s]': i === 2,\r\n                  '[--duration:70s]': i === 3,\r\n                })}\r\n              >\r\n                {testimonials.slice(i * 3, (i + 1) * 3).map((card, idx) => (\r\n                  <motion.div\r\n                    key={idx}\r\n                    initial={{ opacity: 0 }}\r\n                    whileInView={{ opacity: 1 }}\r\n                    viewport={{ once: true }}\r\n                    transition={{\r\n                      delay: Math.random() * 0.8,\r\n                      duration: 1.2,\r\n                    }}\r\n                  >\r\n                    <TestimonialCard {...card} />\r\n                  </motion.div>\r\n                ))}\r\n              </Marquee>\r\n            ))}\r\n        </div>\r\n        <div className=\"pointer-events-none absolute inset-x-0 bottom-0 h-1/4 w-full bg-gradient-to-t from-background from-20%\"></div>\r\n        <div className=\"pointer-events-none absolute inset-x-0 top-0 h-1/4 w-full bg-gradient-to-b from-background from-20%\"></div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS,UAAU,EACxB,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;kBAGD;;;;;;AAGP;AAWO,SAAS,gBAAgB,EAC9B,WAAW,EACX,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,SAAS,EACT,GAAG,MAAM,gCAAgC;EACpB;IACrB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iHACA,eAAe;QACf,6CACA,eAAe;QACf,sEACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAO;wBACP,QAAQ;wBACR,KAAK,OAAO;wBACZ,KAAK;wBACL,WAAU;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;0CAC5C,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;;;;;;;;;;;;;;AAKpE;AACA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAED,8OAAC;8BAAU;;;;;;gBAEE;gBAAI;;;;;;;IAIvB;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAED,8OAAC;8BAAU;;;;;;gBAEE;gBAAI;;;;;;;IAKvB;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAED,8OAAC;8BAAU;;;;;;gBAAuD;;;;;;;IAIxE;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAED,8OAAC;8BAAU;;;;;;gBAEE;gBAAI;;;;;;;IAKvB;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAGD,8OAAC;8BAAU;;;;;;gBAEE;gBAAI;;;;;;;IAIvB;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAED,8OAAC;8BAAU;;;;;;gBAEE;gBAAI;;;;;;;IAIvB;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAED,8OAAC;8BAAU;;;;;;gBAGE;gBAAI;;;;;;;IAIvB;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAED,8OAAC;8BAAU;;;;;;gBAGE;gBAAI;;;;;;;IAIvB;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAGD,8OAAC;8BAAU;;;;;;gBAEE;gBAAI;;;;;;;IAIvB;IACA;QACE,MAAM;QACN,MAAM;QACN,KAAK;QACL,2BACE,8OAAC;;gBAAE;8BAGD,8OAAC;8BAAU;;;;;;gBAGE;gBAAI;;;;;;;IAKvB;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,8OAAC;wBAAG,WAAU;kCAAiG;;;;;;kCAG/G,8OAAC;wBAAG,WAAU;;4BAA0G;4BAC/D;0CACvD,8OAAC;gCAAK,WAAU;0CAA4E;;;;;;4BAEpF;4BAAI;4BACK;0CACjB,8OAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;0BAIpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,MAAM,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG,IACpC,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC,mIAAA,CAAA,UAAO;gCACN,QAAQ;gCAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;oCACZ,oBAAoB,MAAM;oCAC1B,oBAAoB,MAAM;oCAC1B,oBAAoB,MAAM;gCAC5B;0CAEC,aAAa,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,oBACjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;wCAAE;wCACtB,aAAa;4CAAE,SAAS;wCAAE;wCAC1B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CACV,OAAO,KAAK,MAAM,KAAK;4CACvB,UAAU;wCACZ;kDAEA,cAAA,8OAAC;4CAAiB,GAAG,IAAI;;;;;;uCATpB;;;;;+BATJ;;;;;;;;;;kCAwBb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/bricolage_grotesque_be76c17e.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"bricolage_grotesque_be76c17e-module__Yfokwa__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/bricolage_grotesque_be76c17e.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22Hero.tsx%22,%22import%22:%22Bricolage_Grotesque%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22brico%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Bricolage Grotesque', 'Bricolage Grotesque Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,mKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,mKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,mKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/shared/Hero.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef } from \"react\";\r\nimport { Bricolage_Grotesque } from \"next/font/google\";\r\n\r\nconst brico = Bricolage_Grotesque({ subsets: [\"latin\"] });\r\nimport { cn } from \"@/lib/utils\";\r\nimport { <PERSON><PERSON>, <PERSON> } from \"lucide-react\";\r\n\r\nconst vertShader = `\r\nprecision mediump float;\r\nvarying vec2 vUv;\r\nattribute vec2 a_position;\r\nvoid main() {\r\n  vUv = .5 * (a_position + 1.);\r\n  gl_Position = vec4(a_position, 0.0, 1.0);\r\n}`;\r\n\r\nconst fragShader = `\r\nprecision mediump float;\r\nvarying vec2 vUv;\r\nuniform float u_time;\r\nuniform float u_ratio;\r\nuniform vec2 u_pointer_position;\r\nuniform float u_scroll_progress;\r\n\r\nvec2 rotate(vec2 uv, float th) {\r\n  return mat2(cos(th), sin(th), -sin(th), cos(th)) * uv;\r\n}\r\n\r\nfloat neuro_shape(vec2 uv, float t, float p) {\r\n  vec2 sine_acc = vec2(0.);\r\n  vec2 res = vec2(0.);\r\n  float scale = 8.;\r\n  for (int j = 0; j < 15; j++) {\r\n    uv = rotate(uv, 1.);\r\n    sine_acc = rotate(sine_acc, 1.);\r\n    vec2 layer = uv * scale + float(j) + sine_acc - t;\r\n    sine_acc += sin(layer) + 2.4 * p;\r\n    res += (.5 + .5 * cos(layer)) / scale;\r\n    scale *= (1.2);\r\n  }\r\n  return res.x + res.y;\r\n}\r\n\r\nvoid main() {\r\n  vec2 uv = .5 * vUv;\r\n  uv.x *= u_ratio;\r\n\r\n  vec2 pointer = vUv - u_pointer_position;\r\n  pointer.x *= u_ratio;\r\n  float p = clamp(length(pointer), 0., 1.);\r\n  p = .5 * pow(1. - p, 2.);\r\n\r\n  float t = .001 * u_time;\r\n  vec3 color = vec3(0.);\r\n\r\n  float noise = neuro_shape(uv, t, p);\r\n\r\n  noise = 1.2 * pow(noise, 3.);\r\n  noise += pow(noise, 10.);\r\n  noise = max(.0, noise - .5);\r\n  noise *= (1. - length(vUv - .5));\r\n\r\n  color = vec3(0.5, 0.15, 0.65);\r\n  color += vec3(0.3, 0.0, 0.25) * sin(3.0 * u_scroll_progress + 1.5);\r\n\r\n  color = color * noise;\r\n\r\n  gl_FragColor = vec4(color, noise);\r\n}`;\r\n\r\nconst NeuralAI = () => {\r\n  const canvasRef = useRef<HTMLCanvasElement | null>(null);\r\n  const pointer = useRef({ x: 0, y: 0, tX: 0, tY: 0 });\r\n  const uniforms = useRef<any>({});\r\n\r\n  useEffect(() => {\r\n    const canvas = canvasRef.current;\r\n    if (!canvas) return;\r\n    const gl = (canvas.getContext(\"webgl\") ||\r\n      canvas.getContext(\"experimental-webgl\")) as WebGLRenderingContext | null;\r\n    if (!gl) {\r\n      console.error(\"WebGL not supported by your browser.\");\r\n      return;\r\n    }\r\n\r\n    const devicePixelRatio = Math.min(window.devicePixelRatio, 2);\r\n\r\n    function createShader(\r\n      gl: WebGLRenderingContext,\r\n      source: string,\r\n      type: number\r\n    ) {\r\n      const shader = gl.createShader(type)!;\r\n      gl.shaderSource(shader, source);\r\n      gl.compileShader(shader);\r\n      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {\r\n        console.error(\"Shader error: \" + gl.getShaderInfoLog(shader));\r\n        return null;\r\n      }\r\n      return shader;\r\n    }\r\n\r\n    const vertexShader = createShader(gl, vertShader, gl.VERTEX_SHADER)!;\r\n    const fragmentShader = createShader(gl, fragShader, gl.FRAGMENT_SHADER)!;\r\n\r\n    const program = gl.createProgram()!;\r\n    gl.attachShader(program, vertexShader);\r\n    gl.attachShader(program, fragmentShader);\r\n    gl.linkProgram(program);\r\n\r\n    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {\r\n      console.error(\"Shader link error: \" + gl.getProgramInfoLog(program));\r\n      return;\r\n    }\r\n\r\n    const u: any = {};\r\n    const uniformCount = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);\r\n    for (let i = 0; i < uniformCount; i++) {\r\n      const uniformName = gl.getActiveUniform(program, i)?.name;\r\n      if (uniformName) {\r\n        u[uniformName] = gl.getUniformLocation(program, uniformName);\r\n      }\r\n    }\r\n    uniforms.current = u;\r\n\r\n    const vertices = new Float32Array([-1, -1, 1, -1, -1, 1, 1, 1]);\r\n    const vertexBuffer = gl.createBuffer()!;\r\n    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);\r\n    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);\r\n\r\n    gl.useProgram(program);\r\n    const positionLocation = gl.getAttribLocation(program, \"a_position\");\r\n    gl.enableVertexAttribArray(positionLocation);\r\n    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);\r\n\r\n    function resizeCanvas() {\r\n      if (!canvas || !gl) return;\r\n      canvas.width = window.innerWidth * devicePixelRatio;\r\n      canvas.height = window.innerHeight * devicePixelRatio;\r\n      gl.viewport(0, 0, canvas.width, canvas.height);\r\n      gl.uniform1f(uniforms.current.u_ratio, canvas.width / canvas.height);\r\n    }\r\n\r\n    function updateMouse(x: number, y: number) {\r\n      pointer.current.tX = x;\r\n      pointer.current.tY = y;\r\n    }\r\n\r\n    function render() {\r\n      if (!gl) return;\r\n      const time = performance.now();\r\n      const { x, y, tX, tY } = pointer.current;\r\n      pointer.current.x += (tX - x) * 0.2;\r\n      pointer.current.y += (tY - y) * 0.2;\r\n\r\n      gl.uniform1f(uniforms.current.u_time, time);\r\n      gl.uniform2f(\r\n        uniforms.current.u_pointer_position,\r\n        pointer.current.x / window.innerWidth,\r\n        1 - pointer.current.y / window.innerHeight\r\n      );\r\n      gl.uniform1f(\r\n        uniforms.current.u_scroll_progress,\r\n        window.scrollY / (2 * window.innerHeight)\r\n      );\r\n      gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);\r\n      requestAnimationFrame(render);\r\n    }\r\n\r\n    window.addEventListener(\"pointermove\", (e) =>\r\n      updateMouse(e.clientX, e.clientY)\r\n    );\r\n    window.addEventListener(\"touchmove\", (e) =>\r\n      updateMouse(e.touches[0].clientX, e.touches[0].clientY)\r\n    );\r\n    window.addEventListener(\"click\", (e) => updateMouse(e.clientX, e.clientY));\r\n    window.addEventListener(\"resize\", resizeCanvas);\r\n\r\n    resizeCanvas();\r\n    render();\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"relative z-10 min-h-screen overflow-hidden flex items-center pt-36 pb-20 px-4 md:px-8\">\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"absolute z-10 inset-0 w-full h-full opacity-10 dark:opacity-30 pointer-events-none\"\r\n      />\r\n      <div className=\"max-w-7xl mx-auto w-full grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\r\n        <div>\r\n          <div className=\"inline-block px-4 py-2 rounded-full dark:bg-white/5 bg-white/80 border dark:border-white/10 border-white/50 text-muted-foreground text-sm mb-6\">\r\n            <span className=\"flex items-center\">\r\n              <span className=\"w-2 h-2 bg-primary rounded-full mr-2\"></span>\r\n              Introducing Neural AI Platform 2.0\r\n            </span>\r\n          </div>\r\n\r\n          <h1\r\n            className={cn(\r\n              \"text-4xl md:text-7xl mb-6 font-bold md:leading-tight tracking-tighter\",\r\n              brico.className\r\n            )}\r\n          >\r\n            Learn Smarter,\r\n            <br />\r\n            Not Harder\r\n          </h1>\r\n\r\n          <p className=\"text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl\">\r\n            Our AI turns any topic into a short animated explainer, visual\r\n            notes, and smart Q&As — all personalized to your syllabus and level.\r\n            No clutter, just clear learning in seconds.\r\n          </p>\r\n\r\n          <div className=\"flex flex-col sm:flex-row gap-4\">\r\n            <a\r\n              href=\"#\"\r\n              className=\"py-4 px-8 rounded-xl bg-white text-gray-900 font-medium text-center transition-all hover:bg-white/90\"\r\n            >\r\n              Start Building Free\r\n            </a>\r\n            <a\r\n              href=\"#\"\r\n              className=\"py-4 px-8 rounded-xl bg-white/10 hover:bg-white/15 text-white font-medium text-center border border-white/10 transition-all\"\r\n            >\r\n              Watch Demo\r\n            </a>\r\n          </div>\r\n\r\n          <div className=\"mt-10 flex items-center gap-2\">\r\n            <div className=\"flex -space-x-2\">\r\n              <img\r\n                src=\"https://randomuser.me/api/portraits/women/44.jpg\"\r\n                className=\"w-10 h-10 rounded-full border-2 border-gray-900\"\r\n                alt=\"User\"\r\n              />\r\n              <img\r\n                src=\"https://randomuser.me/api/portraits/men/86.jpg\"\r\n                className=\"w-10 h-10 rounded-full border-2 border-gray-900\"\r\n                alt=\"User\"\r\n              />\r\n              <img\r\n                src=\"https://randomuser.me/api/portraits/women/63.jpg\"\r\n                className=\"w-10 h-10 rounded-full border-2 border-gray-900\"\r\n                alt=\"User\"\r\n              />\r\n            </div>\r\n            <div className=\"text-white/70 text-sm\">\r\n              Trusted by <span className=\"text-white font-medium\">10,000+</span>{\" \"}\r\n              developers worldwide\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"relative h-[500px] flex items-center justify-center\">\r\n          <div className=\"glass-effect bg-gray-400/5 dark:bg-white/5 border border-border/50 rounded-2xl w-80 h-80 absolute transform rotate-6 floating-delay\">\r\n            <div className=\"absolute top-6 left-6 right-6 bottom-6 flex flex-col\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <div className=\"w-8 h-8 rounded-lg bg-black/10 dark:bg-white/10 flex items-center justify-center\">\r\n                  <Brain className=\"w-5 h-5\" />\r\n                </div>\r\n                <span className=\"ml-3 text-lg\">DBMS concepts</span>\r\n              </div>\r\n              <div className=\"w-full relative max-w-xs\">\r\n                <div className=\"w-full h-48 rounded-xl gradient-border inner-glow overflow-hidden relative\">\r\n                  <div className=\"absolute inset-0 opacity-10\">\r\n                    <div\r\n                      className=\"w-full h-full animate-pulse\"\r\n                      style={{\r\n                        backgroundImage:\r\n                          \"linear-gradient(90deg, rgba(255,255,255,0.3) 1px, transparent 1px), linear-gradient(rgba(255,255,255,0.3) 1px, transparent 1px)\",\r\n                        backgroundSize: \"15px 15px\",\r\n                        // Light mode by default\r\n                      }}\r\n                    ></div>\r\n                  </div>\r\n\r\n                  <svg\r\n                    className=\"absolute inset-0 w-full h-full pointer-events-none\"\r\n                    viewBox=\"0 0 320 180\"\r\n                  >\r\n                    <defs>\r\n                      <linearGradient\r\n                        id=\"connectionGradient\"\r\n                        x1=\"0%\"\r\n                        y1=\"0%\"\r\n                        x2=\"100%\"\r\n                        y2=\"0%\"\r\n                      >\r\n                        <stop\r\n                          offset=\"0%\"\r\n                          style={{\r\n                            stopColor: \"#4f46e5\",\r\n                            stopOpacity: 0.8,\r\n                          }}\r\n                        />\r\n                        <stop\r\n                          offset=\"50%\"\r\n                          style={{ stopColor: \"#3b82f6\", stopOpacity: 1 }}\r\n                        />\r\n                        <stop\r\n                          offset=\"100%\"\r\n                          style={{\r\n                            stopColor: \"#8b5cf6\",\r\n                            stopOpacity: 0.8,\r\n                          }}\r\n                        />\r\n                      </linearGradient>\r\n                    </defs>\r\n\r\n                    <g\r\n                      stroke=\"url(#connectionGradient)\"\r\n                      strokeWidth=\"1.5\"\r\n                      fill=\"none\"\r\n                    >\r\n                      <path\r\n                        className=\"connector\"\r\n                        d=\"M80,60 L140,60 L140,90 L200,90\"\r\n                      />\r\n                      <path\r\n                        className=\"connector\"\r\n                        d=\"M200,90 L240,90 L240,60 L280,60\"\r\n                      />\r\n                      <path\r\n                        className=\"connector\"\r\n                        d=\"M140,90 L140,120 L200,120\"\r\n                      />\r\n                      <path\r\n                        className=\"connector\"\r\n                        d=\"M200,120 L240,120 L240,150 L200,150\"\r\n                      />\r\n\r\n                      <circle cx=\"80\" cy=\"60\" r=\"3\" fill=\"#4f46e5\" />\r\n                      <circle cx=\"200\" cy=\"90\" r=\"3\" fill=\"#3b82f6\" />\r\n                      <circle cx=\"280\" cy=\"60\" r=\"3\" fill=\"#8b5cf6\" />\r\n                      <circle cx=\"200\" cy=\"120\" r=\"3\" fill=\"#f59e0b\" />\r\n                      <circle cx=\"200\" cy=\"150\" r=\"3\" fill=\"#ef4444\" />\r\n                    </g>\r\n                  </svg>\r\n\r\n                  <div className=\"absolute inset-0 w-full h-full\">\r\n                    <div className=\"absolute top-3 left-1/2 transform -translate-x-1/2 animate-schema-pulse\">\r\n                      <div className=\"w-8 h-8 glass rounded-xl flex items-center justify-center border border-indigo-400/30 inner-glow\">\r\n                        <svg\r\n                          className=\"w-4 h-4 text-indigo-400\"\r\n                          fill=\"currentColor\"\r\n                          viewBox=\"0 0 20 20\"\r\n                        >\r\n                          <path d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\" />\r\n                        </svg>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"absolute left-3 top-12 table-float\">\r\n                      <div className=\"w-16 h-12 glass rounded-lg gradient-border shadow-lg overflow-hidden\">\r\n                        <div className=\"bg-gradient-to-r from-indigo-500/20 to-blue-500/20 text-[7px] px-1.5 py-0.5 font-medium border-b border-white/10\">\r\n                          users\r\n                        </div>\r\n                        <div className=\"px-1.5 py-0.5 space-y-0.5\">\r\n                          <div className=\"flex items-center space-x-0.5\">\r\n                            <div className=\"w-1 h-1 bg-yellow-400 rounded-full\"></div>\r\n                            <div className=\"h-0.5 w-6 bg-white/30 rounded\"></div>\r\n                          </div>\r\n                          <div className=\"h-0.5 w-4 bg-white/20 rounded\"></div>\r\n                          <div className=\"h-0.5 w-7 bg-white/20 rounded\"></div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"absolute right-3 top-12 table-float\">\r\n                      <div className=\"w-16 h-12 glass rounded-lg gradient-border shadow-lg overflow-hidden\">\r\n                        <div className=\"bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white text-[7px] px-1.5 py-0.5 font-medium border-b border-white/10\">\r\n                          orders\r\n                        </div>\r\n                        <div className=\"px-1.5 py-0.5 space-y-0.5\">\r\n                          <div className=\"flex items-center space-x-0.5\">\r\n                            <div className=\"w-1 h-1 bg-blue-400 rounded-full\"></div>\r\n                            <div className=\"h-0.5 w-6 bg-white/30 rounded\"></div>\r\n                          </div>\r\n                          <div className=\"h-0.5 w-3 bg-white/20 rounded\"></div>\r\n                          <div className=\"h-0.5 w-5 bg-white/20 rounded\"></div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"absolute left-1/2 transform -translate-x-1/2 top-24 table-float\">\r\n                      <div className=\"w-16 h-12 glass rounded-lg gradient-border shadow-lg overflow-hidden\">\r\n                        <div className=\"bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white text-[7px] px-1.5 py-0.5 font-medium border-b border-white/10\">\r\n                          products\r\n                        </div>\r\n                        <div className=\"px-1.5 py-0.5 space-y-0.5\">\r\n                          <div className=\"flex items-center space-x-0.5\">\r\n                            <div className=\"w-1 h-1 bg-purple-400 rounded-full\"></div>\r\n                            <div className=\"h-0.5 w-6 bg-white/30 rounded\"></div>\r\n                          </div>\r\n                          <div className=\"h-0.5 w-6 bg-white/20 rounded\"></div>\r\n                          <div className=\"h-0.5 w-4 bg-white/20 rounded\"></div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"absolute left-1/2 transform -translate-x-1/2 bottom-3 table-float\">\r\n                      <div className=\"w-16 h-12 glass rounded-lg gradient-border shadow-lg overflow-hidden\">\r\n                        <div className=\"bg-gradient-to-r from-orange-500/20 to-red-500/20 text-white text-[7px] px-1.5 py-0.5 font-medium border-b border-white/10\">\r\n                          analytics\r\n                        </div>\r\n                        <div className=\"px-1.5 py-0.5 space-y-0.5\">\r\n                          <div className=\"flex items-center space-x-0.5\">\r\n                            <div className=\"w-1 h-1 bg-orange-400 rounded-full\"></div>\r\n                            <div className=\"h-0.5 w-6 bg-white/30 rounded\"></div>\r\n                          </div>\r\n                          <div className=\"h-0.5 w-3 bg-white/20 rounded\"></div>\r\n                          <div className=\"h-0.5 w-5 bg-white/20 rounded\"></div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"glass-effect bg-gray-400/5 dark:bg-white/5 border border-border/50 rounded-2xl w-64 h-fit absolute -bottom-4 -left-4 transform -rotate-12 floating\">\r\n            <div className=\"p-4\">\r\n              <div className=\"flex justify-between items-center mb-3\">\r\n                <span className=\"text-white text-sm\">\r\n                  Simple Harmonic Motion\r\n                </span>\r\n                <Bot className=\"w-5 h-5 text-white/70\" />\r\n              </div>\r\n              <div className=\"flex-1 flex items-center justify-center\">\r\n                <svg\r\n                  viewBox=\"0 0 256 192\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  className=\"w-full h-full\"\r\n                >\r\n                  <defs>\r\n                    <linearGradient\r\n                      id=\"waveGradient\"\r\n                      x1=\"0%\"\r\n                      y1=\"0%\"\r\n                      x2=\"100%\"\r\n                      y2=\"0%\"\r\n                    >\r\n                      <stop offset=\"0%\" style={{ stopColor: \"#3b82f6\" }} />\r\n                      <stop offset=\"50%\" style={{ stopColor: \"#8b5cf6\" }} />\r\n                      <stop offset=\"100%\" style={{ stopColor: \"#ef4444\" }} />\r\n                    </linearGradient>\r\n                    <linearGradient\r\n                      id=\"pendulumGradient\"\r\n                      x1=\"0%\"\r\n                      y1=\"0%\"\r\n                      x2=\"0%\"\r\n                      y2=\"100%\"\r\n                    >\r\n                      <stop offset=\"0%\" style={{ stopColor: \"#6b7280\" }} />\r\n                      <stop offset=\"100%\" style={{ stopColor: \"#374151\" }} />\r\n                    </linearGradient>\r\n                    <radialGradient id=\"bobGradient\" cx=\"30%\" cy=\"30%\" r=\"70%\">\r\n                      <stop offset=\"0%\" style={{ stopColor: \"#fbbf24\" }} />\r\n                      <stop offset=\"70%\" style={{ stopColor: \"#f59e0b\" }} />\r\n                      <stop offset=\"100%\" style={{ stopColor: \"#d97706\" }} />\r\n                    </radialGradient>\r\n                    <radialGradient\r\n                      id=\"pivotGradient\"\r\n                      cx=\"50%\"\r\n                      cy=\"50%\"\r\n                      r=\"50%\"\r\n                    >\r\n                      <stop offset=\"0%\" style={{ stopColor: \"#e5e7eb\" }} />\r\n                      <stop offset=\"100%\" style={{ stopColor: \"#6b7280\" }} />\r\n                    </radialGradient>\r\n                  </defs>\r\n\r\n                  {/* Background */}\r\n                  <rect width=\"256\" height=\"192\" fill=\"transparent\" />\r\n\r\n                  {/* Title animation */}\r\n                  <text\r\n                    x=\"128\"\r\n                    y=\"15\"\r\n                    fontFamily=\"Arial, sans-serif\"\r\n                    fontSize=\"12\"\r\n                    fontWeight=\"bold\"\r\n                    fill=\"currentColor\"\r\n                    textAnchor=\"middle\"\r\n                    className=\"fill-gray-800 dark:fill-white\"\r\n                  >\r\n                    x = A cos(ωt + φ)\r\n                    <animate\r\n                      attributeName=\"opacity\"\r\n                      values=\"0;1;1;1\"\r\n                      dur=\"8s\"\r\n                      repeatCount=\"indefinite\"\r\n                    />\r\n                    <animateTransform\r\n                      attributeName=\"transform\"\r\n                      type=\"scale\"\r\n                      values=\"0.5;1;1;1\"\r\n                      dur=\"8s\"\r\n                      repeatCount=\"indefinite\"\r\n                    />\r\n                  </text>\r\n\r\n                  {/* Pendulum Section */}\r\n                  <g transform=\"translate(60, 50)\">\r\n                    {/* Pendulum pivot point */}\r\n                    <circle\r\n                      cx=\"0\"\r\n                      cy=\"0\"\r\n                      r=\"3\"\r\n                      fill=\"url(#pivotGradient)\"\r\n                      opacity=\"0\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;1;1\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                    </circle>\r\n\r\n                    {/* Pendulum string */}\r\n                    <line\r\n                      x1=\"0\"\r\n                      y1=\"0\"\r\n                      x2=\"0\"\r\n                      y2=\"50\"\r\n                      stroke=\"url(#pendulumGradient)\"\r\n                      strokeWidth=\"1.5\"\r\n                      opacity=\"0\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;1;1\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <animateTransform\r\n                        attributeName=\"transform\"\r\n                        type=\"rotate\"\r\n                        values=\"30;-30;30;-30;30\"\r\n                        dur=\"4s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"2s\"\r\n                      />\r\n                    </line>\r\n\r\n                    {/* Pendulum bob */}\r\n                    <circle r=\"4\" fill=\"url(#bobGradient)\" opacity=\"0\">\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;1;1\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <animateTransform\r\n                        attributeName=\"transform\"\r\n                        type=\"rotate\"\r\n                        values=\"30;-30;30;-30;30\"\r\n                        dur=\"4s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"2s\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"cx\"\r\n                        values=\"0;0;0;0;0\"\r\n                        dur=\"4s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"2s\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"cy\"\r\n                        values=\"50;50;50;50;50\"\r\n                        dur=\"4s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"2s\"\r\n                      />\r\n                    </circle>\r\n\r\n                    {/* Arc showing pendulum motion */}\r\n                    <path\r\n                      d=\"M -25 43 A 50 50 0 0 1 25 43\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"0.8\"\r\n                      fill=\"none\"\r\n                      strokeDasharray=\"3,2\"\r\n                      opacity=\"0\"\r\n                      className=\"stroke-gray-500 dark:stroke-gray-400\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;0.5;0.5\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                    </path>\r\n\r\n                    {/* Angle indicator */}\r\n                    <path\r\n                      d=\"M 0 0 L 12 0 A 12 12 0 0 0 10.4 6\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"1\"\r\n                      fill=\"none\"\r\n                      opacity=\"0\"\r\n                      className=\"stroke-blue-500\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;0.7;0.7\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                    </path>\r\n                    <text\r\n                      x=\"15\"\r\n                      y=\"8\"\r\n                      fontFamily=\"Arial, sans-serif\"\r\n                      fontSize=\"8\"\r\n                      fill=\"currentColor\"\r\n                      opacity=\"0\"\r\n                      className=\"fill-blue-600 dark:fill-blue-400\"\r\n                    >\r\n                      θ\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;1;1\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                    </text>\r\n                  </g>\r\n\r\n                  {/* Wave Section */}\r\n                  <g transform=\"translate(10, 120)\">\r\n                    {/* Time axis */}\r\n                    <line\r\n                      x1=\"0\"\r\n                      y1=\"0\"\r\n                      x2=\"200\"\r\n                      y2=\"0\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"1\"\r\n                      strokeDasharray=\"200\"\r\n                      strokeDashoffset=\"200\"\r\n                      className=\"stroke-gray-700 dark:stroke-white\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"stroke-dashoffset\"\r\n                        values=\"200;0;0;0\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                    </line>\r\n\r\n                    {/* Amplitude axis */}\r\n                    <line\r\n                      x1=\"0\"\r\n                      y1=\"-25\"\r\n                      x2=\"0\"\r\n                      y2=\"25\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"1\"\r\n                      strokeDasharray=\"50\"\r\n                      strokeDashoffset=\"50\"\r\n                      className=\"stroke-gray-700 dark:stroke-white\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"stroke-dashoffset\"\r\n                        values=\"50;0;0;0\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"0.5s\"\r\n                      />\r\n                    </line>\r\n\r\n                    {/* Amplitude markers */}\r\n                    <g\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"0.5\"\r\n                      opacity=\"0\"\r\n                      className=\"stroke-gray-500 dark:stroke-gray-400\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0.6;0.6\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <line x1=\"-3\" y1=\"-20\" x2=\"3\" y2=\"-20\" />\r\n                      <line x1=\"-3\" y1=\"20\" x2=\"3\" y2=\"20\" />\r\n                      <text\r\n                        x=\"-8\"\r\n                        y=\"-17\"\r\n                        fontFamily=\"Arial, sans-serif\"\r\n                        fontSize=\"7\"\r\n                        fill=\"currentColor\"\r\n                        className=\"fill-gray-600 dark:fill-gray-400\"\r\n                      >\r\n                        A\r\n                      </text>\r\n                      <text\r\n                        x=\"-8\"\r\n                        y=\"23\"\r\n                        fontFamily=\"Arial, sans-serif\"\r\n                        fontSize=\"7\"\r\n                        fill=\"currentColor\"\r\n                        className=\"fill-gray-600 dark:fill-gray-400\"\r\n                      >\r\n                        -A\r\n                      </text>\r\n                    </g>\r\n\r\n                    {/* Main sine wave */}\r\n                    <path\r\n                      d=\"M 0 0 Q 25 -20 50 0 Q 75 20 100 0 Q 125 -20 150 0 Q 175 20 200 0\"\r\n                      stroke=\"url(#waveGradient)\"\r\n                      strokeWidth=\"2.5\"\r\n                      fill=\"none\"\r\n                      strokeDasharray=\"300\"\r\n                      strokeDashoffset=\"300\"\r\n                      opacity=\"0\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;1;1\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"stroke-dashoffset\"\r\n                        values=\"300;0;0;0\"\r\n                        dur=\"2s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"3s\"\r\n                      />\r\n                    </path>\r\n\r\n                    {/* Moving point on wave */}\r\n                    <circle r=\"2.5\" fill=\"#fbbf24\" opacity=\"0\">\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;0;1;1\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"cx\"\r\n                        values=\"0;50;100;150;200;0\"\r\n                        dur=\"4s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"4s\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"cy\"\r\n                        values=\"0;0;0;0;0;0\"\r\n                        dur=\"4s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"4s\"\r\n                      />\r\n                      <animateTransform\r\n                        attributeName=\"transform\"\r\n                        type=\"translate\"\r\n                        values=\"0,0;0,-20;0,0;0,20;0,0;0,0\"\r\n                        dur=\"4s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"4s\"\r\n                      />\r\n                    </circle>\r\n\r\n                    {/* Connecting line from pendulum to wave */}\r\n                    <line\r\n                      x1=\"50\"\r\n                      y1=\"-70\"\r\n                      x2=\"50\"\r\n                      y2=\"-25\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"1\"\r\n                      strokeDasharray=\"3,2\"\r\n                      opacity=\"0\"\r\n                      className=\"stroke-purple-500 dark:stroke-purple-400\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;0;0.5;0.5\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                    </line>\r\n\r\n                    {/* Phase indicators */}\r\n                    <g opacity=\"0\">\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;0;0.7;0.7\"\r\n                        dur=\"8s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <text\r\n                        x=\"25\"\r\n                        y=\"-30\"\r\n                        fontFamily=\"Arial, sans-serif\"\r\n                        fontSize=\"7\"\r\n                        fill=\"currentColor\"\r\n                        className=\"fill-blue-600 dark:fill-blue-400\"\r\n                      >\r\n                        π/2\r\n                      </text>\r\n                      <text\r\n                        x=\"75\"\r\n                        y=\"35\"\r\n                        fontFamily=\"Arial, sans-serif\"\r\n                        fontSize=\"7\"\r\n                        fill=\"currentColor\"\r\n                        className=\"fill-red-600 dark:fill-red-400\"\r\n                      >\r\n                        3π/2\r\n                      </text>\r\n                      <text\r\n                        x=\"125\"\r\n                        y=\"-30\"\r\n                        fontFamily=\"Arial, sans-serif\"\r\n                        fontSize=\"7\"\r\n                        fill=\"currentColor\"\r\n                        className=\"fill-blue-600 dark:fill-blue-400\"\r\n                      >\r\n                        5π/2\r\n                      </text>\r\n                    </g>\r\n                  </g>\r\n\r\n                  {/* Frequency display */}\r\n                  <g transform=\"translate(180, 45)\" opacity=\"0\">\r\n                    <animate\r\n                      attributeName=\"opacity\"\r\n                      values=\"0;0;0;0;1;1\"\r\n                      dur=\"8s\"\r\n                      repeatCount=\"indefinite\"\r\n                    />\r\n                    <rect\r\n                      x=\"-20\"\r\n                      y=\"-8\"\r\n                      width=\"40\"\r\n                      height=\"16\"\r\n                      rx=\"8\"\r\n                      fill=\"#10b981\"\r\n                      opacity=\"0.9\"\r\n                    />\r\n                    <text\r\n                      x=\"0\"\r\n                      y=\"3\"\r\n                      fontFamily=\"Arial, sans-serif\"\r\n                      fontSize=\"8\"\r\n                      fontWeight=\"bold\"\r\n                      fill=\"white\"\r\n                      textAnchor=\"middle\"\r\n                    >\r\n                      f ={\" \"}\r\n                      <tspan>\r\n                        <animate\r\n                          values=\"0.25;0.25;0.25;0.25;0.25\"\r\n                          dur=\"4s\"\r\n                          repeatCount=\"indefinite\"\r\n                          begin=\"4s\"\r\n                        />\r\n                        0.25\r\n                      </tspan>{\" \"}\r\n                      Hz\r\n                    </text>\r\n                  </g>\r\n\r\n                  {/* Energy conservation indicator */}\r\n                  <g transform=\"translate(20, 170)\" opacity=\"0\">\r\n                    <animate\r\n                      attributeName=\"opacity\"\r\n                      values=\"0;0;0;0;1;1\"\r\n                      dur=\"8s\"\r\n                      repeatCount=\"indefinite\"\r\n                    />\r\n                    <text\r\n                      x=\"0\"\r\n                      y=\"0\"\r\n                      fontFamily=\"Arial, sans-serif\"\r\n                      fontSize=\"9\"\r\n                      fill=\"currentColor\"\r\n                      className=\"fill-gray-700 dark:fill-gray-300\"\r\n                    >\r\n                      E = ½kA² = constant\r\n                    </text>\r\n                  </g>\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"glass-effect bg-gray-400/5 dark:bg-white/5 border border-border/50 rounded-2xl w-48 h-48 absolute top-0 right-0 transform rotate-12 floating\">\r\n            <div className=\"p-4 h-full flex flex-col\">\r\n              <div className=\"text-sm mb-2\">High Definition Maths</div>\r\n              <div className=\"flex-1 flex items-center justify-center\">\r\n                {/* SVG */}\r\n                <svg\r\n                  viewBox=\"0 0 256 192\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  className=\"w-full h-full\"\r\n                >\r\n                  <defs>\r\n                    <linearGradient\r\n                      id=\"curve\"\r\n                      x1=\"0%\"\r\n                      y1=\"0%\"\r\n                      x2=\"100%\"\r\n                      y2=\"0%\"\r\n                    >\r\n                      <stop offset=\"0%\" style={{ stopColor: \"#6366f1\" }} />\r\n                      <stop offset=\"100%\" style={{ stopColor: \"#8b5cf6\" }} />\r\n                    </linearGradient>\r\n                    <linearGradient\r\n                      id=\"tangent\"\r\n                      x1=\"0%\"\r\n                      y1=\"0%\"\r\n                      x2=\"100%\"\r\n                      y2=\"0%\"\r\n                    >\r\n                      <stop offset=\"0%\" style={{ stopColor: \"#f59e0b\" }} />\r\n                      <stop offset=\"100%\" style={{ stopColor: \"#ef4444\" }} />\r\n                    </linearGradient>\r\n                    <radialGradient id=\"point\" cx=\"50%\" cy=\"50%\" r=\"50%\">\r\n                      <stop offset=\"0%\" style={{ stopColor: \"#ffffff\" }} />\r\n                      <stop offset=\"70%\" style={{ stopColor: \"#6366f1\" }} />\r\n                    </radialGradient>\r\n                    <radialGradient id=\"pointLight\" cx=\"50%\" cy=\"50%\" r=\"50%\">\r\n                      <stop offset=\"0%\" style={{ stopColor: \"#1f2937\" }} />\r\n                      <stop offset=\"70%\" style={{ stopColor: \"#6366f1\" }} />\r\n                    </radialGradient>\r\n                  </defs>\r\n\r\n                  {/* Background */}\r\n                  <rect width=\"256\" height=\"192\" fill=\"transparent\" />\r\n\r\n                  {/* Title animation */}\r\n                  <text\r\n                    x=\"128\"\r\n                    y=\"20\"\r\n                    fontFamily=\"Arial, sans-serif\"\r\n                    fontSize=\"14\"\r\n                    fontWeight=\"bold\"\r\n                    fill=\"currentColor\"\r\n                    textAnchor=\"middle\"\r\n                    className=\"fill-gray-800 dark:fill-white\"\r\n                  >\r\n                    dy/dx = x\r\n                    <animate\r\n                      attributeName=\"opacity\"\r\n                      values=\"0;1;1;1\"\r\n                      dur=\"6s\"\r\n                      repeatCount=\"indefinite\"\r\n                    />\r\n                    <animateTransform\r\n                      attributeName=\"transform\"\r\n                      type=\"scale\"\r\n                      values=\"0.5;1;1;1\"\r\n                      dur=\"6s\"\r\n                      repeatCount=\"indefinite\"\r\n                    />\r\n                  </text>\r\n\r\n                  {/* Coordinate system */}\r\n                  <g transform=\"translate(128, 96)\">\r\n                    {/* Axes with draw-on animation */}\r\n                    <line\r\n                      x1=\"-80\"\r\n                      y1=\"0\"\r\n                      x2=\"80\"\r\n                      y2=\"0\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"1.5\"\r\n                      strokeDasharray=\"160\"\r\n                      strokeDashoffset=\"160\"\r\n                      className=\"stroke-gray-700 dark:stroke-white\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"stroke-dashoffset\"\r\n                        values=\"160;0;0;0\"\r\n                        dur=\"6s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                    </line>\r\n                    <line\r\n                      x1=\"0\"\r\n                      y1=\"60\"\r\n                      x2=\"0\"\r\n                      y2=\"-60\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"1.5\"\r\n                      strokeDasharray=\"120\"\r\n                      strokeDashoffset=\"120\"\r\n                      className=\"stroke-gray-700 dark:stroke-white\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"stroke-dashoffset\"\r\n                        values=\"120;0;0;0\"\r\n                        dur=\"6s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"0.25s\"\r\n                      />\r\n                    </line>\r\n\r\n                    {/* Grid lines appearing */}\r\n                    <g\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"0.5\"\r\n                      className=\"stroke-gray-400 dark:stroke-white\"\r\n                    >\r\n                      <line x1=\"-40\" y1=\"-60\" x2=\"-40\" y2=\"60\">\r\n                        <animate\r\n                          attributeName=\"opacity\"\r\n                          values=\"0;0;0.3;0.3\"\r\n                          dur=\"6s\"\r\n                          repeatCount=\"indefinite\"\r\n                        />\r\n                      </line>\r\n                      <line x1=\"40\" y1=\"-60\" x2=\"40\" y2=\"60\">\r\n                        <animate\r\n                          attributeName=\"opacity\"\r\n                          values=\"0;0;0.3;0.3\"\r\n                          dur=\"6s\"\r\n                          repeatCount=\"indefinite\"\r\n                        />\r\n                      </line>\r\n                      <line x1=\"-80\" y1=\"-30\" x2=\"80\" y2=\"-30\">\r\n                        <animate\r\n                          attributeName=\"opacity\"\r\n                          values=\"0;0;0.3;0.3\"\r\n                          dur=\"6s\"\r\n                          repeatCount=\"indefinite\"\r\n                        />\r\n                      </line>\r\n                      <line x1=\"-80\" y1=\"30\" x2=\"80\" y2=\"30\">\r\n                        <animate\r\n                          attributeName=\"opacity\"\r\n                          values=\"0;0;0.3;0.3\"\r\n                          dur=\"6s\"\r\n                          repeatCount=\"indefinite\"\r\n                        />\r\n                      </line>\r\n                    </g>\r\n\r\n                    {/* Slope field appearing sequentially */}\r\n                    <g\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"1\"\r\n                      className=\"stroke-gray-500 dark:stroke-slate-400\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0.6;0.6\"\r\n                        dur=\"6s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <line x1=\"-60\" y1=\"-40\" x2=\"-50\" y2=\"-46\" />\r\n                      <line x1=\"-20\" y1=\"-40\" x2=\"-10\" y2=\"-42\" />\r\n                      <line x1=\"20\" y1=\"-40\" x2=\"30\" y2=\"-42\" />\r\n                      <line x1=\"60\" y1=\"-40\" x2=\"70\" y2=\"-46\" />\r\n                      <line x1=\"-60\" y1=\"0\" x2=\"-50\" y2=\"-6\" />\r\n                      <line x1=\"-20\" y1=\"0\" x2=\"-10\" y2=\"-2\" />\r\n                      <line x1=\"20\" y1=\"0\" x2=\"30\" y2=\"2\" />\r\n                      <line x1=\"60\" y1=\"0\" x2=\"70\" y2=\"6\" />\r\n                      <line x1=\"-60\" y1=\"40\" x2=\"-50\" y2=\"34\" />\r\n                      <line x1=\"-20\" y1=\"40\" x2=\"-10\" y2=\"38\" />\r\n                      <line x1=\"20\" y1=\"40\" x2=\"30\" y2=\"42\" />\r\n                      <line x1=\"60\" y1=\"40\" x2=\"70\" y2=\"46\" />\r\n                    </g>\r\n\r\n                    {/* Solution curves appearing */}\r\n                    <g opacity=\"0\">\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;0.4;0.4\"\r\n                        dur=\"6s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <path\r\n                        d=\"M -70 24.5 Q -40 8 -20 2 Q 0 0 20 2 Q 40 8 70 24.5\"\r\n                        stroke=\"#8b5cf6\"\r\n                        strokeWidth=\"1.5\"\r\n                        fill=\"none\"\r\n                        strokeDasharray=\"100\"\r\n                        strokeDashoffset=\"100\"\r\n                      >\r\n                        <animate\r\n                          attributeName=\"stroke-dashoffset\"\r\n                          values=\"100;0;0\"\r\n                          dur=\"1s\"\r\n                          repeatCount=\"indefinite\"\r\n                          begin=\"3s\"\r\n                        />\r\n                      </path>\r\n                      <path\r\n                        d=\"M -70 44.5 Q -40 28 -20 22 Q 0 20 20 22 Q 40 28 70 44.5\"\r\n                        stroke=\"#8b5cf6\"\r\n                        strokeWidth=\"1.5\"\r\n                        fill=\"none\"\r\n                        strokeDasharray=\"100\"\r\n                        strokeDashoffset=\"100\"\r\n                      >\r\n                        <animate\r\n                          attributeName=\"stroke-dashoffset\"\r\n                          values=\"100;0;0\"\r\n                          dur=\"1s\"\r\n                          repeatCount=\"indefinite\"\r\n                          begin=\"3.25s\"\r\n                        />\r\n                      </path>\r\n                    </g>\r\n\r\n                    {/* Main curve with draw-on animation */}\r\n                    <path\r\n                      id=\"mainCurve\"\r\n                      d=\"M -70 4.5 Q -40 -12 -20 -18 Q 0 -20 20 -18 Q 40 -12 70 4.5\"\r\n                      stroke=\"url(#curve)\"\r\n                      strokeWidth=\"2.5\"\r\n                      fill=\"none\"\r\n                      strokeDasharray=\"120\"\r\n                      strokeDashoffset=\"120\"\r\n                      opacity=\"0\"\r\n                    >\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;1;1\"\r\n                        dur=\"6s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"stroke-dashoffset\"\r\n                        values=\"120;0;0;0\"\r\n                        dur=\"1.5s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"3.5s\"\r\n                      />\r\n                    </path>\r\n\r\n                    {/* Animated point appearing after curve */}\r\n                    <circle r=\"3\" opacity=\"0\">\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;0;1;1\"\r\n                        dur=\"6s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"fill\"\r\n                        values=\"url(#pointLight);url(#point)\"\r\n                        dur=\"0.1s\"\r\n                        repeatCount=\"1\"\r\n                      />\r\n                      <animateMotion\r\n                        dur=\"2s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"4s\"\r\n                      >\r\n                        <mpath href=\"#mainCurve\" />\r\n                      </animateMotion>\r\n                    </circle>\r\n\r\n                    {/* Tangent line animation */}\r\n                    <line stroke=\"url(#tangent)\" strokeWidth=\"2\" opacity=\"0\">\r\n                      <animate\r\n                        attributeName=\"opacity\"\r\n                        values=\"0;0;0;0;0.8;0.8\"\r\n                        dur=\"6s\"\r\n                        repeatCount=\"indefinite\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"x1\"\r\n                        values=\"-80;-55;-30;-5;20;45;70;-80\"\r\n                        dur=\"2s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"4s\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"y1\"\r\n                        values=\"16;-4;-14;-18;-14;-4;16;16\"\r\n                        dur=\"2s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"4s\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"x2\"\r\n                        values=\"-60;-35;-10;15;40;65;90;-60\"\r\n                        dur=\"2s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"4s\"\r\n                      />\r\n                      <animate\r\n                        attributeName=\"y2\"\r\n                        values=\"-4;-24;-34;-38;-34;-24;-4;-4\"\r\n                        dur=\"2s\"\r\n                        repeatCount=\"indefinite\"\r\n                        begin=\"4s\"\r\n                      />\r\n                    </line>\r\n                  </g>\r\n\r\n                  {/* Slope value display */}\r\n                  <g transform=\"translate(200, 170)\" opacity=\"0\">\r\n                    <animate\r\n                      attributeName=\"opacity\"\r\n                      values=\"0;0;0;0;1;1\"\r\n                      dur=\"6s\"\r\n                      repeatCount=\"indefinite\"\r\n                    />\r\n                    <rect\r\n                      x=\"-25\"\r\n                      y=\"-8\"\r\n                      width=\"50\"\r\n                      height=\"16\"\r\n                      rx=\"8\"\r\n                      fill=\"#3b82f6\"\r\n                    />\r\n                    <text\r\n                      x=\"0\"\r\n                      y=\"3\"\r\n                      fontFamily=\"Arial, sans-serif\"\r\n                      fontSize=\"10\"\r\n                      fontWeight=\"bold\"\r\n                      fill=\"white\"\r\n                      textAnchor=\"middle\"\r\n                    >\r\n                      m ={\" \"}\r\n                      <tspan>\r\n                        <animate\r\n                          values=\"-1.8;-1.4;-1;-0.6;-0.2;0.2;0.6;1;1.4;1.8;-1.8\"\r\n                          dur=\"2s\"\r\n                          repeatCount=\"indefinite\"\r\n                          begin=\"4s\"\r\n                        />\r\n                        -1.8\r\n                      </tspan>\r\n                    </text>\r\n                  </g>\r\n\r\n                  {/* Solution equation reveal */}\r\n                  <text\r\n                    x=\"30\"\r\n                    y=\"180\"\r\n                    fontFamily=\"Arial, sans-serif\"\r\n                    fontSize=\"12\"\r\n                    fill=\"currentColor\"\r\n                    opacity=\"0\"\r\n                    className=\"fill-gray-800 dark:fill-white\"\r\n                  >\r\n                    y = x²/2 + C\r\n                    <animate\r\n                      attributeName=\"opacity\"\r\n                      values=\"0;0;0;0;1;1\"\r\n                      dur=\"6s\"\r\n                      repeatCount=\"indefinite\"\r\n                    />\r\n                    <animateTransform\r\n                      attributeName=\"transform\"\r\n                      type=\"scale\"\r\n                      values=\"0.8;1;1\"\r\n                      dur=\"0.5s\"\r\n                      repeatCount=\"indefinite\"\r\n                      begin=\"4.5s\"\r\n                    />\r\n                  </text>\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default NeuralAI;\r\n"], "names": [], "mappings": ";;;;AAEA;;AAIA;AACA;AAAA;AAPA;;;;;;AASA,MAAM,aAAa,CAAC;;;;;;;CAOnB,CAAC;AAEF,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoDnB,CAAC;AAEF,MAAM,WAAW;IACf,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA4B;IACnD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;QAAG,IAAI;QAAG,IAAI;IAAE;IAClD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO,CAAC;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QACb,MAAM,KAAM,OAAO,UAAU,CAAC,YAC5B,OAAO,UAAU,CAAC;QACpB,IAAI,CAAC,IAAI;YACP,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,MAAM,mBAAmB,KAAK,GAAG,CAAC,OAAO,gBAAgB,EAAE;QAE3D,SAAS,aACP,EAAyB,EACzB,MAAc,EACd,IAAY;YAEZ,MAAM,SAAS,GAAG,YAAY,CAAC;YAC/B,GAAG,YAAY,CAAC,QAAQ;YACxB,GAAG,aAAa,CAAC;YACjB,IAAI,CAAC,GAAG,kBAAkB,CAAC,QAAQ,GAAG,cAAc,GAAG;gBACrD,QAAQ,KAAK,CAAC,mBAAmB,GAAG,gBAAgB,CAAC;gBACrD,OAAO;YACT;YACA,OAAO;QACT;QAEA,MAAM,eAAe,aAAa,IAAI,YAAY,GAAG,aAAa;QAClE,MAAM,iBAAiB,aAAa,IAAI,YAAY,GAAG,eAAe;QAEtE,MAAM,UAAU,GAAG,aAAa;QAChC,GAAG,YAAY,CAAC,SAAS;QACzB,GAAG,YAAY,CAAC,SAAS;QACzB,GAAG,WAAW,CAAC;QAEf,IAAI,CAAC,GAAG,mBAAmB,CAAC,SAAS,GAAG,WAAW,GAAG;YACpD,QAAQ,KAAK,CAAC,wBAAwB,GAAG,iBAAiB,CAAC;YAC3D;QACF;QAEA,MAAM,IAAS,CAAC;QAChB,MAAM,eAAe,GAAG,mBAAmB,CAAC,SAAS,GAAG,eAAe;QACvE,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACrC,MAAM,cAAc,GAAG,gBAAgB,CAAC,SAAS,IAAI;YACrD,IAAI,aAAa;gBACf,CAAC,CAAC,YAAY,GAAG,GAAG,kBAAkB,CAAC,SAAS;YAClD;QACF;QACA,SAAS,OAAO,GAAG;QAEnB,MAAM,WAAW,IAAI,aAAa;YAAC,CAAC;YAAG,CAAC;YAAG;YAAG,CAAC;YAAG,CAAC;YAAG;YAAG;YAAG;SAAE;QAC9D,MAAM,eAAe,GAAG,YAAY;QACpC,GAAG,UAAU,CAAC,GAAG,YAAY,EAAE;QAC/B,GAAG,UAAU,CAAC,GAAG,YAAY,EAAE,UAAU,GAAG,WAAW;QAEvD,GAAG,UAAU,CAAC;QACd,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,SAAS;QACvD,GAAG,uBAAuB,CAAC;QAC3B,GAAG,mBAAmB,CAAC,kBAAkB,GAAG,GAAG,KAAK,EAAE,OAAO,GAAG;QAEhE,SAAS;YACP,IAAI,CAAC,UAAU,CAAC,IAAI;YACpB,OAAO,KAAK,GAAG,OAAO,UAAU,GAAG;YACnC,OAAO,MAAM,GAAG,OAAO,WAAW,GAAG;YACrC,GAAG,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAC7C,GAAG,SAAS,CAAC,SAAS,OAAO,CAAC,OAAO,EAAE,OAAO,KAAK,GAAG,OAAO,MAAM;QACrE;QAEA,SAAS,YAAY,CAAS,EAAE,CAAS;YACvC,QAAQ,OAAO,CAAC,EAAE,GAAG;YACrB,QAAQ,OAAO,CAAC,EAAE,GAAG;QACvB;QAEA,SAAS;YACP,IAAI,CAAC,IAAI;YACT,MAAM,OAAO,YAAY,GAAG;YAC5B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,QAAQ,OAAO;YACxC,QAAQ,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;YAChC,QAAQ,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;YAEhC,GAAG,SAAS,CAAC,SAAS,OAAO,CAAC,MAAM,EAAE;YACtC,GAAG,SAAS,CACV,SAAS,OAAO,CAAC,kBAAkB,EACnC,QAAQ,OAAO,CAAC,CAAC,GAAG,OAAO,UAAU,EACrC,IAAI,QAAQ,OAAO,CAAC,CAAC,GAAG,OAAO,WAAW;YAE5C,GAAG,SAAS,CACV,SAAS,OAAO,CAAC,iBAAiB,EAClC,OAAO,OAAO,GAAG,CAAC,IAAI,OAAO,WAAW;YAE1C,GAAG,UAAU,CAAC,GAAG,cAAc,EAAE,GAAG;YACpC,sBAAsB;QACxB;QAEA,OAAO,gBAAgB,CAAC,eAAe,CAAC,IACtC,YAAY,EAAE,OAAO,EAAE,EAAE,OAAO;QAElC,OAAO,gBAAgB,CAAC,aAAa,CAAC,IACpC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;QAExD,OAAO,gBAAgB,CAAC,SAAS,CAAC,IAAM,YAAY,EAAE,OAAO,EAAE,EAAE,OAAO;QACxE,OAAO,gBAAgB,CAAC,UAAU;QAElC;QACA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBACC,KAAK;gBACL,WAAU;;;;;;0BAEZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAK,WAAU;;;;;;wCAA8C;;;;;;;;;;;;0CAKlE,8OAAC;gCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yEACA,uJAAA,CAAA,UAAK,CAAC,SAAS;;oCAElB;kDAEC,8OAAC;;;;;oCAAK;;;;;;;0CAIR,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAMvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAI;gDACJ,WAAU;gDACV,KAAI;;;;;;0DAEN,8OAAC;gDACC,KAAI;gDACJ,WAAU;gDACV,KAAI;;;;;;0DAEN,8OAAC;gDACC,KAAI;gDACJ,WAAU;gDACV,KAAI;;;;;;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;;4CAAwB;0DAC1B,8OAAC;gDAAK,WAAU;0DAAyB;;;;;;4CAAe;4CAAI;;;;;;;;;;;;;;;;;;;kCAM7E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,iBACE;gEACF,gBAAgB;4DAElB;;;;;;;;;;;kEAIJ,8OAAC;wDACC,WAAU;wDACV,SAAQ;;0EAER,8OAAC;0EACC,cAAA,8OAAC;oEACC,IAAG;oEACH,IAAG;oEACH,IAAG;oEACH,IAAG;oEACH,IAAG;;sFAEH,8OAAC;4EACC,QAAO;4EACP,OAAO;gFACL,WAAW;gFACX,aAAa;4EACf;;;;;;sFAEF,8OAAC;4EACC,QAAO;4EACP,OAAO;gFAAE,WAAW;gFAAW,aAAa;4EAAE;;;;;;sFAEhD,8OAAC;4EACC,QAAO;4EACP,OAAO;gFACL,WAAW;gFACX,aAAa;4EACf;;;;;;;;;;;;;;;;;0EAKN,8OAAC;gEACC,QAAO;gEACP,aAAY;gEACZ,MAAK;;kFAEL,8OAAC;wEACC,WAAU;wEACV,GAAE;;;;;;kFAEJ,8OAAC;wEACC,WAAU;wEACV,GAAE;;;;;;kFAEJ,8OAAC;wEACC,WAAU;wEACV,GAAE;;;;;;kFAEJ,8OAAC;wEACC,WAAU;wEACV,GAAE;;;;;;kFAGJ,8OAAC;wEAAO,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAI,MAAK;;;;;;kFACnC,8OAAC;wEAAO,IAAG;wEAAM,IAAG;wEAAK,GAAE;wEAAI,MAAK;;;;;;kFACpC,8OAAC;wEAAO,IAAG;wEAAM,IAAG;wEAAK,GAAE;wEAAI,MAAK;;;;;;kFACpC,8OAAC;wEAAO,IAAG;wEAAM,IAAG;wEAAM,GAAE;wEAAI,MAAK;;;;;;kFACrC,8OAAC;wEAAO,IAAG;wEAAM,IAAG;wEAAM,GAAE;wEAAI,MAAK;;;;;;;;;;;;;;;;;;kEAIzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,WAAU;wEACV,MAAK;wEACL,SAAQ;kFAER,cAAA,8OAAC;4EAAK,GAAE;;;;;;;;;;;;;;;;;;;;;0EAKd,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAmH;;;;;;sFAGlI,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;;;;;;sGACf,8OAAC;4FAAI,WAAU;;;;;;;;;;;;8FAEjB,8OAAC;oFAAI,WAAU;;;;;;8FACf,8OAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0EAKrB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA8H;;;;;;sFAG7I,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;;;;;;sGACf,8OAAC;4FAAI,WAAU;;;;;;;;;;;;8FAEjB,8OAAC;oFAAI,WAAU;;;;;;8FACf,8OAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0EAKrB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA8H;;;;;;sFAG7I,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;;;;;;sGACf,8OAAC;4FAAI,WAAU;;;;;;;;;;;;8FAEjB,8OAAC;oFAAI,WAAU;;;;;;8FACf,8OAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0EAKrB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA6H;;;;;;sFAG5I,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;;;;;;sGACf,8OAAC;4FAAI,WAAU;;;;;;;;;;;;8FAEjB,8OAAC;oFAAI,WAAU;;;;;;8FACf,8OAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU/B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;8DAGrC,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,SAAQ;gDACR,OAAM;gDACN,WAAU;;kEAEV,8OAAC;;0EACC,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;;kFAEH,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAM,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFACjD,8OAAC;wEAAK,QAAO;wEAAO,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;0EAEpD,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;;kFAEH,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAO,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;0EAEpD,8OAAC;gEAAe,IAAG;gEAAc,IAAG;gEAAM,IAAG;gEAAM,GAAE;;kFACnD,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAM,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFACjD,8OAAC;wEAAK,QAAO;wEAAO,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;0EAEpD,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,GAAE;;kFAEF,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAO,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;;;;;;;kEAKtD,8OAAC;wDAAK,OAAM;wDAAM,QAAO;wDAAM,MAAK;;;;;;kEAGpC,8OAAC;wDACC,GAAE;wDACF,GAAE;wDACF,YAAW;wDACX,UAAS;wDACT,YAAW;wDACX,MAAK;wDACL,YAAW;wDACX,WAAU;;4DACX;0EAEC,8OAAC;gEACC,eAAc;gEACd,QAAO;gEACP,KAAI;gEACJ,aAAY;;;;;;0EAEd,8OAAC;gEACC,eAAc;gEACd,MAAK;gEACL,QAAO;gEACP,KAAI;gEACJ,aAAY;;;;;;;;;;;;kEAKhB,8OAAC;wDAAE,WAAU;;0EAEX,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,GAAE;gEACF,MAAK;gEACL,SAAQ;0EAER,cAAA,8OAAC;oEACC,eAAc;oEACd,QAAO;oEACP,KAAI;oEACJ,aAAY;;;;;;;;;;;0EAKhB,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,QAAO;gEACP,aAAY;gEACZ,SAAQ;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,eAAc;wEACd,MAAK;wEACL,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;;;;;;;0EAKV,8OAAC;gEAAO,GAAE;gEAAI,MAAK;gEAAoB,SAAQ;;kFAC7C,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,eAAc;wEACd,MAAK;wEACL,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;;;;;;;0EAKV,8OAAC;gEACC,GAAE;gEACF,QAAO;gEACP,aAAY;gEACZ,MAAK;gEACL,iBAAgB;gEAChB,SAAQ;gEACR,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,QAAO;oEACP,KAAI;oEACJ,aAAY;;;;;;;;;;;0EAKhB,8OAAC;gEACC,GAAE;gEACF,QAAO;gEACP,aAAY;gEACZ,MAAK;gEACL,SAAQ;gEACR,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,QAAO;oEACP,KAAI;oEACJ,aAAY;;;;;;;;;;;0EAGhB,8OAAC;gEACC,GAAE;gEACF,GAAE;gEACF,YAAW;gEACX,UAAS;gEACT,MAAK;gEACL,SAAQ;gEACR,WAAU;;oEACX;kFAEC,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;;;;;;;;;;;;;kEAMlB,8OAAC;wDAAE,WAAU;;0EAEX,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,QAAO;gEACP,aAAY;gEACZ,iBAAgB;gEAChB,kBAAiB;gEACjB,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,QAAO;oEACP,KAAI;oEACJ,aAAY;;;;;;;;;;;0EAKhB,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,QAAO;gEACP,aAAY;gEACZ,iBAAgB;gEAChB,kBAAiB;gEACjB,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,QAAO;oEACP,KAAI;oEACJ,aAAY;oEACZ,OAAM;;;;;;;;;;;0EAKV,8OAAC;gEACC,QAAO;gEACP,aAAY;gEACZ,SAAQ;gEACR,WAAU;;kFAEV,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAM,IAAG;wEAAI,IAAG;;;;;;kFACjC,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAK,IAAG;wEAAI,IAAG;;;;;;kFAChC,8OAAC;wEACC,GAAE;wEACF,GAAE;wEACF,YAAW;wEACX,UAAS;wEACT,MAAK;wEACL,WAAU;kFACX;;;;;;kFAGD,8OAAC;wEACC,GAAE;wEACF,GAAE;wEACF,YAAW;wEACX,UAAS;wEACT,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;0EAMH,8OAAC;gEACC,GAAE;gEACF,QAAO;gEACP,aAAY;gEACZ,MAAK;gEACL,iBAAgB;gEAChB,kBAAiB;gEACjB,SAAQ;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;;;;;;;0EAKV,8OAAC;gEAAO,GAAE;gEAAM,MAAK;gEAAU,SAAQ;;kFACrC,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;kFAER,8OAAC;wEACC,eAAc;wEACd,MAAK;wEACL,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;;;;;;;0EAKV,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,QAAO;gEACP,aAAY;gEACZ,iBAAgB;gEAChB,SAAQ;gEACR,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,QAAO;oEACP,KAAI;oEACJ,aAAY;;;;;;;;;;;0EAKhB,8OAAC;gEAAE,SAAQ;;kFACT,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,GAAE;wEACF,GAAE;wEACF,YAAW;wEACX,UAAS;wEACT,MAAK;wEACL,WAAU;kFACX;;;;;;kFAGD,8OAAC;wEACC,GAAE;wEACF,GAAE;wEACF,YAAW;wEACX,UAAS;wEACT,MAAK;wEACL,WAAU;kFACX;;;;;;kFAGD,8OAAC;wEACC,GAAE;wEACF,GAAE;wEACF,YAAW;wEACX,UAAS;wEACT,MAAK;wEACL,WAAU;kFACX;;;;;;;;;;;;;;;;;;kEAOL,8OAAC;wDAAE,WAAU;wDAAqB,SAAQ;;0EACxC,8OAAC;gEACC,eAAc;gEACd,QAAO;gEACP,KAAI;gEACJ,aAAY;;;;;;0EAEd,8OAAC;gEACC,GAAE;gEACF,GAAE;gEACF,OAAM;gEACN,QAAO;gEACP,IAAG;gEACH,MAAK;gEACL,SAAQ;;;;;;0EAEV,8OAAC;gEACC,GAAE;gEACF,GAAE;gEACF,YAAW;gEACX,UAAS;gEACT,YAAW;gEACX,MAAK;gEACL,YAAW;;oEACZ;oEACK;kFACJ,8OAAC;;0FACC,8OAAC;gFACC,QAAO;gFACP,KAAI;gFACJ,aAAY;gFACZ,OAAM;;;;;;4EACN;;;;;;;oEAEK;oEAAI;;;;;;;;;;;;;kEAMjB,8OAAC;wDAAE,WAAU;wDAAqB,SAAQ;;0EACxC,8OAAC;gEACC,eAAc;gEACd,QAAO;gEACP,KAAI;gEACJ,aAAY;;;;;;0EAEd,8OAAC;gEACC,GAAE;gEACF,GAAE;gEACF,YAAW;gEACX,UAAS;gEACT,MAAK;gEACL,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAe;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;sDAEb,cAAA,8OAAC;gDACC,SAAQ;gDACR,OAAM;gDACN,WAAU;;kEAEV,8OAAC;;0EACC,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;;kFAEH,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAO,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;0EAEpD,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;;kFAEH,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAO,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;0EAEpD,8OAAC;gEAAe,IAAG;gEAAQ,IAAG;gEAAM,IAAG;gEAAM,GAAE;;kFAC7C,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAM,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;0EAEnD,8OAAC;gEAAe,IAAG;gEAAa,IAAG;gEAAM,IAAG;gEAAM,GAAE;;kFAClD,8OAAC;wEAAK,QAAO;wEAAK,OAAO;4EAAE,WAAW;wEAAU;;;;;;kFAChD,8OAAC;wEAAK,QAAO;wEAAM,OAAO;4EAAE,WAAW;wEAAU;;;;;;;;;;;;;;;;;;kEAKrD,8OAAC;wDAAK,OAAM;wDAAM,QAAO;wDAAM,MAAK;;;;;;kEAGpC,8OAAC;wDACC,GAAE;wDACF,GAAE;wDACF,YAAW;wDACX,UAAS;wDACT,YAAW;wDACX,MAAK;wDACL,YAAW;wDACX,WAAU;;4DACX;0EAEC,8OAAC;gEACC,eAAc;gEACd,QAAO;gEACP,KAAI;gEACJ,aAAY;;;;;;0EAEd,8OAAC;gEACC,eAAc;gEACd,MAAK;gEACL,QAAO;gEACP,KAAI;gEACJ,aAAY;;;;;;;;;;;;kEAKhB,8OAAC;wDAAE,WAAU;;0EAEX,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,QAAO;gEACP,aAAY;gEACZ,iBAAgB;gEAChB,kBAAiB;gEACjB,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,QAAO;oEACP,KAAI;oEACJ,aAAY;;;;;;;;;;;0EAGhB,8OAAC;gEACC,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,IAAG;gEACH,QAAO;gEACP,aAAY;gEACZ,iBAAgB;gEAChB,kBAAiB;gEACjB,WAAU;0EAEV,cAAA,8OAAC;oEACC,eAAc;oEACd,QAAO;oEACP,KAAI;oEACJ,aAAY;oEACZ,OAAM;;;;;;;;;;;0EAKV,8OAAC;gEACC,QAAO;gEACP,aAAY;gEACZ,WAAU;;kFAEV,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAM,IAAG;wEAAM,IAAG;kFAClC,cAAA,8OAAC;4EACC,eAAc;4EACd,QAAO;4EACP,KAAI;4EACJ,aAAY;;;;;;;;;;;kFAGhB,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAM,IAAG;wEAAK,IAAG;kFAChC,cAAA,8OAAC;4EACC,eAAc;4EACd,QAAO;4EACP,KAAI;4EACJ,aAAY;;;;;;;;;;;kFAGhB,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAM,IAAG;wEAAK,IAAG;kFACjC,cAAA,8OAAC;4EACC,eAAc;4EACd,QAAO;4EACP,KAAI;4EACJ,aAAY;;;;;;;;;;;kFAGhB,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAK,IAAG;wEAAK,IAAG;kFAChC,cAAA,8OAAC;4EACC,eAAc;4EACd,QAAO;4EACP,KAAI;4EACJ,aAAY;;;;;;;;;;;;;;;;;0EAMlB,8OAAC;gEACC,QAAO;gEACP,aAAY;gEACZ,WAAU;;kFAEV,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAM,IAAG;wEAAM,IAAG;;;;;;kFACpC,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAM,IAAG;wEAAM,IAAG;;;;;;kFACpC,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAM,IAAG;wEAAK,IAAG;;;;;;kFAClC,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAM,IAAG;wEAAK,IAAG;;;;;;kFAClC,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAI,IAAG;wEAAM,IAAG;;;;;;kFAClC,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAI,IAAG;wEAAM,IAAG;;;;;;kFAClC,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAI,IAAG;wEAAK,IAAG;;;;;;kFAChC,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAI,IAAG;wEAAK,IAAG;;;;;;kFAChC,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAK,IAAG;wEAAM,IAAG;;;;;;kFACnC,8OAAC;wEAAK,IAAG;wEAAM,IAAG;wEAAK,IAAG;wEAAM,IAAG;;;;;;kFACnC,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAK,IAAG;wEAAK,IAAG;;;;;;kFACjC,8OAAC;wEAAK,IAAG;wEAAK,IAAG;wEAAK,IAAG;wEAAK,IAAG;;;;;;;;;;;;0EAInC,8OAAC;gEAAE,SAAQ;;kFACT,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,GAAE;wEACF,QAAO;wEACP,aAAY;wEACZ,MAAK;wEACL,iBAAgB;wEAChB,kBAAiB;kFAEjB,cAAA,8OAAC;4EACC,eAAc;4EACd,QAAO;4EACP,KAAI;4EACJ,aAAY;4EACZ,OAAM;;;;;;;;;;;kFAGV,8OAAC;wEACC,GAAE;wEACF,QAAO;wEACP,aAAY;wEACZ,MAAK;wEACL,iBAAgB;wEAChB,kBAAiB;kFAEjB,cAAA,8OAAC;4EACC,eAAc;4EACd,QAAO;4EACP,KAAI;4EACJ,aAAY;4EACZ,OAAM;;;;;;;;;;;;;;;;;0EAMZ,8OAAC;gEACC,IAAG;gEACH,GAAE;gEACF,QAAO;gEACP,aAAY;gEACZ,MAAK;gEACL,iBAAgB;gEAChB,kBAAiB;gEACjB,SAAQ;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;;;;;;;0EAKV,8OAAC;gEAAO,GAAE;gEAAI,SAAQ;;kFACpB,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,KAAI;wEACJ,aAAY;wEACZ,OAAM;kFAEN,cAAA,8OAAC;4EAAM,MAAK;;;;;;;;;;;;;;;;;0EAKhB,8OAAC;gEAAK,QAAO;gEAAgB,aAAY;gEAAI,SAAQ;;kFACnD,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;;;;;;kFAEd,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;kFAER,8OAAC;wEACC,eAAc;wEACd,QAAO;wEACP,KAAI;wEACJ,aAAY;wEACZ,OAAM;;;;;;;;;;;;;;;;;;kEAMZ,8OAAC;wDAAE,WAAU;wDAAsB,SAAQ;;0EACzC,8OAAC;gEACC,eAAc;gEACd,QAAO;gEACP,KAAI;gEACJ,aAAY;;;;;;0EAEd,8OAAC;gEACC,GAAE;gEACF,GAAE;gEACF,OAAM;gEACN,QAAO;gEACP,IAAG;gEACH,MAAK;;;;;;0EAEP,8OAAC;gEACC,GAAE;gEACF,GAAE;gEACF,YAAW;gEACX,UAAS;gEACT,YAAW;gEACX,MAAK;gEACL,YAAW;;oEACZ;oEACK;kFACJ,8OAAC;;0FACC,8OAAC;gFACC,QAAO;gFACP,KAAI;gFACJ,aAAY;gFACZ,OAAM;;;;;;4EACN;;;;;;;;;;;;;;;;;;;kEAOR,8OAAC;wDACC,GAAE;wDACF,GAAE;wDACF,YAAW;wDACX,UAAS;wDACT,MAAK;wDACL,SAAQ;wDACR,WAAU;;4DACX;0EAEC,8OAAC;gEACC,eAAc;gEACd,QAAO;gEACP,KAAI;gEACJ,aAAY;;;;;;0EAEd,8OAAC;gEACC,eAAc;gEACd,MAAK;gEACL,QAAO;gEACP,KAAI;gEACJ,aAAY;gEACZ,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5B;uCAEe", "debugId": null}}, {"offset": {"line": 3588, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/ui/resizable-navbar.tsx"], "sourcesContent": ["\"use client\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Menu, X } from \"lucide-react\";\r\nimport {\r\n  motion,\r\n  AnimatePresence,\r\n  useScroll,\r\n  useMotionValueEvent,\r\n} from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport React, { useRef, useState } from \"react\";\r\n\r\ninterface NavbarProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface NavBodyProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  visible?: boolean;\r\n}\r\n\r\ninterface NavItemsProps {\r\n  items: {\r\n    name: string;\r\n    link: string;\r\n  }[];\r\n  className?: string;\r\n  onItemClick?: () => void;\r\n}\r\n\r\ninterface MobileNavProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  visible?: boolean;\r\n}\r\n\r\ninterface MobileNavHeaderProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface MobileNavMenuProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport const Navbar = ({ children, className }: NavbarProps) => {\r\n  const ref = useRef<HTMLDivElement>(null);\r\n  const { scrollY } = useScroll({\r\n    target: ref,\r\n    offset: [\"start start\", \"end start\"],\r\n  });\r\n  const [visible, setVisible] = useState<boolean>(false);\r\n\r\n  useMotionValueEvent(scrollY, \"change\", (latest) => {\r\n    if (latest > 100) {\r\n      setVisible(true);\r\n    } else {\r\n      setVisible(false);\r\n    }\r\n  });\r\n\r\n  return (\r\n    <motion.div\r\n      ref={ref}\r\n      // IMPORTANT: Change this to class of `fixed` if you want the navbar to be fixed\r\n      className={cn(\r\n        \"fixed inset-x-0 top-2 z-50 w-full\",\r\n        className,\r\n        visible && \"top-0\"\r\n      )}\r\n    >\r\n      {React.Children.map(children, (child) =>\r\n        React.isValidElement(child)\r\n          ? React.cloneElement(\r\n              child as React.ReactElement<{ visible?: boolean }>,\r\n              { visible }\r\n            )\r\n          : child\r\n      )}\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport const NavBody = ({ children, className, visible }: NavBodyProps) => {\r\n  return (\r\n    <motion.div\r\n      animate={{\r\n        backdropFilter: visible ? \"blur(10px)\" : \"none\",\r\n        boxShadow: visible\r\n          ? \"rgba(34, 42, 53, 0.06) 0px 0px 24px, rgba(0, 0, 0, 0.05) 0px 1px 1px, rgba(34, 42, 53, 0.04) 0px 0px 0px 1px, rgba(34, 42, 53, 0.08) 0px 0px 4px, rgb(155, 45, 232, 0.1) 0px 16px 68px, rgba(155, 50, 209, 0.51) 0px 1px 0px inset\"\r\n          : \"none\",\r\n        width: visible ? \"40%\" : \"100%\",\r\n        y: visible ? 20 : 0,\r\n      }}\r\n      transition={{\r\n        type: \"spring\",\r\n        stiffness: 200,\r\n        damping: 50,\r\n      }}\r\n      style={{\r\n        minWidth: \"800px\",\r\n      }}\r\n      className={cn(\r\n        \"relative z-[60] mx-auto hidden w-full max-w-5xl flex-row items-center justify-between self-start rounded-full bg-white/80 px-4 py-2 dark:bg-background/40 md:flex\",\r\n        visible && \"bg-white/80 dark:bg-background/40\",\r\n        className\r\n      )}\r\n    >\r\n      {children}\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport const NavItems = ({ items, className, onItemClick }: NavItemsProps) => {\r\n  const [hovered, setHovered] = useState<number | null>(null);\r\n\r\n  return (\r\n    <motion.div\r\n      onMouseLeave={() => setHovered(null)}\r\n      className={cn(\r\n        \"absolute inset-0 hidden flex-1 flex-row items-center justify-center space-x-2 text-sm font-medium text-zinc-600 transition duration-200 hover:text-zinc-800 md:flex md:space-x-2\",\r\n        className\r\n      )}\r\n    >\r\n      {items.map((item, idx) => (\r\n        <Link\r\n          onMouseEnter={() => setHovered(idx)}\r\n          onClick={onItemClick}\r\n          className=\"relative px-4 py-2 text-neutral-600 dark:text-neutral-300\"\r\n          key={`link-${idx}`}\r\n          href={item.link}\r\n        >\r\n          {hovered === idx && (\r\n            <motion.div\r\n              layoutId=\"hovered\"\r\n              className=\"absolute inset-0 h-full w-full rounded-full bg-gray-100 dark:bg-neutral-900\"\r\n            />\r\n          )}\r\n          <span className=\"relative z-20\">{item.name}</span>\r\n        </Link>\r\n      ))}\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport const MobileNav = ({ children, className, visible }: MobileNavProps) => {\r\n  return (\r\n    <motion.div\r\n      animate={{\r\n        backdropFilter: visible ? \"blur(10px)\" : \"none\",\r\n        boxShadow: visible\r\n          ? \"rgba(34, 42, 53, 0.06) 0px 0px 24px, rgba(0, 0, 0, 0.05) 0px 1px 1px, rgba(34, 42, 53, 0.04) 0px 0px 0px 1px, rgba(34, 42, 53, 0.08) 0px 0px 4px, rgb(155, 55, 232, 0.1) 0px 16px 68px, rgba(155, 50, 209, 0.51) 0px 1px 0px inset\"\r\n          : \"none\",\r\n        width: visible ? \"90%\" : \"100%\",\r\n        paddingRight: visible ? \"12px\" : \"0px\",\r\n        paddingLeft: visible ? \"12px\" : \"0px\",\r\n        borderRadius: visible ? \"2rem\" : \"2rem\",\r\n        y: visible ? 20 : 0,\r\n      }}\r\n      transition={{\r\n        type: \"spring\",\r\n        stiffness: 200,\r\n        damping: 50,\r\n      }}\r\n      className={cn(\r\n        \"relative z-50 mx-auto flex w-full max-w-[calc(100vw-2rem)] flex-col items-center justify-between bg-background px-0 py-2 md:hidden\",\r\n        visible && \"bg-white/80 dark:bg-neutral-950/80\",\r\n        className\r\n      )}\r\n    >\r\n      {children}\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport const MobileNavHeader = ({\r\n  children,\r\n  className,\r\n}: MobileNavHeaderProps) => {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex w-full flex-row items-center justify-between px-4\",\r\n        className\r\n      )}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport const MobileNavMenu = ({\r\n  children,\r\n  className,\r\n  isOpen,\r\n  onClose,\r\n}: MobileNavMenuProps) => {\r\n  return (\r\n    <AnimatePresence>\r\n      {isOpen && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          className={cn(\r\n            \"absolute inset-x-0 top-16 z-50 flex w-full flex-col items-start justify-start gap-4 rounded-lg bg-white px-4 py-8 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:bg-neutral-950\",\r\n            className\r\n          )}\r\n        >\r\n          {children}\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport const MobileNavToggle = ({\r\n  isOpen,\r\n  onClick,\r\n}: {\r\n  isOpen: boolean;\r\n  onClick: () => void;\r\n}) => {\r\n  return isOpen ? (\r\n    <X className=\"text-black dark:text-white\" onClick={onClick} />\r\n  ) : (\r\n    <Menu className=\"text-black dark:text-white\" onClick={onClick} />\r\n  );\r\n};\r\n\r\nexport const NavbarLogo = () => {\r\n  return (\r\n    <Link href=\"/\" className=\"z-50 flex items-center justify-center gap-2\">\r\n      <img src=\"/logo.png\" alt=\"logo\" className=\"h-8 w-8 rounded-full\" />\r\n      <span className=\"bg-primary from-foreground via-purple-200 to-primary bg-clip-text text-2xl font-semibold text-transparent dark:bg-gradient-to-b md:text-xl\">\r\n        Clarif AI\r\n      </span>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport const NavbarButton = ({\r\n  href,\r\n  as: Tag = \"button\",\r\n  children,\r\n  className,\r\n  variant = \"primary\",\r\n  ...props\r\n}: {\r\n  href?: string;\r\n  as?: React.ElementType;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  variant?: \"primary\" | \"secondary\" | \"dark\" | \"gradient\";\r\n} & (\r\n  | React.ComponentPropsWithoutRef<\"a\">\r\n  | React.ComponentPropsWithoutRef<\"button\">\r\n)) => {\r\n  const baseStyles =\r\n    \"px-4 py-2 rounded-md bg-white button bg-white text-black text-sm font-bold relative cursor-pointer hover:-translate-y-0.5 transition duration-200 inline-block text-center\";\r\n\r\n  const variantStyles = {\r\n    primary:\r\n      \"shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]\",\r\n    secondary: \"bg-transparent shadow-none dark:text-white\",\r\n    dark: \"bg-black text-white shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset]\",\r\n    gradient:\r\n      \"bg-gradient-to-b from-purple-500 to-purple-700 text-white shadow-[0px_5px_5px_0px_rgba(255,255,255,0.6)_inset]\",\r\n  };\r\n\r\n  // Use createElement instead of JSX to avoid the children type issue\r\n  return React.createElement(\r\n    Tag,\r\n    {\r\n      href: href || undefined,\r\n      className: cn(baseStyles, variantStyles[variant], className),\r\n      ...props,\r\n    },\r\n    children\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AAVA;;;;;;;AAkDO,MAAM,SAAS,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAe;IACzD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QAC5B,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEhD,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,UAAU,CAAC;QACtC,IAAI,SAAS,KAAK;YAChB,WAAW;QACb,OAAO;YACL,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,gFAAgF;QAChF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,WACA,WAAW;kBAGZ,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,sBAC7B,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,uBACjB,qMAAA,CAAA,UAAK,CAAC,YAAY,CAChB,OACA;gBAAE;YAAQ,KAEZ;;;;;;AAIZ;AAEO,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAgB;IACpE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,gBAAgB,UAAU,eAAe;YACzC,WAAW,UACP,uOACA;YACJ,OAAO,UAAU,QAAQ;YACzB,GAAG,UAAU,KAAK;QACpB;QACA,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;QACA,OAAO;YACL,UAAU;QACZ;QACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qKACA,WAAW,qCACX;kBAGD;;;;;;AAGP;AAEO,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAiB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,cAAc,IAAM,WAAW;QAC/B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oLACA;kBAGD,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC,4JAAA,CAAA,UAAI;gBACH,cAAc,IAAM,WAAW;gBAC/B,SAAS;gBACT,WAAU;gBAEV,MAAM,KAAK,IAAI;;oBAEd,YAAY,qBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGd,8OAAC;wBAAK,WAAU;kCAAiB,KAAK,IAAI;;;;;;;eATrC,CAAC,KAAK,EAAE,KAAK;;;;;;;;;;AAc5B;AAEO,MAAM,YAAY,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAkB;IACxE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,gBAAgB,UAAU,eAAe;YACzC,WAAW,UACP,uOACA;YACJ,OAAO,UAAU,QAAQ;YACzB,cAAc,UAAU,SAAS;YACjC,aAAa,UAAU,SAAS;YAChC,cAAc,UAAU,SAAS;YACjC,GAAG,UAAU,KAAK;QACpB;QACA,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;QACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sIACA,WAAW,sCACX;kBAGD;;;;;;AAGP;AAEO,MAAM,kBAAkB,CAAC,EAC9B,QAAQ,EACR,SAAS,EACY;IACrB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;kBAGD;;;;;;AAGP;AAEO,MAAM,gBAAgB,CAAC,EAC5B,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACY;IACnB,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+VACA;sBAGD;;;;;;;;;;;AAKX;AAEO,MAAM,kBAAkB,CAAC,EAC9B,MAAM,EACN,OAAO,EAIR;IACC,OAAO,uBACL,8OAAC,4LAAA,CAAA,IAAC;QAAC,WAAU;QAA6B,SAAS;;;;;6BAEnD,8OAAC,kMAAA,CAAA,OAAI;QAAC,WAAU;QAA6B,SAAS;;;;;;AAE1D;AAEO,MAAM,aAAa;IACxB,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;QAAI,WAAU;;0BACvB,8OAAC;gBAAI,KAAI;gBAAY,KAAI;gBAAO,WAAU;;;;;;0BAC1C,8OAAC;gBAAK,WAAU;0BAA6I;;;;;;;;;;;;AAKnK;AAEO,MAAM,eAAe,CAAC,EAC3B,IAAI,EACJ,IAAI,MAAM,QAAQ,EAClB,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,GAAG,OAUJ;IACC,MAAM,aACJ;IAEF,MAAM,gBAAgB;QACpB,SACE;QACF,WAAW;QACX,MAAM;QACN,UACE;IACJ;IAEA,oEAAoE;IACpE,qBAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CACxB,KACA;QACE,MAAM,QAAQ;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,aAAa,CAAC,QAAQ,EAAE;QAClD,GAAG,KAAK;IACV,GACA;AAEJ", "debugId": null}}, {"offset": {"line": 3833, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-95 cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean;\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\";\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = \"Button\";\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,qYACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3891, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/ui/mode-toggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Moon, Sun } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport function ModeToggle() {\r\n  const { theme, setTheme } = useTheme();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"icon\"\r\n      className=\"z-50 rounded-full text-primary\"\r\n      onClick={() => setTheme((theme) => (theme === \"dark\" ? \"light\" : \"dark\"))}\r\n    >\r\n      {theme === \"dark\" ? <Sun /> : <Moon />}\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AALA;;;;;AAOO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEnC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAU;QACV,SAAS,IAAM,SAAS,CAAC,QAAW,UAAU,SAAS,UAAU;kBAEhE,UAAU,uBAAS,8OAAC,gMAAA,CAAA,MAAG;;;;iCAAM,8OAAC,kMAAA,CAAA,OAAI;;;;;;;;;;AAGzC", "debugId": null}}, {"offset": {"line": 3932, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/shared/navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  MobileNav,\r\n  MobileNavHeader,\r\n  MobileNavMenu,\r\n  MobileNavToggle,\r\n  Navbar,\r\n  NavbarButton,\r\n  NavbarLogo,\r\n  NavBody,\r\n  NavItems,\r\n} from \"@/components/ui/resizable-navbar\";\r\nimport { useUser } from \"@civic/auth/react\";\r\nimport { useState } from \"react\";\r\nimport { ModeToggle } from \"@/components/ui/mode-toggle\";\r\n\r\nexport function NavbarDemo() {\r\n  const navItems = [\r\n    {\r\n      name: \"Docs\",\r\n      link: \"/#docs\",\r\n    },\r\n    {\r\n      name: \"Features\",\r\n      link: \"/#features\",\r\n    },\r\n    {\r\n      name: \"Reviews\",\r\n      link: \"#reviews\",\r\n    },\r\n    {\r\n      name: \"FAQs\",\r\n      link: \"#faq\",\r\n    },\r\n  ];\r\n\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n\r\n  const { signIn } = useUser();\r\n\r\n  return (\r\n    <Navbar className=\"z-[150]\">\r\n      <NavBody>\r\n        <NavbarLogo />\r\n        <NavItems items={navItems} />\r\n        <div className=\"flex items-center gap-4\">\r\n          <NavbarButton variant=\"gradient\" onClick={() => signIn()}>\r\n            Sign in\r\n          </NavbarButton>\r\n          <ModeToggle />\r\n        </div>\r\n      </NavBody>\r\n\r\n      {/* Mobile Navigation */}\r\n      <MobileNav>\r\n        <MobileNavHeader>\r\n          <NavbarLogo />\r\n          <div className=\"flex items-center gap-4\">\r\n            <ModeToggle />\r\n            <MobileNavToggle\r\n              isOpen={isMobileMenuOpen}\r\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n            />\r\n          </div>\r\n        </MobileNavHeader>\r\n\r\n        <MobileNavMenu\r\n          isOpen={isMobileMenuOpen}\r\n          onClose={() => setIsMobileMenuOpen(false)}\r\n        >\r\n          {navItems.map((item, idx) => (\r\n            <a\r\n              key={`mobile-link-${idx}`}\r\n              href={item.link}\r\n              onClick={() => setIsMobileMenuOpen(false)}\r\n              className=\"relative text-neutral-600 dark:text-neutral-300\"\r\n            >\r\n              <span className=\"block\">{item.name}</span>\r\n            </a>\r\n          ))}\r\n          <div className=\"flex w-full flex-col gap-4\">\r\n            <NavbarButton\r\n              variant=\"gradient\"\r\n              className=\"w-full\"\r\n              onClick={() => signIn()}\r\n            >\r\n              Github\r\n            </NavbarButton>\r\n          </div>\r\n        </MobileNavMenu>\r\n      </MobileNav>\r\n    </Navbar>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAWA;AAAA;AACA;AACA;;;;;;AAfA;;;;;;AAiBO,SAAS;IACd,MAAM,WAAW;QACf;YACE,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;QACR;QACA;YACE,MAAM;YACN,MAAM;QACR;KACD;IAED,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD;IAEzB,qBACE,8OAAC,+IAAA,CAAA,SAAM;QAAC,WAAU;;0BAChB,8OAAC,+IAAA,CAAA,UAAO;;kCACN,8OAAC,+IAAA,CAAA,aAAU;;;;;kCACX,8OAAC,+IAAA,CAAA,WAAQ;wBAAC,OAAO;;;;;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,+IAAA,CAAA,eAAY;gCAAC,SAAQ;gCAAW,SAAS,IAAM;0CAAU;;;;;;0CAG1D,8OAAC,0IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;0BAKf,8OAAC,+IAAA,CAAA,YAAS;;kCACR,8OAAC,+IAAA,CAAA,kBAAe;;0CACd,8OAAC,+IAAA,CAAA,aAAU;;;;;0CACX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0IAAA,CAAA,aAAU;;;;;kDACX,8OAAC,+IAAA,CAAA,kBAAe;wCACd,QAAQ;wCACR,SAAS,IAAM,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;kCAK1C,8OAAC,+IAAA,CAAA,gBAAa;wBACZ,QAAQ;wBACR,SAAS,IAAM,oBAAoB;;4BAElC,SAAS,GAAG,CAAC,CAAC,MAAM,oBACnB,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CAEV,cAAA,8OAAC;wCAAK,WAAU;kDAAS,KAAK,IAAI;;;;;;mCAL7B,CAAC,YAAY,EAAE,KAAK;;;;;0CAQ7B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,+IAAA,CAAA,eAAY;oCACX,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM;8CAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}