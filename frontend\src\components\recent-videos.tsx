// components/recent-videos.tsx
"use client";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { Progress } from "@/components/ui/progress";
import {
  AlertCircle,
  CheckCircle2,
  ChevronLeft,
  ChevronRight,
  Clock,
  Library,
  Brain,
  Download,
  Edit,
  Loader2,
  MoreVertical,
  Play,
  Share,
  ThumbsDown,
  ThumbsUp,
  Trash2,
  Video,
  Zap,
} from "lucide-react";
import Link from "next/link";

import { useRef, useState } from "react";

export function RecentVideosTable() {
  const [recentVideosScrollPosition, setRecentVideosScrollPosition] =
    useState(0);
  const recentVideosRef = useRef<HTMLDivElement>(null);

  const recentVideos = [
    {
      id: 1,
      title: "Ocean Waves at Sunset - Peaceful Meditation",
      prompt:
        "Peaceful ocean waves during golden hour sunset with seagulls flying",
      duration: "0:30",
      createdAt: "2 hours ago",
      thumbnail:
        "https://kzmfqpucb1jpkkt6hpih.lite.vusercontent.net/placeholder.svg?height=180&width=320",
      status: "completed",
      views: 1245,
      likes: 89,
      dislikes: 3,
      creator: "John Doe",
      creatorAvatar: "/placeholder.svg?height=32&width=32",
      description:
        "A serene ocean scene perfect for relaxation and meditation content",
      style: "realistic",
    },
    {
      id: 2,
      title: "City Traffic Time-lapse - Urban Energy",
      prompt:
        "Busy city intersection with flowing traffic and neon lights at night",
      duration: "0:45",
      createdAt: "5 hours ago",
      thumbnail:
        "https://kzmfqpucb1jpkkt6hpih.lite.vusercontent.net/placeholder.svg?height=180&width=320",
      status: "completed",
      views: 892,
      likes: 67,
      dislikes: 5,
      creator: "John Doe",
      creatorAvatar: "/placeholder.svg?height=32&width=32",
      description:
        "Urban energy captured in motion with stunning neon reflections",
      style: "cinematic",
    },
    {
      id: 3,
      title: "Mountain Landscape - Nature's Beauty",
      prompt: "Majestic mountain range with morning mist and pine trees",
      duration: "1:00",
      createdAt: "1 day ago",
      thumbnail:
        "https://kzmfqpucb1jpkkt6hpih.lite.vusercontent.net/placeholder.svg?height=180&width=320",
      status: "completed",
      views: 2156,
      likes: 178,
      dislikes: 8,
      creator: "John Doe",
      creatorAvatar: "/placeholder.svg?height=32&width=32",
      description:
        "Nature's beauty captured in artistic style with ethereal morning mist",
      style: "artistic",
    },
    {
      id: 4,
      title: "Underwater Coral Reef - Marine Paradise",
      prompt: "Vibrant underwater coral reef with tropical fish swimming",
      duration: "0:40",
      createdAt: "2 hours ago",
      thumbnail:
        "https://kzmfqpucb1jpkkt6hpih.lite.vusercontent.net/placeholder.svg?height=180&width=320",
      status: "generating",
      views: 0,
      likes: 0,
      dislikes: 0,
      creator: "John Doe",
      creatorAvatar: "/placeholder.svg?height=32&width=32",
      description: "Beautiful underwater ecosystem in progress",
      style: "realistic",
      progress: 65,
    },
    {
      id: 5,
      title: "Abstract Art Animation - Colorful Dreams",
      prompt: "Colorful abstract shapes morphing and flowing in space",
      duration: "0:25",
      createdAt: "3 hours ago",
      thumbnail:
        "https://kzmfqpucb1jpkkt6hpih.lite.vusercontent.net/placeholder.svg?height=180&width=320",
      status: "processing",
      views: 0,
      likes: 0,
      dislikes: 0,
      creator: "John Doe",
      creatorAvatar: "/placeholder.svg?height=32&width=32",
      description: "Perfect for social media content with vibrant colors",
      style: "animated",
      progress: 90,
    },
    {
      id: 6,
      title: "Forest Walk - Immersive Nature Experience",
      prompt: "First-person view walking through a dense forest",
      duration: "1:30",
      createdAt: "6 hours ago",
      thumbnail:
        "https://kzmfqpucb1jpkkt6hpih.lite.vusercontent.net/placeholder.svg?height=180&width=320",
      status: "completed",
      views: 3421,
      likes: 298,
      dislikes: 12,
      creator: "John Doe",
      creatorAvatar: "/placeholder.svg?height=32&width=32",
      description: "An immersive journey through nature's cathedral",
      style: "realistic",
    },
  ];

  const scrollRecentVideos = (direction: "left" | "right") => {
    if (recentVideosRef.current) {
      const scrollAmount = 320; // Width of one video card plus gap
      const newPosition =
        direction === "left"
          ? Math.max(0, recentVideosScrollPosition - scrollAmount)
          : recentVideosScrollPosition + scrollAmount;

      recentVideosRef.current.scrollTo({
        left: newPosition,
        behavior: "smooth",
      });
      setRecentVideosScrollPosition(newPosition);
    }
  };

  const formatViews = (views: number) => {
    if (views === 0) return "No views";
    if (views < 1000) return `${views} view${views !== 1 ? "s" : ""}`;
    if (views < 1000000) return `${(views / 1000).toFixed(1)}K views`;
    return `${(views / 1000000).toFixed(1)}M views`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-3 w-3 text-green-500" />;
      case "generating":
        return <Loader2 className="h-3 w-3 text-blue-500 animate-spin" />;
      case "processing":
        return <Clock className="h-3 w-3 text-yellow-500" />;
      case "failed":
        return <AlertCircle className="h-3 w-3 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <EnhancedCard
      className="col-span-5 animate-slide-up"
      style={{ animationDelay: "0.2s" }}
    >
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-3 text-xl">
              <Video className="h-5 w-5 text-primary" />
              Recent Videos
            </CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => scrollRecentVideos("left")}
              disabled={recentVideosScrollPosition === 0}
              className="h-8 w-8 p-0 rounded-full"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => scrollRecentVideos("right")}
              className="h-8 w-8 p-0 rounded-full"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/library">View All</Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent  className="-mt-4">
        <div
          ref={recentVideosRef}
          className="flex space-x-6 overflow-x-auto scrollbar-hide pb-4"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        >
          {recentVideos.map((video, index) => (
            <div
              key={video.id}
              className="flex-shrink-0 w-80 group cursor-pointer animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Video Thumbnail Container */}
              <div className="relative aspect-video mb-3 overflow-hidden rounded-xl bg-muted shadow-sm hover:shadow-lg transition-all duration-200">
                <img
                  src={video.thumbnail || "/placeholder.svg"}
                  alt={video.title}
                  className="h-full w-full object-cover transition-transform duration-200 group-hover:scale-105"
                />

                {/* Video Duration Overlay */}
                <div className="absolute bottom-2 right-2">
                  <Badge
                    variant="secondary"
                    className="bg-black/80 text-white border-0 text-xs font-medium px-2 py-1 rounded"
                  >
                    {video.duration}
                  </Badge>
                </div>

                {/* Status Indicators for Generating/Processing Videos */}
                {(video.status === "generating" ||
                  video.status === "processing") && (
                  <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                    <div className="text-center text-white space-y-3 p-4">
                      <div className="flex items-center justify-center space-x-2">
                        {getStatusIcon(video.status)}
                        <span className="text-sm font-medium capitalize">
                          {video.status}...
                        </span>
                      </div>
                      <div className="w-32 mx-auto">
                        <Progress
                          value={video.progress || 0}
                          className="h-2 bg-white/20"
                        />
                      </div>
                      <div className="text-xs opacity-80">
                        {video.progress || 0}% complete
                      </div>
                    </div>
                  </div>
                )}

                {/* Status Icon for Completed Videos */}
                {video.status === "completed" && (
                  <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {getStatusIcon(video.status)}
                  </div>
                )}

                {/* Play Button Overlay */}
                {video.status === "completed" && (
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    <div className="bg-white/90 rounded-full p-4 transform scale-90 group-hover:scale-100 transition-transform duration-200 shadow-lg">
                      <Play className="h-6 w-6 text-black fill-current ml-0.5" />
                    </div>
                  </div>
                )}

                {/* Context Menu */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 bg-black/60 hover:bg-black/80 text-white rounded-full"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="animate-scale-in"
                    >
                      <DropdownMenuItem disabled={video.status !== "completed"}>
                        <Play className="mr-2 h-4 w-4" />
                        Play
                      </DropdownMenuItem>
                      <DropdownMenuItem disabled={video.status !== "completed"}>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem disabled={video.status !== "completed"}>
                        <Share className="mr-2 h-4 w-4" />
                        Share
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit details
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive focus:text-destructive">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              {/* Video Information - YouTube Style */}
              <div className="space-y-2">
                {/* Creator Avatar and Title */}
                <div className="flex space-x-3">
                  <div className="flex-1 min-w-0">
                    {/* Title */}
                    <h3 className="font-medium text-md leading-5 line-clamp-2 group-hover:text-primary transition-colors mb-1">
                      {video.title}
                    </h3>

                    {/* Views and Date */}
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <span>{formatViews(video.views)}</span>
                      {video.views > 0 && (
                        <>
                          <span>•</span>
                          <span>{video.createdAt}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Description */}
                {video.description && (
                  <p className="text-xs text-muted-foreground line-clamp-2 leading-4">
                    {video.description}
                  </p>
                )}

                {/* Engagement and Metadata */}
                <div className="flex items-center justify-between">
                  {/* Engagement Stats */}
                  {video.status === "completed" && video.views > 0 && (
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                        <ThumbsUp className="h-3 w-3" />
                        <span>{video.likes}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                        <ThumbsDown className="h-3 w-3" />
                        <span>{video.dislikes}</span>
                      </div>
                    </div>
                  )}

                  {/* Metadata Badges */}
                  <div className="flex items-center space-x-1">
                    <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                      {video.style}
                    </Badge>
                  </div>
                </div>

                {/* Status for generating videos */}
                {(video.status === "generating" ||
                  video.status === "processing") && (
                  <div className="flex items-center space-x-2 text-xs">
                    <Badge variant="secondary" className="text-xs">
                      {video.status === "generating"
                        ? "Generating"
                        : "Processing"}
                    </Badge>
                    <span className="text-muted-foreground">
                      {video.progress || 0}% complete
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </EnhancedCard>
  );
}
