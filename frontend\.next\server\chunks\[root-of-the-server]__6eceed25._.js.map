{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/app/api/entries/update/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { db } from \"@/lib/db\";\n\nexport async function POST(req: NextRequest) {\n  const data = await req.json();\n  const { entryId, videoURL } = data;\n\n  // Validate required fields\n  if (!entryId || !videoURL) {\n    return NextResponse.json(\n      { error: \"entryId and videoURL are required\" },\n      { status: 400 }\n    );\n  }\n\n  // Validate that entryId is a valid string\n  if (typeof entryId !== \"string\" || typeof videoURL !== \"string\") {\n    return NextResponse.json(\n      { error: \"entryId and videoURL must be strings\" },\n      { status: 400 }\n    );\n  }\n\n  console.log(\"Updating entry\", entryId, \"with video URL\", videoURL);\n\n  // Check if entry exists\n  //const existingEntry = await db.entries.findUnique({\n  // where: { id: entryId },\n  // });\n\n  // if (!existingEntry) {\n  //   return NextResponse.json(\n  //     { error: \"Entry not found\" },\n  //    { status: 404 }\n  //   );\n  //  }\n\n  //   // Update the entry with the video URL\n  //   const updatedEntry = await db.entries.update({\n  //     where: { id: entryId },\n  //     data: { videoUrl: videoURL },\n  //   });\n\n  //   return NextResponse.json({\n  //     success: true,\n  //     message: \"Entry updated successfully\",\n  //     entry: {\n  //       id: updatedEntry.id,\n  //       videoUrl: updatedEntry.videoUrl,\n  //       updatedAt: updatedEntry.updatedAt,\n  //     },\n  //   });\n\n  // } catch (error) {\n  //   console.error(\"Error updating entry:\", error);\n\n  //   // Handle Prisma errors\n  //   if (error instanceof Error) {\n  //     if (error.message.includes(\"Record to update not found\")) {\n  //       return NextResponse.json(\n  //         { error: \"Entry not found\" },\n  //         { status: 404 }\n  //       );\n  //     }\n  //   }\n\n  //   return NextResponse.json(\n  //     { error: \"Internal server error\" },\n  //     { status: 500 }\n  //   );\n  // }\n}\n\n// Optional: Add GET method to retrieve entry details\nexport async function GET(req: NextRequest) {\n  // try {\n  const { searchParams } = new URL(req.url);\n  const entryId = searchParams.get(\"entryId\");\n\n  // if (!entryId) {\n  //   return NextResponse.json(\n  //     { error: \"entryId parameter is required\" },\n  //     { status: 400 }\n  //   );\n  // }\n\n  // const entry = await db.entries.findUnique({\n  //   where: { id: entryId },\n  //   select: {\n  //     id: true,\n  //     videoUrl: true,\n  //     prompt: true,\n  //     createdAt: true,\n  //     updatedAt: true,\n  //   },\n  // });\n\n  //   if (!entry) {\n  //     return NextResponse.json({ error: \"Entry not found\" }, { status: 404 });\n  //   }\n\n  return NextResponse.json({\n    success: true,\n    entryId,\n  });\n  // } catch (error) {\n  //   console.error(\"Error retrieving entry:\", error);\n  //   return NextResponse.json(\n  //     { error: \"Internal server error\" },\n  //     { status: 500 }\n  //   );\n  // }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe,KAAK,GAAgB;IACzC,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;IAE9B,2BAA2B;IAC3B,IAAI,CAAC,WAAW,CAAC,UAAU;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoC,GAC7C;YAAE,QAAQ;QAAI;IAElB;IAEA,0CAA0C;IAC1C,IAAI,OAAO,YAAY,YAAY,OAAO,aAAa,UAAU;QAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;IAEA,QAAQ,GAAG,CAAC,kBAAkB,SAAS,kBAAkB;AAEzD,wBAAwB;AACxB,qDAAqD;AACrD,0BAA0B;AAC1B,MAAM;AAEN,wBAAwB;AACxB,8BAA8B;AAC9B,oCAAoC;AACpC,qBAAqB;AACrB,OAAO;AACP,KAAK;AAEL,2CAA2C;AAC3C,mDAAmD;AACnD,8BAA8B;AAC9B,oCAAoC;AACpC,QAAQ;AAER,+BAA+B;AAC/B,qBAAqB;AACrB,6CAA6C;AAC7C,eAAe;AACf,6BAA6B;AAC7B,yCAAyC;AACzC,2CAA2C;AAC3C,SAAS;AACT,QAAQ;AAER,oBAAoB;AACpB,mDAAmD;AAEnD,4BAA4B;AAC5B,kCAAkC;AAClC,kEAAkE;AAClE,kCAAkC;AAClC,wCAAwC;AACxC,0BAA0B;AAC1B,WAAW;AACX,QAAQ;AACR,MAAM;AAEN,8BAA8B;AAC9B,0CAA0C;AAC1C,sBAAsB;AACtB,OAAO;AACP,IAAI;AACN;AAGO,eAAe,IAAI,GAAgB;IACxC,QAAQ;IACR,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG;IACxC,MAAM,UAAU,aAAa,GAAG,CAAC;IAEjC,kBAAkB;IAClB,8BAA8B;IAC9B,kDAAkD;IAClD,sBAAsB;IACtB,OAAO;IACP,IAAI;IAEJ,8CAA8C;IAC9C,4BAA4B;IAC5B,cAAc;IACd,gBAAgB;IAChB,sBAAsB;IACtB,oBAAoB;IACpB,uBAAuB;IACvB,uBAAuB;IACvB,OAAO;IACP,MAAM;IAEN,kBAAkB;IAClB,+EAA+E;IAC/E,MAAM;IAEN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;IACF;AACA,oBAAoB;AACpB,qDAAqD;AACrD,8BAA8B;AAC9B,0CAA0C;AAC1C,sBAAsB;AACtB,OAAO;AACP,IAAI;AACN", "debugId": null}}]}