#!/usr/bin/env python3
"""
End-to-End Test Script for Render with UploadThing Integration

This script tests the complete workflow:
1. POST to /render endpoint with entryId, script, and scene name
2. Monitor job progress until completion
3. Verify video upload to UploadThing
4. Check database entry update
5. Return comprehensive results

Usage:
    python test_end_to_end_render.py
"""

import asyncio
import httpx
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional


# Configuration
BACKEND_BASE_URL = "http://localhost:8000"
FRONTEND_BASE_URL = "http://localhost:3000"
TIMEOUT = 120  # 2 minutes timeout for requests
MAX_WAIT_TIME = 600  # 10 minutes max wait for job completion

# Sample Manim script for testing
SAMPLE_SCRIPT = """from manim import *

class TestUploadScene(Scene):
    def construct(self):
        # Create title
        title = Text("UploadThing Integration Test", font_size=48, color=BLUE)
        subtitle = Text("End-to-End Workflow Demo", font_size=32, color=WHITE)
        subtitle.next_to(title, DOWN, buff=0.5)
        
        # Animate title
        self.play(Write(title))
        self.wait(0.5)
        self.play(Write(subtitle))
        self.wait(1)
        
        # Create animated shapes
        circle = Circle(radius=1.5, color=GREEN)
        square = Square(side_length=2, color=RED)
        triangle = Triangle(color=YELLOW)
        
        # Position shapes
        circle.shift(LEFT * 3)
        square.shift(RIGHT * 3)
        triangle.shift(UP * 2)
        
        # Animate shapes
        self.play(Create(circle), Create(square), Create(triangle))
        self.wait(0.5)
        
        # Transform shapes
        self.play(
            Transform(circle, square.copy().shift(LEFT * 3)),
            Transform(square, triangle.copy().shift(RIGHT * 3)),
            Transform(triangle, circle.copy().shift(UP * 2))
        )
        self.wait(1)
        
        # Final message
        success_text = Text("✅ Upload Test Complete!", font_size=36, color=GREEN)
        success_text.shift(DOWN * 2)
        
        self.play(Write(success_text))
        self.wait(2)
        
        # Fade out everything
        self.play(FadeOut(Group(*self.mobjects)))
        self.wait(0.5)
"""


class EndToEndTester:
    """Complete end-to-end test for render with UploadThing integration."""
    
    def __init__(self):
        self.backend_client = httpx.AsyncClient(base_url=BACKEND_BASE_URL, timeout=TIMEOUT)
        self.frontend_client = httpx.AsyncClient(base_url=FRONTEND_BASE_URL, timeout=TIMEOUT)
        self.test_entry_id = str(uuid.uuid4())
        self.job_id = None
        self.results = {}
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.backend_client.aclose()
        await self.frontend_client.aclose()
    
    def log(self, message: str, level: str = "INFO"):
        """Log a message with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        icon = {"INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", "WARNING": "⚠️"}.get(level, "📝")
        print(f"[{timestamp}] {icon} {message}")
    
    async def test_backend_health(self) -> bool:
        """Check if backend is accessible."""
        try:
            response = await self.backend_client.get("/")
            if response.status_code == 200:
                self.log("Backend is accessible", "SUCCESS")
                return True
            else:
                self.log(f"Backend returned status {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"Backend not accessible: {e}", "ERROR")
            return False
    
    async def submit_render_job(self) -> bool:
        """Submit a render job with entryId."""
        try:
            self.log("Submitting render job...")
            
            payload = {
                "script": SAMPLE_SCRIPT,
                "scene_name": "TestUploadScene",
                "priority": 1,
                "entry_id": self.test_entry_id
            }
            
            self.log(f"Entry ID: {self.test_entry_id}")
            self.log(f"Scene: TestUploadScene")
            
            response = await self.backend_client.post("/render", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                self.job_id = data.get("job_id")
                self.results["job_submission"] = data
                
                self.log(f"Job submitted successfully! Job ID: {self.job_id}", "SUCCESS")
                return True
            else:
                self.log(f"Job submission failed: HTTP {response.status_code}", "ERROR")
                self.log(f"Response: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Error submitting job: {e}", "ERROR")
            return False
    
    async def monitor_job_progress(self) -> bool:
        """Monitor job progress until completion."""
        if not self.job_id:
            self.log("No job ID to monitor", "ERROR")
            return False
        
        self.log("Monitoring job progress...")
        start_time = time.time()
        
        while time.time() - start_time < MAX_WAIT_TIME:
            try:
                response = await self.backend_client.get(f"/jobs/{self.job_id}")
                
                if response.status_code == 200:
                    data = response.json()
                    status = data.get("status", "unknown")
                    
                    if status == "completed":
                        self.log("Job completed successfully!", "SUCCESS")
                        self.results["job_completion"] = data
                        return True
                    elif status == "failed":
                        self.log("Job failed!", "ERROR")
                        self.results["job_completion"] = data
                        self.log(f"Error details: {data.get('error', 'No error details')}", "ERROR")
                        return False
                    elif status == "cancelled":
                        self.log("Job was cancelled", "WARNING")
                        self.results["job_completion"] = data
                        return False
                    else:
                        # Job still in progress
                        elapsed = int(time.time() - start_time)
                        self.log(f"Job status: {status} (elapsed: {elapsed}s)")
                        await asyncio.sleep(5)  # Wait 5 seconds before checking again
                else:
                    self.log(f"Error checking job status: HTTP {response.status_code}", "ERROR")
                    await asyncio.sleep(5)
                    
            except Exception as e:
                self.log(f"Error monitoring job: {e}", "WARNING")
                await asyncio.sleep(5)
        
        self.log("Job monitoring timed out", "ERROR")
        return False
    
    async def verify_upload_results(self) -> bool:
        """Verify the upload results and UploadThing integration."""
        if "job_completion" not in self.results:
            self.log("No job completion data to verify", "ERROR")
            return False
        
        try:
            job_data = self.results["job_completion"]
            result = job_data.get("result", {})
            output_urls = result.get("output_urls", [])
            
            self.log("Analyzing upload results...")
            
            # Check for local media files
            local_urls = [url for url in output_urls if url.startswith("/media/")]
            uploadthing_urls = [url for url in output_urls if "uploadthing" in url.lower()]
            
            self.results["local_urls"] = local_urls
            self.results["uploadthing_urls"] = uploadthing_urls
            
            if local_urls:
                self.log(f"✅ Local media files: {len(local_urls)}", "SUCCESS")
                for url in local_urls:
                    self.log(f"   📁 {url}")
            else:
                self.log("❌ No local media files found", "ERROR")
            
            if uploadthing_urls:
                self.log(f"✅ UploadThing URLs: {len(uploadthing_urls)}", "SUCCESS")
                for url in uploadthing_urls:
                    self.log(f"   🔗 {url}")
                    
                # Test if UploadThing URLs are accessible
                await self.test_uploadthing_urls(uploadthing_urls)
                return True
            else:
                self.log("❌ No UploadThing URLs found", "ERROR")
                self.log("This might indicate UploadThing upload failed", "WARNING")
                return False
                
        except Exception as e:
            self.log(f"Error verifying upload results: {e}", "ERROR")
            return False
    
    async def test_uploadthing_urls(self, urls: list):
        """Test if UploadThing URLs are accessible."""
        self.log("Testing UploadThing URL accessibility...")
        
        for url in urls:
            try:
                async with httpx.AsyncClient(timeout=10) as client:
                    response = await client.head(url)
                    if response.status_code == 200:
                        self.log(f"✅ URL accessible: {url[:50]}...", "SUCCESS")
                    else:
                        self.log(f"⚠️ URL returned {response.status_code}: {url[:50]}...", "WARNING")
            except Exception as e:
                self.log(f"❌ URL test failed: {url[:50]}... - {e}", "ERROR")
    
    async def verify_database_update(self) -> bool:
        """Verify that the database entry was updated with video URL."""
        try:
            self.log("Checking database entry update...")
            
            response = await self.frontend_client.get(
                f"/api/entries/update?entryId={self.test_entry_id}"
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    entry = data.get("entry", {})
                    video_url = entry.get("videoUrl")
                    
                    if video_url:
                        self.log(f"✅ Database updated with video URL: {video_url[:50]}...", "SUCCESS")
                        self.results["database_video_url"] = video_url
                        return True
                    else:
                        self.log("❌ Database entry has no video URL", "ERROR")
                        return False
                else:
                    self.log(f"❌ Database query failed: {data}", "ERROR")
                    return False
            elif response.status_code == 404:
                self.log("❌ Entry not found in database", "ERROR")
                return False
            else:
                self.log(f"❌ Database check failed: HTTP {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Error checking database: {e}", "ERROR")
            return False
    
    def print_final_results(self):
        """Print comprehensive final results."""
        print("\n" + "=" * 80)
        print("🎯 END-TO-END TEST RESULTS")
        print("=" * 80)
        
        # Job Information
        print(f"📋 Test Entry ID: {self.test_entry_id}")
        print(f"🆔 Job ID: {self.job_id}")
        
        # URLs
        if "local_urls" in self.results:
            print(f"\n📁 Local Media URLs ({len(self.results['local_urls'])}):")
            for url in self.results["local_urls"]:
                print(f"   {url}")
        
        if "uploadthing_urls" in self.results:
            print(f"\n🔗 UploadThing URLs ({len(self.results['uploadthing_urls'])}):")
            for url in self.results["uploadthing_urls"]:
                print(f"   {url}")
        
        if "database_video_url" in self.results:
            print(f"\n💾 Database Video URL:")
            print(f"   {self.results['database_video_url']}")
        
        # Success Summary
        print(f"\n📊 SUMMARY:")
        print(f"   ✅ Job Submission: {'✓' if self.job_id else '✗'}")
        print(f"   ✅ Job Completion: {'✓' if 'job_completion' in self.results else '✗'}")
        print(f"   ✅ Local Media: {'✓' if self.results.get('local_urls') else '✗'}")
        print(f"   ✅ UploadThing Upload: {'✓' if self.results.get('uploadthing_urls') else '✗'}")
        print(f"   ✅ Database Update: {'✓' if 'database_video_url' in self.results else '✗'}")
        
        print("=" * 80)
    
    async def run_complete_test(self):
        """Run the complete end-to-end test."""
        print("🚀 Starting End-to-End Render Test with UploadThing Integration")
        print("=" * 80)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Step 1: Health check
        if not await self.test_backend_health():
            self.log("Backend health check failed - aborting test", "ERROR")
            return False
        
        # Step 2: Submit job
        if not await self.submit_render_job():
            self.log("Job submission failed - aborting test", "ERROR")
            return False
        
        # Step 3: Monitor progress
        if not await self.monitor_job_progress():
            self.log("Job monitoring failed - test incomplete", "ERROR")
            self.print_final_results()
            return False
        
        # Step 4: Verify uploads
        upload_success = await self.verify_upload_results()
        
        # Step 5: Check database
        db_success = await self.verify_database_update()
        
        # Final results
        self.print_final_results()
        
        if upload_success and db_success:
            self.log("🎉 Complete end-to-end test PASSED!", "SUCCESS")
            return True
        else:
            self.log("❌ End-to-end test FAILED", "ERROR")
            return False


async def main():
    """Main test runner."""
    async with EndToEndTester() as tester:
        success = await tester.run_complete_test()
        return success


if __name__ == "__main__":
    print("🧪 End-to-End Render Test with UploadThing Integration")
    print("This script will test the complete workflow from POST request to UploadThing upload")
    print("Make sure both backend (port 8000) and frontend (port 3000) are running!")
    print()
    
    try:
        success = asyncio.run(main())
        exit_code = 0 if success else 1
        print(f"\n🏁 Test completed with exit code: {exit_code}")
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
