"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Entries } from "@prisma/client";
import {
  AlertCircle,
  Brain,
  CheckCircle2,
  Clock,
  Copy,
  Download,
  Edit,
  Facebook,
  Linkedin,
  Loader2,
  MessageSquare,
  MoreVertical,
  Play,
  Search,
  Share,
  ThumbsDown,
  ThumbsUp,
  Trash2,
  Twitter,
  VideoIcon,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { getUserEntries } from "@/actions/quiz.actions";
import { useUser } from "@civic/auth/react";
import { redirect } from "next/navigation";

export default function VideoLibrary() {
  const { user } = useUser();
  if (!user) {
    return redirect("/register");
  }
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("newest");
  const [filterBy, setFilterBy] = useState("all");
  const [loading, setLoading] = useState(false);
  const [entries, setEntries] = useState<Entries[]>([]);

  useEffect(() => {
    const getEntries = async () => {
      setLoading(true);
      const data = await getUserEntries(
        user.id,
      );
      if (data) {
        setEntries(data);
        setLoading(false);
      } else {
        toast.error("Failed to load your videos");
        setEntries([]);
        setLoading(false);
      }
    };
    getEntries();
  }, []);


  // const handleDeleteVideo = (id: number) => {
  //   setVideos(videos.filter((video) => video.id !== id));
  //   toast.success("Video deleted successfully");
  // };

  // const handleEditVideo = (updatedVideo: Video) => {
  //   setVideos(
  //     videos.map((video) =>
  //       video.id === updatedVideo.id ? updatedVideo : video
  //     )
  //   );
  //   setEditingVideo(null);
  //   toast.success("Video details updated successfully");
  // };

  // const handleLikeVideo = (id: number) => {
  //   setVideos(
  //     videos.map((video) =>
  //       video.id === id
  //         ? {
  //             ...video,
  //             isLiked: !video.isLiked,
  //             isDisliked: video.isLiked ? video.isDisliked : false,
  //             likes: video.isLiked ? video.likes - 1 : video.likes + 1,
  //             dislikes: video.isLiked
  //               ? video.dislikes
  //               : video.isDisliked
  //               ? video.dislikes - 1
  //               : video.dislikes,
  //           }
  //         : video
  //     )
  //   );
  // };

  // const handleDislikeVideo = (id: number) => {
  //   setVideos(
  //     videos.map((video) =>
  //       video.id === id
  //         ? {
  //             ...video,
  //             isDisliked: !video.isDisliked,
  //             isLiked: video.isDisliked ? video.isLiked : false,
  //             dislikes: video.isDisliked
  //               ? video.dislikes - 1
  //               : video.dislikes + 1,
  //             likes: video.isDisliked
  //               ? video.likes
  //               : video.isLiked
  //               ? video.likes - 1
  //               : video.likes,
  //           }
  //         : video
  //     )
  //   );
  // };

  // const formatDate = (dateString: string) => {
  //   const now = new Date();
  //   const date = new Date(dateString);
  //   const diffInHours = Math.floor(
  //     (now.getTime() - date.getTime()) / (1000 * 60 * 60)
  //   );

  //   if (diffInHours < 1) return "Just now";
  //   if (diffInHours < 24)
  //     return `${diffInHours} hour${diffInHours !== 1 ? "s" : ""} ago`;
  //   if (diffInHours < 168)
  //     return `${Math.floor(diffInHours / 24)} day${
  //       Math.floor(diffInHours / 24) !== 1 ? "s" : ""
  //     } ago`;
  //   if (diffInHours < 720)
  //     return `${Math.floor(diffInHours / 168)} week${
  //       Math.floor(diffInHours / 168) !== 1 ? "s" : ""
  //     } ago`;
  //   return date.toLocaleDateString("en-US", {
  //     year: "numeric",
  //     month: "short",
  //     day: "numeric",
  //   });
  // };

  const formatViews = (views: number) => {
    if (views === 0) return "No views";
    if (views < 1000) return `${views} view${views !== 1 ? "s" : ""}`;
    if (views < 1000000) return `${(views / 1000).toFixed(1)}K views`;
    return `${(views / 1000000).toFixed(1)}M views`;
  };

  // const getStatusIcon = (status: string) => {
  //   switch (status) {
  //     case "completed":
  //       return <CheckCircle2 className="h-4 w-4 text-green-500" />;
  //     case "generating":
  //       return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
  //     case "processing":
  //       return <Clock className="h-4 w-4 text-yellow-500" />;
  //     case "failed":
  //       return <AlertCircle className="h-4 w-4 text-red-500" />;
  //     default:
  //       return null;
  //   }
  // };

  return (
    <TooltipProvider>
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6 animate-fade-in">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold tracking-tight">Video Library</h1>
            <p className="text-muted-foreground">
              Manage and view all your AI-generated videos in one place. Use
              the search and filters to find specific videos quickly.
            </p>
          </div>
        </div>

        {/* Search and Filters - YouTube Style */}
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:space-x-4 lg:space-y-0">
          {/* Search Bar */}
          <div className="relative flex-1 max-w-full">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search your videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-10 bg-background border-border focus:border-primary rounded-full"
            />
          </div>

          {/* Filter and Sort Controls */}
          <div className="flex items-center space-x-2">
            <Select value={filterBy} onValueChange={setFilterBy}>
              <SelectTrigger className="w-[140px] h-10 rounded-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Styles</SelectItem>
                <SelectItem value="realistic">Realistic</SelectItem>
                <SelectItem value="artistic">Artistic</SelectItem>
                <SelectItem value="cinematic">Cinematic</SelectItem>
                <SelectItem value="animated">Animated</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[160px] h-10 rounded-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Upload date (newest)</SelectItem>
                <SelectItem value="oldest">Upload date (oldest)</SelectItem>
                <SelectItem value="views">View count</SelectItem>
                <SelectItem value="likes">Most liked</SelectItem>
                <SelectItem value="title">Title (A-Z)</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* YouTube-Style Video Grid */}
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
          {entries.map((video, index) => (
            <div
              key={video.id}
              className="group cursor-pointer animate-slide-up"
              style={{ animationDelay: `${index * 0.05}s` }}
            >
              {/* Video Thumbnail Container */}
              <div className="relative aspect-video mb-3 overflow-hidden rounded-xl bg-muted shadow-sm hover:shadow-md transition-shadow duration-200">
                <img
                  src="/placeholder.svg"
                  alt={video.quiz?.title}
                  className="h-full w-full object-cover transition-transform duration-200 group-hover:scale-105"
                />

                {/* Video Duration Overlay */}
                <div className="absolute bottom-2 right-2">
                  <Badge
                    variant="secondary"
                    className="bg-black/80 text-white border-0 text-xs font-medium px-2 py-1 rounded"
                  >
                    {/* Random between 1.30 mins to 3 mins */}
                    {Math.floor(Math.random() * 90 + 90)} s
                  </Badge>
                </div>

                {/* Status Indicators for Generating/Processing Videos */}
                {/* {(video.status === "generating" ||
                  video.status === "processing") && (
                  <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                    <div className="text-center text-white space-y-3 p-4">
                      <div className="flex items-center justify-center space-x-2">
                        {getStatusIcon(video.status)}
                        <span className="text-sm font-medium capitalize">
                          {video.status}...
                        </span>
                      </div>
                      <div className="w-32 mx-auto">
                        <Progress
                          value={video.progress || 0}
                          className="h-2 bg-white/20"
                        />
                      </div>
                      <div className="text-xs opacity-80">
                        {video.progress || 0}% complete
                      </div>
                    </div>
                  </div>
                )} */}

                {/* Status Icon for Completed Videos */}
                {/* {video.status === "completed" && (
                  <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {getStatusIcon(video.status)}
                  </div>
                )} */}

                {/* Play Button Overlay */}
                {/* {video.status === "completed" && (
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    <div className="bg-white/90 rounded-full p-4 transform scale-90 group-hover:scale-100 transition-transform duration-200 shadow-lg">
                      <Play className="h-6 w-6 text-black fill-current ml-0.5" />
                    </div>
                  </div>
                )} */}

                {/* Context Menu */}
                {/* <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 bg-black/60 hover:bg-black/80 text-white rounded-full"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="animate-scale-in"
                    >
                      <DropdownMenuItem disabled={video.status !== "completed"}>
                        <Play className="mr-2 h-4 w-4" />
                        Play
                      </DropdownMenuItem>
                      <DropdownMenuItem disabled={video.status !== "completed"}>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setEditingVideo(video)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit details
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDeleteVideo(video.id)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setSharingVideo(video)}>
                        <Share className="mr-2 h-4 w-4" />
                        Share
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div> */}
              </div>

              {/* Video Information - YouTube Style */}
              <div className="space-y-2">
                {/* Creator Avatar and Title */}
                <div className="flex space-x-3">
                  <div className="flex-1 min-w-0">
                    {/* Title */}
                    <h3 className="font-medium text-md leading-5 line-clamp-2 group-hover:text-primary transition-colors mb-1">
                      {video.quiz?.title || "Untitled Video"}
                    </h3>

                    {/* Views and Date */}
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <span>{formatViews(
                        Math.random() * 1000
                      )}</span>
                      {/* {video.views > 0 && (
                        <>
                          <span>•</span>
                          <span>{formatDate(video.createdAt)}</span>
                        </>
                      )} */}
                    </div>
                  </div>
                </div>

                {/* Description */}
                {video.quiz?.description && (
                  <p className="text-xs text-muted-foreground line-clamp-2 leading-4">
                    {video.quiz?.description}
                  </p>
                )}

                {/* Engagement and Metadata */}
                <div className="flex items-center justify-between">
                  {/* Engagement Stats */}
                  {/* {video.status === "completed" && video.views > 0 && (
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleLikeVideo(video.id);
                        }}
                        className={`flex items-center space-x-1 text-xs transition-colors ${
                          video.isLiked
                            ? "text-blue-600"
                            : "text-muted-foreground hover:text-blue-600"
                        }`}
                      >
                        <ThumbsUp
                          className={`h-3 w-3 ${
                            video.isLiked ? "fill-current" : ""
                          }`}
                        />
                        <span>{video.likes}</span>
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDislikeVideo(video.id);
                        }}
                        className={`flex items-center space-x-1 text-xs transition-colors ${
                          video.isDisliked
                            ? "text-red-600"
                            : "text-muted-foreground hover:text-red-600"
                        }`}
                      >
                        <ThumbsDown
                          className={`h-3 w-3 ${
                            video.isDisliked ? "fill-current" : ""
                          }`}
                        />
                        <span>{video.dislikes}</span>
                      </button>
                    </div>
                  )} */}

                  {/* Metadata Badges */}
                  <div className="flex items-center space-x-1">
                    <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                      {video.quiz?.questions.length || 0}
                    </Badge>
                  </div>
                </div>
                {/* Take Quiz Button */}
                <div className="mt-3">
                  <Button
                    size="sm"
                    disabled={video.hasGiven}
                    className={`w-full transition-all duration-200 ${
                      !video.hasGiven
                        ? "bg-muted text-muted-foreground cursor-not-allowed hover:bg-muted"
                        : "bg-primary text-primary-foreground hover:bg-primary/90"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!video.hasGiven) {
                        // Navigate to quiz page for this video
                        window.location.href = `/quiz/${video.id}`;
                      }
                    }}
                  >
                    <Brain className="h-3 w-3 mr-1" />
                    {!video.hasGiven
                      ? "Take Quiz"
                      : "Quiz Unavailable"}
                  </Button>
                </div>

                {/* Status for generating videos */}
                {/* {(video.status === "generating" ||
                  video.status === "processing") && (
                  <div className="flex items-center space-x-2 text-xs">
                    <Badge variant="secondary" className="text-xs">
                      {video.status === "generating"
                        ? "Generating"
                        : "Processing"}
                    </Badge>
                    <span className="text-muted-foreground">
                      {video.progress || 0}% complete
                    </span>
                  </div>
                )} */}
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {entries.length === 0 && (
          <div className="flex flex-col items-center justify-center py-16">
            <div className="text-center space-y-4">
              <div className="mx-auto h-20 w-20 rounded-full bg-muted flex items-center justify-center">
                <VideoIcon className="h-10 w-10 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">No videos found</h3>
                <p className="text-muted-foreground max-w-md">
                  {searchQuery
                    ? `No videos match "${searchQuery}". Try adjusting your search terms or filters.`
                    : "You haven't generated any videos yet. Create your first video to get started!"}
                </p>
              </div>
              {!searchQuery && (
                <Button asChild className="mt-4">
                  <a href="/generate">
                    <VideoIcon className="mr-2 h-4 w-4" />
                    Generate Your First Video
                  </a>
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Edit Video Modal */}
        {/* <Dialog
          open={!!editingVideo}
          onOpenChange={() => setEditingVideo(null)}
        >
          <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto animate-scale-in">
            <DialogHeader>
              <DialogTitle>Edit Video Details</DialogTitle>
              <DialogDescription>
                Update your video's title, description, and other metadata.
              </DialogDescription>
            </DialogHeader>
            {editingVideo && (
              <div className="grid gap-6 py-4">
                <div className="aspect-video w-full overflow-hidden rounded-lg bg-muted border">
                  <img
                    src={editingVideo.thumbnail || "/placeholder.svg"}
                    alt={editingVideo.title}
                    className="h-full w-full object-cover"
                  />
                </div>

                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="edit-title">Title</Label>
                    <Input
                      id="edit-title"
                      value={editingVideo.title}
                      onChange={(e) =>
                        setEditingVideo({
                          ...editingVideo,
                          title: e.target.value,
                        })
                      }
                      className="transition-colors focus:border-primary"
                      placeholder="Enter video title..."
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="edit-description">Description</Label>
                    <Textarea
                      id="edit-description"
                      value={editingVideo.description || ""}
                      onChange={(e) =>
                        setEditingVideo({
                          ...editingVideo,
                          description: e.target.value,
                        })
                      }
                      rows={4}
                      className="resize-none transition-colors focus:border-primary"
                      placeholder="Add a description for your video..."
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="edit-prompt">Original Prompt</Label>
                    <Textarea
                      id="edit-prompt"
                      value={editingVideo.prompt}
                      onChange={(e) =>
                        setEditingVideo({
                          ...editingVideo,
                          prompt: e.target.value,
                        })
                      }
                      rows={3}
                      className="resize-none transition-colors focus:border-primary"
                      placeholder="The prompt used to generate this video..."
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {formatViews(editingVideo.views)}
                      </div>
                      <div className="text-sm text-muted-foreground">Views</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {editingVideo.likes}
                      </div>
                      <div className="text-sm text-muted-foreground">Likes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {editingVideo.dislikes}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Dislikes
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingVideo(null)}>
                Cancel
              </Button>
              <Button
                onClick={() => editingVideo && handleEditVideo(editingVideo)}
              >
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog> */}
        
        {/* <Dialog
          open={!!sharingVideo}
          onOpenChange={() => setSharingVideo(null)}
        >
          <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto animate-scale-in">
            <DialogHeader>
              <DialogTitle>Share Video</DialogTitle>
              <DialogDescription>
                Share "{sharingVideo?.title}" with others
              </DialogDescription>
            </DialogHeader>
            {sharingVideo && (
              <div className="space-y-4">
                <div className="aspect-video w-full overflow-hidden rounded-lg bg-muted border">
                  <img
                    src={sharingVideo.thumbnail || "/placeholder.svg"}
                    alt={sharingVideo.title}
                    className="h-full w-full object-cover"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Share Link</Label>
                  <div className="flex space-x-2">
                    <Input
                      value={`https://clarif.ai/watch/${sharingVideo.id}`}
                      readOnly
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          `https://clarif.ai/watch/${sharingVideo.id}`
                        );
                        toast.success("Video link copied to clipboard!");
                      }}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  <Label>Share on Social Media</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="outline"
                      className="justify-start"
                      onClick={() => {
                        const url = `https://clarif.ai/watch/${sharingVideo.id}`;
                        const text = `Check out this amazing AI-generated video: ${sharingVideo.title}`;
                        window.open(
                          `https://twitter.com/intent/tweet?text=${encodeURIComponent(
                            text
                          )}&url=${encodeURIComponent(url)}`,
                          "_blank"
                        );
                      }}
                    >
                      <Twitter className="h-4 w-4 mr-2 text-blue-500" />
                      Twitter
                    </Button>
                    <Button
                      variant="outline"
                      className="justify-start"
                      onClick={() => {
                        const url = `https://clarif.ai/watch/${sharingVideo.id}`;
                        window.open(
                          `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
                            url
                          )}`,
                          "_blank"
                        );
                      }}
                    >
                      <Facebook className="h-4 w-4 mr-2 text-blue-600" />
                      Facebook
                    </Button>
                    <Button
                      variant="outline"
                      className="justify-start"
                      onClick={() => {
                        const url = `https://clarif.ai/watch/${sharingVideo.id}`;
                        const text = `Check out this AI-generated video: ${sharingVideo.title}`;
                        window.open(
                          `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
                            url
                          )}`,
                          "_blank"
                        );
                      }}
                    >
                      <Linkedin className="h-4 w-4 mr-2 text-blue-700" />
                      LinkedIn
                    </Button>
                    <Button
                      variant="outline"
                      className="justify-start"
                      onClick={() => {
                        const url = `https://clarif.ai/watch/${sharingVideo.id}`;
                        const text = `Check out this amazing AI-generated video: ${sharingVideo.title} ${url}`;
                        window.open(
                          `https://wa.me/?text=${encodeURIComponent(text)}`,
                          "_blank"
                        );
                      }}
                    >
                      <MessageSquare className="h-4 w-4 mr-2 text-green-600" />
                      WhatsApp
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Embed Code</Label>
                  <Textarea
                    value={`<iframe src="https://clarif.ai/embed/${sharingVideo.id}" width="560" height="315" frameborder="0" allowfullscreen></iframe>`}
                    readOnly
                    rows={3}
                    className="text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        `<iframe src="https://clarif.ai/embed/${sharingVideo.id}" width="560" height="315" frameborder="0" allowfullscreen></iframe>`
                      );
                      toast.success("Embed code copied to clipboard!");
                    }}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Embed Code
                  </Button>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setSharingVideo(null)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog> */}
      </div>
    </TooltipProvider>
  );
}
