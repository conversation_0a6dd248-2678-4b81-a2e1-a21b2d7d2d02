#!/usr/bin/env python3
"""
Simple POST Test for Render Endpoint with UploadThing Integration

This script demonstrates the simplest way to use the render endpoint:
1. POST to /render with entryId, script, and scene name
2. Get immediate response with job ID
3. Optionally monitor job until completion
4. Show final results including UploadThing URLs

Usage:
    python test_simple_render_post.py
"""

import requests
import json
import time
import uuid
from datetime import datetime


# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

# Simple test script
TEST_SCRIPT = """from manim import *

class SimpleTest(Scene):
    def construct(self):
        text = Text("Hello UploadThing!", font_size=48, color=BLUE)
        self.play(Write(text))
        self.wait(2)
        self.play(FadeOut(text))
"""


def post_render_request():
    """Send a POST request to the render endpoint."""
    print("🚀 Sending POST request to render endpoint...")
    
    # Generate a test entry ID
    entry_id = str(uuid.uuid4())
    print(f"📋 Generated Entry ID: {entry_id}")
    
    # Prepare the payload
    payload = {
        "script": TEST_SCRIPT,
        "scene_name": "SimpleTest",
        "priority": 1,
        "entry_id": entry_id
    }
    
    try:
        # Send POST request
        response = requests.post(
            f"{BACKEND_URL}/render",
            json=payload,
            timeout=30
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            job_id = data.get("job_id")
            
            print("✅ Request successful!")
            print(f"🆔 Job ID: {job_id}")
            print(f"📄 Response: {json.dumps(data, indent=2)}")
            
            return {
                "success": True,
                "job_id": job_id,
                "entry_id": entry_id,
                "response": data
            }
        else:
            print("❌ Request failed!")
            print(f"📄 Response: {response.text}")
            return {"success": False, "error": response.text}
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return {"success": False, "error": str(e)}


def check_job_status(job_id):
    """Check the status of a job."""
    try:
        response = requests.get(f"{BACKEND_URL}/jobs/{job_id}", timeout=10)
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}


def wait_for_completion(job_id, max_wait=300):
    """Wait for job completion and return final status."""
    print(f"\n⏳ Monitoring job {job_id} (max wait: {max_wait}s)...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        status_data = check_job_status(job_id)
        
        if "error" in status_data:
            print(f"❌ Error checking status: {status_data['error']}")
            time.sleep(5)
            continue
        
        status = status_data.get("status", "unknown")
        elapsed = int(time.time() - start_time)
        
        if status == "completed":
            print(f"✅ Job completed! (took {elapsed}s)")
            return status_data
        elif status == "failed":
            print(f"❌ Job failed! (after {elapsed}s)")
            return status_data
        elif status == "cancelled":
            print(f"⚠️ Job cancelled! (after {elapsed}s)")
            return status_data
        else:
            print(f"🔄 Status: {status} (elapsed: {elapsed}s)")
            time.sleep(5)
    
    print(f"⏰ Timeout reached after {max_wait}s")
    return {"status": "timeout", "last_check": status_data}


def analyze_results(job_data):
    """Analyze and display the final job results."""
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    if job_data.get("status") == "completed":
        result = job_data.get("result", {})
        output_urls = result.get("output_urls", [])
        
        print(f"✅ Job Status: {job_data.get('status')}")
        print(f"🕐 Completed At: {job_data.get('completed_at', 'Unknown')}")
        
        if output_urls:
            print(f"\n📎 Output URLs ({len(output_urls)}):")
            
            local_urls = []
            uploadthing_urls = []
            
            for url in output_urls:
                if url.startswith("/media/"):
                    local_urls.append(url)
                    print(f"   📁 Local: {url}")
                elif "uploadthing" in url.lower():
                    uploadthing_urls.append(url)
                    print(f"   🔗 UploadThing: {url}")
                else:
                    print(f"   🌐 Other: {url}")
            
            print(f"\n📈 Summary:")
            print(f"   Local files: {len(local_urls)}")
            print(f"   UploadThing URLs: {len(uploadthing_urls)}")
            
            if uploadthing_urls:
                print("✅ UploadThing integration working!")
            else:
                print("⚠️ No UploadThing URLs found")
                
        else:
            print("❌ No output URLs found")
    else:
        print(f"❌ Job Status: {job_data.get('status')}")
        if "error" in job_data:
            print(f"❌ Error: {job_data['error']}")
    
    print("=" * 60)


def main():
    """Main test function."""
    print("🧪 Simple Render POST Test")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Backend URL: {BACKEND_URL}")
    print()
    
    # Step 1: Send POST request
    result = post_render_request()
    
    if not result["success"]:
        print("❌ POST request failed - stopping test")
        return False
    
    job_id = result["job_id"]
    entry_id = result["entry_id"]
    
    print(f"\n✅ Job submitted successfully!")
    print(f"🆔 Job ID: {job_id}")
    print(f"📋 Entry ID: {entry_id}")
    
    # Ask user if they want to wait for completion
    print(f"\n❓ Do you want to wait for job completion? (y/n): ", end="")
    try:
        choice = input().lower().strip()
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted")
        return False
    
    if choice in ['y', 'yes']:
        # Step 2: Wait for completion
        final_data = wait_for_completion(job_id)
        
        # Step 3: Analyze results
        analyze_results(final_data)
        
        return final_data.get("status") == "completed"
    else:
        print(f"\n📝 Job submitted! You can check status manually:")
        print(f"   GET {BACKEND_URL}/jobs/{job_id}")
        print(f"   Entry ID: {entry_id}")
        return True


if __name__ == "__main__":
    print("🔧 Make sure your backend is running on http://localhost:8000")
    print("🔧 Make sure your frontend is running on http://localhost:3000")
    print()
    
    try:
        success = main()
        if success:
            print("\n🎉 Test completed successfully!")
        else:
            print("\n❌ Test failed!")
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
