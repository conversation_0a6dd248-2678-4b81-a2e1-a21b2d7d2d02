#!/usr/bin/env python3
"""
Batch Render POST Test with UploadThing Integration

This script tests the batch render endpoint:
1. POST to /batch_render with multiple scripts and entryId
2. Monitor job progress until completion
3. Show merged video upload to UploadThing

Usage:
    python test_batch_render_post.py
"""

import requests
import json
import time
import uuid
from datetime import datetime


# Configuration
BACKEND_URL = "http://localhost:8000"

# Multiple test scripts for batch rendering
BATCH_SCRIPTS = [
    {
        "script": """from manim import *

class Scene1(Scene):
    def construct(self):
        title = Text("Scene 1: Introduction", font_size=36, color=BLUE)
        self.play(Write(title))
        self.wait(1)
        
        circle = Circle(radius=1, color=GREEN)
        circle.shift(DOWN)
        self.play(Create(circle))
        self.wait(1)
        
        self.play(FadeOut(Group(*self.mobjects)))
""",
        "scene_name": "Scene1"
    },
    {
        "script": """from manim import *

class Scene2(Scene):
    def construct(self):
        title = Text("Scene 2: Animation", font_size=36, color=RED)
        self.play(Write(title))
        self.wait(1)
        
        square = Square(side_length=2, color=YELLOW)
        square.shift(DOWN)
        self.play(Create(square))
        
        # Rotate the square
        self.play(Rotate(square, PI/2))
        self.wait(1)
        
        self.play(FadeOut(Group(*self.mobjects)))
""",
        "scene_name": "Scene2"
    },
    {
        "script": """from manim import *

class Scene3(Scene):
    def construct(self):
        title = Text("Scene 3: Conclusion", font_size=36, color=PURPLE)
        self.play(Write(title))
        self.wait(1)
        
        triangle = Triangle(color=ORANGE)
        triangle.shift(DOWN)
        self.play(Create(triangle))
        
        # Scale animation
        self.play(triangle.animate.scale(1.5))
        self.wait(1)
        
        final_text = Text("Batch Render Complete!", font_size=24, color=GREEN)
        final_text.shift(DOWN * 2)
        self.play(Write(final_text))
        self.wait(2)
        
        self.play(FadeOut(Group(*self.mobjects)))
""",
        "scene_name": "Scene3"
    }
]


def post_batch_render_request():
    """Send a POST request to the batch render endpoint."""
    print("🚀 Sending POST request to batch render endpoint...")
    
    # Generate a test entry ID
    entry_id = str(uuid.uuid4())
    print(f"📋 Generated Entry ID: {entry_id}")
    print(f"📝 Number of scenes: {len(BATCH_SCRIPTS)}")
    
    # Add entry_id to each script
    scripts_with_entry = []
    for script_data in BATCH_SCRIPTS:
        script_with_entry = script_data.copy()
        script_with_entry["entry_id"] = entry_id
        scripts_with_entry.append(script_with_entry)
    
    # Prepare the payload
    payload = {
        "scripts": scripts_with_entry,
        "priority": 1,
        "entry_id": entry_id
    }
    
    try:
        # Send POST request
        print("📡 Sending request...")
        response = requests.post(
            f"{BACKEND_URL}/batch_render",
            json=payload,
            timeout=30
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            job_id = data.get("job_id")
            
            print("✅ Batch render request successful!")
            print(f"🆔 Job ID: {job_id}")
            print(f"📄 Response: {json.dumps(data, indent=2)}")
            
            return {
                "success": True,
                "job_id": job_id,
                "entry_id": entry_id,
                "response": data
            }
        else:
            print("❌ Request failed!")
            print(f"📄 Response: {response.text}")
            return {"success": False, "error": response.text}
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return {"success": False, "error": str(e)}


def check_job_status(job_id):
    """Check the status of a batch job."""
    try:
        response = requests.get(f"{BACKEND_URL}/jobs/{job_id}", timeout=10)
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        return {"error": str(e)}


def wait_for_batch_completion(job_id, max_wait=600):
    """Wait for batch job completion (longer timeout for multiple scenes)."""
    print(f"\n⏳ Monitoring batch job {job_id} (max wait: {max_wait}s)...")
    print("📝 Note: Batch rendering takes longer as it processes multiple scenes")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        status_data = check_job_status(job_id)
        
        if "error" in status_data:
            print(f"❌ Error checking status: {status_data['error']}")
            time.sleep(10)
            continue
        
        status = status_data.get("status", "unknown")
        elapsed = int(time.time() - start_time)
        
        # Only print status changes to reduce noise
        if status != last_status:
            print(f"🔄 Status changed to: {status} (elapsed: {elapsed}s)")
            last_status = status
        
        if status == "completed":
            print(f"✅ Batch job completed! (took {elapsed}s)")
            return status_data
        elif status == "failed":
            print(f"❌ Batch job failed! (after {elapsed}s)")
            return status_data
        elif status == "cancelled":
            print(f"⚠️ Batch job cancelled! (after {elapsed}s)")
            return status_data
        else:
            # Show progress every 30 seconds for long-running jobs
            if elapsed % 30 == 0 and elapsed > 0:
                print(f"🕐 Still processing... (elapsed: {elapsed}s, status: {status})")
            time.sleep(10)  # Check every 10 seconds for batch jobs
    
    print(f"⏰ Timeout reached after {max_wait}s")
    return {"status": "timeout", "last_check": status_data}


def analyze_batch_results(job_data):
    """Analyze and display the batch job results."""
    print("\n" + "=" * 70)
    print("📊 BATCH RENDER RESULTS")
    print("=" * 70)
    
    if job_data.get("status") == "completed":
        result = job_data.get("result", {})
        output_urls = result.get("output_urls", [])
        
        print(f"✅ Job Status: {job_data.get('status')}")
        print(f"🕐 Completed At: {job_data.get('completed_at', 'Unknown')}")
        print(f"📝 Scenes Processed: {len(BATCH_SCRIPTS)}")
        
        if output_urls:
            print(f"\n📎 Output URLs ({len(output_urls)}):")
            
            local_urls = []
            uploadthing_urls = []
            
            for url in output_urls:
                if url.startswith("/media/"):
                    local_urls.append(url)
                    print(f"   📁 Local: {url}")
                elif "uploadthing" in url.lower():
                    uploadthing_urls.append(url)
                    print(f"   🔗 UploadThing: {url}")
                else:
                    print(f"   🌐 Other: {url}")
            
            print(f"\n📈 Summary:")
            print(f"   Local files: {len(local_urls)}")
            print(f"   UploadThing URLs: {len(uploadthing_urls)}")
            
            if uploadthing_urls:
                print("✅ Batch render + UploadThing integration working!")
                print("📹 The merged video from all scenes has been uploaded!")
            else:
                print("⚠️ No UploadThing URLs found")
                print("🔍 Check UploadThing configuration and logs")
                
        else:
            print("❌ No output URLs found")
            
        # Additional batch-specific info
        if "processing_time" in result:
            print(f"⏱️ Processing Time: {result['processing_time']}s")
            
    else:
        print(f"❌ Job Status: {job_data.get('status')}")
        if "error" in job_data:
            print(f"❌ Error: {job_data['error']}")
        
        # Show partial results if available
        if "result" in job_data:
            result = job_data["result"]
            if "partial_outputs" in result:
                print(f"📋 Partial outputs: {result['partial_outputs']}")
    
    print("=" * 70)


def main():
    """Main batch test function."""
    print("🧪 Batch Render POST Test with UploadThing Integration")
    print("=" * 70)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Backend URL: {BACKEND_URL}")
    print()
    
    # Show what we're about to test
    print("📝 Test Configuration:")
    print(f"   Number of scenes: {len(BATCH_SCRIPTS)}")
    for i, script in enumerate(BATCH_SCRIPTS, 1):
        print(f"   Scene {i}: {script['scene_name']}")
    print()
    
    # Step 1: Send POST request
    result = post_batch_render_request()
    
    if not result["success"]:
        print("❌ POST request failed - stopping test")
        return False
    
    job_id = result["job_id"]
    entry_id = result["entry_id"]
    
    print(f"\n✅ Batch job submitted successfully!")
    print(f"🆔 Job ID: {job_id}")
    print(f"📋 Entry ID: {entry_id}")
    
    # Ask user if they want to wait for completion
    print(f"\n❓ Do you want to wait for batch job completion? (y/n): ", end="")
    try:
        choice = input().lower().strip()
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted")
        return False
    
    if choice in ['y', 'yes']:
        # Step 2: Wait for completion (longer timeout for batch jobs)
        final_data = wait_for_batch_completion(job_id, max_wait=600)
        
        # Step 3: Analyze results
        analyze_batch_results(final_data)
        
        return final_data.get("status") == "completed"
    else:
        print(f"\n📝 Batch job submitted! You can check status manually:")
        print(f"   GET {BACKEND_URL}/jobs/{job_id}")
        print(f"   Entry ID: {entry_id}")
        print("⚠️ Note: Batch jobs take longer to complete (multiple scenes + merging)")
        return True


if __name__ == "__main__":
    print("🔧 Make sure your backend is running on http://localhost:8000")
    print("🔧 Make sure your frontend is running on http://localhost:3000")
    print("⚠️ Batch rendering takes longer - be patient!")
    print()
    
    try:
        success = main()
        if success:
            print("\n🎉 Batch test completed successfully!")
        else:
            print("\n❌ Batch test failed!")
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
