{"name": "next-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@ai-sdk/cerebras": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@civic/auth": "^0.8.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@prisma/client": "^6.10.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tanstack/react-table": "^8.21.3", "@types/react-syntax-highlighter": "^15.5.13", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "lucide-react": "^0.522.0", "next": "^15.4.0-canary.90", "next-themes": "^0.4.6", "prisma": "^6.10.1", "react": "19.1.0", "react-dom": "19.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^24.0.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}}