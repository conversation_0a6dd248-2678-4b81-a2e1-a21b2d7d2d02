#!/usr/bin/env python3
"""
Test script for UploadThing API v6/v7 integration.

This script tests the updated UploadThing service with the correct API endpoints.
It creates a test video file in the media folder and attempts to upload it.

Usage:
    python test_uploadthing_api.py
"""

import os
import tempfile
import time
from datetime import datetime

from services.uploadthing_service import get_uploadthing_service


def create_test_video_in_media_folder():
    """Create a test video file in the media folder structure."""
    # Create media folder structure like the real system
    job_id = f"test-{int(time.time())}"
    media_dir = os.path.join("media", job_id)
    os.makedirs(media_dir, exist_ok=True)
    
    # Create a test video file
    video_filename = "TestScene.mp4"
    video_path = os.path.join(media_dir, video_filename)
    
    # Create a fake MP4 file with some content
    fake_mp4_header = b'\x00\x00\x00\x20ftypmp41\x00\x00\x00\x00mp41isom'
    fake_content = fake_mp4_header + b'A' * 1024  # 1KB of fake video data
    
    with open(video_path, 'wb') as f:
        f.write(fake_content)
    
    print(f"✅ Created test video: {video_path} ({len(fake_content)} bytes)")
    return video_path, media_dir


def test_uploadthing_service():
    """Test the UploadThing service with v6/v7 API."""
    print("🧪 Testing UploadThing API v6/v7 Integration")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check environment variables
    uploadthing_secret = os.getenv("UPLOADTHING_SECRET")
    if not uploadthing_secret or uploadthing_secret == "your_uploadthing_secret_key":
        print("❌ UPLOADTHING_SECRET not configured")
        print("Please set your UploadThing API key in the .env file")
        return False
    
    print(f"✅ UploadThing secret configured: {uploadthing_secret[:10]}...")
    
    # Create test video file
    try:
        video_path, media_dir = create_test_video_in_media_folder()
    except Exception as e:
        print(f"❌ Failed to create test video: {e}")
        return False
    
    # Test UploadThing service
    try:
        print("\n📤 Testing UploadThing upload...")
        service = get_uploadthing_service()
        
        # Attempt upload
        result_url = service.upload_video(video_path)
        
        if result_url:
            print(f"✅ Upload successful!")
            print(f"📎 Video URL: {result_url}")
            
            # Verify URL format
            if "uploadthing" in result_url.lower():
                print("✅ URL format looks correct")
            else:
                print("⚠️  URL format unexpected")
            
            return True
        else:
            print("❌ Upload failed - no URL returned")
            return False
            
    except Exception as e:
        print(f"❌ Upload failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test files
        try:
            if os.path.exists(video_path):
                os.unlink(video_path)
                print(f"🧹 Cleaned up test video: {video_path}")
            if os.path.exists(media_dir):
                os.rmdir(media_dir)
                print(f"🧹 Cleaned up test directory: {media_dir}")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")


def test_api_endpoints():
    """Test individual API endpoints."""
    print("\n🔍 Testing API Endpoint Configuration")
    print("-" * 40)
    
    service = get_uploadthing_service()
    
    # Test configuration
    print(f"API Base URL: {service.config.uploadthing_api_url}")
    print(f"Secret configured: {'Yes' if service.config.uploadthing_secret else 'No'}")
    
    # Test prepare upload endpoint
    try:
        # Create a small test file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_file.write(b'test content')
            temp_path = temp_file.name
        
        try:
            print("\n📋 Testing v7/prepareUpload endpoint...")
            prepare_response = service._prepare_upload("test.mp4", temp_path)
            
            if prepare_response:
                print("✅ Prepare upload successful")
                print(f"Response keys: {list(prepare_response.keys())}")
                
                # Check for expected fields
                expected_fields = ["key", "presignedUrl"]
                for field in expected_fields:
                    if field in prepare_response:
                        print(f"✅ Found expected field: {field}")
                    else:
                        print(f"⚠️  Missing field: {field}")
            else:
                print("❌ Prepare upload failed")
                
        finally:
            os.unlink(temp_path)
            
    except Exception as e:
        print(f"❌ Prepare upload test failed: {e}")


def main():
    """Main test runner."""
    print("🔧 UploadThing API v6/v7 Test Suite")
    print("This script tests the updated UploadThing integration")
    print("Make sure you have configured UPLOADTHING_SECRET in your .env file")
    print()
    
    # Test API endpoints first
    test_api_endpoints()
    
    # Test full upload flow
    success = test_uploadthing_service()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 UploadThing integration test PASSED!")
        print("Your videos from the media folder will be uploaded correctly.")
    else:
        print("❌ UploadThing integration test FAILED!")
        print("Please check your configuration and try again.")
    print("=" * 60)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
