import * as React from "react"
import { Card } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface EnhancedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  hover?: boolean
  gradient?: boolean
  glass?: boolean
}

const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({ className, hover = false, gradient = false, glass = false, ...props }, ref) => (
    <Card
      ref={ref}
      className={cn(
        "transition-all duration-200 py-3",
        hover && "hover:shadow-lg hover:-translate-y-1 cursor-pointer",
        gradient && "gradient-bg text-white",
        glass && "glass-effect",
        className,
      )}
      {...props}
    />
  ),
)
EnhancedCard.displayName = "EnhancedCard"

export { EnhancedCard }
