import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import { CivicAuthProvider } from "@civic/auth/nextjs";
import { ModeToggle } from "@/components/ui/mode-toggle";
import { Toaster } from "@/components/ui/sonner";

const font = Geist({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "ClarifAI",
  description: "Get detailed in depth animated explanations for any topic.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={font.className}>
        <CivicAuthProvider>
          <ThemeProvider attribute="class">{children}
            <Toaster />
          </ThemeProvider>
        </CivicAuthProvider>
      </body>
    </html>
  );
}
