{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/recent-videos.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RecentVideosTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call RecentVideosTable() from the server but RecentVideosTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/recent-videos.tsx <module evaluation>\",\n    \"RecentVideosTable\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,kEACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/recent-videos.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RecentVideosTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call RecentVideosTable() from the server but RecentVideosTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/recent-videos.tsx\",\n    \"RecentVideosTable\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8CACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\r\n        destructive:\r\n          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\r\n        outline: 'text-foreground',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  },\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/components/section-cards.tsx"], "sourcesContent": ["import { IconTrendingUp, IconPlus, IconClock } from \"@tabler/icons-react\"\r\n\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport {\r\n  Card,\r\n  CardAction,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\"\r\n\r\nexport function SectionCards() {\r\n  return (\r\n    <div className=\"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs lg:px-6 @xl/main:grid-cols-2 @5xl/main:grid-cols-4\">\r\n      \r\n      {/* Card 1: Videos Generated */}\r\n      <Card className=\"@container/card\">\r\n        <CardHeader>\r\n          <CardDescription>Videos Generated</CardDescription>\r\n          <CardTitle className=\"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl\">\r\n            5\r\n          </CardTitle>\r\n          <CardAction>\r\n            <Badge variant=\"outline\" className=\"flex items-center gap-2\">\r\n              <IconTrendingUp className=\"size-6\"/>\r\n              +20.1%\r\n            </Badge>\r\n          </CardAction>\r\n        </CardHeader>\r\n        <CardFooter className=\"flex-col items-start gap-1.5 text-sm\">\r\n          <div className=\"line-clamp-1 flex gap-2 font-medium\">\r\n            From yesterday <IconTrendingUp className=\"size-4\" />\r\n          </div>\r\n          <div className=\"text-muted-foreground\">\r\n            Improved video creation rate\r\n          </div>\r\n        </CardFooter>\r\n      </Card>\r\n\r\n      {/* Card 2: Total Videos */}\r\n      <Card className=\"@container/card\">\r\n        <CardHeader>\r\n          <CardDescription>Total Videos</CardDescription>\r\n          <CardTitle className=\"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl\">\r\n            10\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardFooter className=\"flex-col items-start gap-1.5 text-sm\">\r\n          <div className=\"line-clamp-1 flex gap-2 font-medium\">\r\n            In your library <IconPlus className=\"size-4\" />\r\n          </div>\r\n          <div className=\"text-muted-foreground\">\r\n            Saved and ready to view anytime\r\n          </div>\r\n        </CardFooter>\r\n      </Card>\r\n\r\n      {/* Card 3: Quizzes Created */}\r\n      <Card className=\"@container/card\">\r\n        <CardHeader>\r\n          <CardDescription>Quizzes Created</CardDescription>\r\n          <CardTitle className=\"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl\">\r\n            3\r\n          </CardTitle>\r\n          <CardAction>\r\n            <Badge variant=\"outline\" className=\"flex items-center gap-2\">\r\n              <IconPlus className=\"size-6\"/>\r\n              +2 today\r\n            </Badge>\r\n          </CardAction>\r\n        </CardHeader>\r\n        <CardFooter className=\"flex-col items-start gap-1.5 text-sm\">\r\n          <div className=\"line-clamp-1 flex gap-2 font-medium\">\r\n            Active content creation <IconPlus className=\"size-4\" />\r\n          </div>\r\n          <div className=\"text-muted-foreground\">\r\n            Great progress this session\r\n          </div>\r\n        </CardFooter>\r\n      </Card>\r\n\r\n      {/* Card 4: Generation Time */}\r\n      <Card className=\"@container/card\">\r\n        <CardHeader>\r\n          <CardDescription>Generation Time</CardDescription>\r\n          <CardTitle className=\"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl\">\r\n            1m\r\n          </CardTitle>\r\n          <CardAction>\r\n            <Badge variant=\"outline\" className=\"flex items-center gap-2\">\r\n              <IconClock className=\"size-6\" />\r\n              avg/video\r\n            </Badge>\r\n          </CardAction>\r\n        </CardHeader>\r\n        <CardFooter className=\"flex-col items-start gap-1.5 text-sm\">\r\n          <div className=\"line-clamp-1 flex gap-2 font-medium\">\r\n            Efficient performance <IconClock className=\"size-4\" />\r\n          </div>\r\n          <div className=\"text-muted-foreground\">\r\n            Based on last 10 generations\r\n          </div>\r\n        </CardFooter>\r\n      </Card>\r\n\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAEA;AACA;;;;;AASO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAGb,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;0CACjB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA6D;;;;;;0CAGlF,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,8OAAC,kOAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;wCAAU;;;;;;;;;;;;;;;;;;kCAK1C,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;;oCAAsC;kDACpC,8OAAC,kOAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;0CAE3C,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;0CACjB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA6D;;;;;;;;;;;;kCAIpF,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;;oCAAsC;kDACnC,8OAAC,sNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;0CACjB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA6D;;;;;;0CAGlF,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,8OAAC,sNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAU;;;;;;;;;;;;;;;;;;kCAKpC,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;;oCAAsC;kDAC3B,8OAAC,sNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAE9C,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;0CACjB,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA6D;;;;;;0CAGlF,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,8OAAC,wNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAW;;;;;;;;;;;;;;;;;;kCAKtC,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC;gCAAI,WAAU;;oCAAsC;kDAC7B,8OAAC,wNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;0CAE7C,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/app/%28app%29/dashboard/page.tsx"], "sourcesContent": ["import { RecentVideosTable } from \"@/components/recent-videos\";\r\nimport { SectionCards } from \"@/components/section-cards\";\r\n\r\nexport default function Page() {\r\n  return (\r\n    <div className=\"flex flex-1 flex-col\">\r\n      <div className=\"@container/main flex flex-1 flex-col gap-2\">\r\n        <div className=\"flex flex-col gap-4 py-4 md:gap-6 md:py-6\">\r\n          <SectionCards />\r\n\r\n          {/* Recent Videos Section */}\r\n          <div className=\"px-4 lg:px-6 space-y-4\">\r\n            <RecentVideosTable />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,eAAY;;;;;kCAGb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sIAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}]}