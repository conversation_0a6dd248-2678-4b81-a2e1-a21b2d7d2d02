import os
import logging
import requests
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)


class UploadThingConfig(BaseSettings):
    """Configuration for UploadThing service."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    uploadthing_secret: str = Field(default="", description="UploadThing secret key")
    uploadthing_app_id: str = Field(default="", description="UploadThing app ID")
    uploadthing_api_url: str = Field(
        default="https://api.uploadthing.com", description="UploadThing API base URL"
    )


class UploadThingService:
    """Service for uploading files to UploadThing."""

    def __init__(self):
        self.config = UploadThingConfig()
        self.session = requests.Session()

        # Set up headers for API requests
        if self.config.uploadthing_secret:
            self.session.headers.update(
                {
                    "x-uploadthing-api-key": self.config.uploadthing_secret,
                    "Content-Type": "application/json",
                }
            )

    def upload_video(
        self, video_path: str, filename: Optional[str] = None
    ) -> Optional[str]:
        """
        Upload a video file to UploadThing and return the URL.

        Args:
            video_path: Path to the video file to upload
            filename: Optional custom filename (defaults to original filename)

        Returns:
            The URL of the uploaded video, or None if upload failed
        """
        try:
            if not os.path.exists(video_path):
                logger.error(f"Video file not found: {video_path}")
                return None

            if not self.config.uploadthing_secret:
                logger.error("UploadThing secret not configured")
                return None

            # Use original filename if not provided
            if not filename:
                filename = os.path.basename(video_path)

            # Step 1: Prepare upload using v7 API
            prepare_response = self._prepare_upload(filename, video_path)
            if not prepare_response:
                return None

            # Step 2: Upload file using v6 API
            upload_success = self._upload_files(prepare_response, video_path)
            if not upload_success:
                return None

            # Step 3: Get the final URL from the upload response
            final_url = upload_success.get("url")
            if not final_url:
                logger.error("No URL returned from upload")
                return None

            if final_url:
                logger.info(f"Successfully uploaded video to UploadThing: {final_url}")
                return final_url
            else:
                logger.error("Failed to complete upload to UploadThing")
                return None

        except Exception as e:
            logger.error(f"Error uploading video to UploadThing: {e}")
            return None

    def _prepare_upload(self, filename: str, video_path: str) -> Optional[dict]:
        """Prepare upload using v7 API."""
        try:
            url = f"{self.config.uploadthing_api_url}/v7/prepareUpload"

            # Get file size
            file_size = os.path.getsize(video_path)

            payload = {
                "files": [{"name": filename, "type": "video/mp4", "size": file_size}]
            }

            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            if data and len(data) > 0:
                return data[0]

            return None

        except Exception as e:
            logger.error(f"Error preparing upload: {e}")
            return None

    def _upload_files(self, prepare_response: dict, video_path: str) -> Optional[dict]:
        """Upload files using v6 API."""
        try:
            url = f"{self.config.uploadthing_api_url}/v6/uploadFiles"

            # Get upload details from prepare response
            upload_url = prepare_response.get("presignedUrl", {}).get("url")
            fields = prepare_response.get("presignedUrl", {}).get("fields", {})
            file_key = prepare_response.get("key")

            if not upload_url or not file_key:
                logger.error("Invalid prepare response - missing upload URL or key")
                return None

            # Upload file to presigned URL
            with open(video_path, "rb") as file:
                # Create form data with required fields
                files = {"file": (os.path.basename(video_path), file, "video/mp4")}
                data = fields  # Include any required form fields

                # Use a separate session without API key for S3 upload
                upload_session = requests.Session()
                response = upload_session.post(upload_url, files=files, data=data)
                response.raise_for_status()

            # Now call the uploadFiles endpoint to finalize
            payload = {
                "files": [
                    {
                        "key": file_key,
                        "name": os.path.basename(video_path),
                        "size": os.path.getsize(video_path),
                    }
                ]
            }

            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            if data and len(data) > 0:
                return data[0]

            return None

        except Exception as e:
            logger.error(f"Error uploading files: {e}")
            return None


# Global service instance
_uploadthing_service: Optional[UploadThingService] = None


def get_uploadthing_service() -> UploadThingService:
    """Get the global UploadThing service instance."""
    global _uploadthing_service
    if _uploadthing_service is None:
        _uploadthing_service = UploadThingService()
    return _uploadthing_service
