import os
import logging
import requests
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)


class UploadThingConfig(BaseSettings):
    """Configuration for UploadThing service."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    uploadthing_secret: str = Field(default="", description="UploadThing secret key")
    uploadthing_app_id: str = Field(default="", description="UploadThing app ID")
    uploadthing_api_url: str = Field(
        default="https://api.uploadthing.com", description="UploadThing API base URL"
    )


class UploadThingService:
    """Service for uploading files to UploadThing."""

    def __init__(self):
        self.config = UploadThingConfig()
        self.session = requests.Session()

        # Set up headers for API requests
        if self.config.uploadthing_secret:
            self.session.headers.update(
                {
                    "X-Uploadthing-Api-Key": self.config.uploadthing_secret,
                    "Content-Type": "application/json",
                }
            )

    def upload_video(
        self, video_path: str, filename: Optional[str] = None
    ) -> Optional[str]:
        """
        Upload a video file to UploadThing and return the URL.

        Args:
            video_path: Path to the video file to upload
            filename: Optional custom filename (defaults to original filename)

        Returns:
            The URL of the uploaded video, or None if upload failed
        """
        try:
            if not os.path.exists(video_path):
                logger.error(f"Video file not found: {video_path}")
                return None

            if not self.config.uploadthing_secret:
                logger.error("UploadThing secret not configured")
                return None

            # Use original filename if not provided
            if not filename:
                filename = os.path.basename(video_path)

            # Step 1: Request upload URL from UploadThing
            upload_url_response = self._request_upload_url(filename)
            if not upload_url_response:
                return None

            upload_url = upload_url_response.get("url")
            file_key = upload_url_response.get("key")

            if not upload_url or not file_key:
                logger.error("Failed to get upload URL from UploadThing")
                return None

            # Step 2: Upload file to the provided URL
            upload_success = self._upload_file_to_url(video_path, upload_url)
            if not upload_success:
                return None

            # Step 3: Complete the upload and get the final URL
            final_url = self._complete_upload(file_key)

            if final_url:
                logger.info(f"Successfully uploaded video to UploadThing: {final_url}")
                return final_url
            else:
                logger.error("Failed to complete upload to UploadThing")
                return None

        except Exception as e:
            logger.error(f"Error uploading video to UploadThing: {e}")
            return None

    def _request_upload_url(self, filename: str) -> Optional[dict]:
        """Request an upload URL from UploadThing."""
        try:
            url = f"{self.config.uploadthing_api_url}/api/requestFileUpload"

            payload = {"files": [{"name": filename, "type": "video/mp4"}]}

            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            if data and len(data) > 0:
                return data[0]

            return None

        except Exception as e:
            logger.error(f"Error requesting upload URL: {e}")
            return None

    def _upload_file_to_url(self, video_path: str, upload_url: str) -> bool:
        """Upload the file to the provided URL."""
        try:
            with open(video_path, "rb") as file:
                # Create a new session for the file upload (without API key headers)
                upload_session = requests.Session()

                files = {"file": (os.path.basename(video_path), file, "video/mp4")}
                response = upload_session.post(upload_url, files=files)
                response.raise_for_status()

                return True

        except Exception as e:
            logger.error(f"Error uploading file to URL: {e}")
            return False

    def _complete_upload(self, file_key: str) -> Optional[str]:
        """Complete the upload and get the final URL."""
        try:
            url = f"{self.config.uploadthing_api_url}/api/completeUpload"

            payload = {"fileKeys": [file_key]}

            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            if data and len(data) > 0:
                return data[0].get("url")

            return None

        except Exception as e:
            logger.error(f"Error completing upload: {e}")
            return None


# Global service instance
_uploadthing_service: Optional[UploadThingService] = None


def get_uploadthing_service() -> UploadThingService:
    """Get the global UploadThing service instance."""
    global _uploadthing_service
    if _uploadthing_service is None:
        _uploadthing_service = UploadThingService()
    return _uploadthing_service
