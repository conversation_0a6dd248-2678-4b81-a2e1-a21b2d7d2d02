import os
import logging
import requests
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

# Try to import the official UploadThing Python SDK
try:
    from uploadthing_py import UTApi

    UPLOADTHING_SDK_AVAILABLE = True
except ImportError:
    UPLOADTHING_SDK_AVAILABLE = False
    UTApi = None

logger = logging.getLogger(__name__)


class UploadThingConfig(BaseSettings):
    """Configuration for UploadThing service."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    uploadthing_secret: str = Field(default="", description="UploadThing secret key")
    uploadthing_app_id: str = Field(default="", description="UploadThing app ID")
    uploadthing_api_url: str = Field(
        default="https://api.uploadthing.com", description="UploadThing API base URL"
    )


class UploadThingService:
    """Service for uploading files to UploadThing."""

    def __init__(self):
        self.config = UploadThingConfig()
        self.session = requests.Session()

        # Initialize the official SDK if available
        self.utapi = None
        if UPLOADTHING_SDK_AVAILABLE and self.config.uploadthing_secret and UTApi is not None:
            try:
                self.utapi = UTApi(self.config.uploadthing_secret)
                logger.info("UploadThing official SDK initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize UploadThing SDK: {e}")
                self.utapi = None

        # Set up headers for manual API requests (fallback)
        if self.config.uploadthing_secret:
            self.session.headers.update(
                {
                    "x-uploadthing-api-key": self.config.uploadthing_secret,
                    "Content-Type": "application/json",
                }
            )

    def upload_video(
        self, video_path: str, filename: Optional[str] = None
    ) -> Optional[str]:
        """
        Upload a video file to UploadThing and return the URL.

        Args:
            video_path: Path to the video file to upload
            filename: Optional custom filename (defaults to original filename)

        Returns:
            The URL of the uploaded video, or None if upload failed
        """
        try:
            if not os.path.exists(video_path):
                logger.error(f"Video file not found: {video_path}")
                return None

            if not self.config.uploadthing_secret:
                logger.error("UploadThing secret not configured")
                return None

            # Use original filename if not provided
            if not filename:
                filename = os.path.basename(video_path)

            # Try using the official SDK first
            if self.utapi:
                logger.info("Attempting upload using UploadThing official SDK...")
                try:
                    result = self._upload_with_sdk(video_path, filename)
                    if result:
                        logger.info(f"Successfully uploaded video using SDK: {result}")
                        return result
                    else:
                        logger.warning(
                            "SDK upload failed, falling back to manual implementation"
                        )
                except Exception as e:
                    logger.warning(
                        f"SDK upload failed: {e}, falling back to manual implementation"
                    )

            # Fallback to manual implementation
            logger.info("Using manual UploadThing API implementation...")
            return self._upload_with_manual_api(video_path, filename)

        except Exception as e:
            logger.error(f"Error uploading video to UploadThing: {e}")
            return None

    def _upload_with_sdk(self, video_path: str, filename: str) -> Optional[str]:
        """Upload using the official UploadThing Python SDK."""
        try:
            # Note: The SDK's upload_files method might not be fully implemented yet
            # This is a placeholder for when it becomes available
            logger.warning("UploadThing SDK upload_files method not yet implemented")
            return None
        except Exception as e:
            logger.error(f"SDK upload failed: {e}")
            return None

    def _upload_with_manual_api(self, video_path: str, filename: str) -> Optional[str]:
        """Upload using manual API implementation."""
        try:
            # Step 1: Prepare upload using v7 API
            prepare_response = self._prepare_upload(filename, video_path)
            if not prepare_response:
                return None

            # Step 2: Upload file using v6 API
            upload_success = self._upload_files(prepare_response, video_path)
            if not upload_success:
                return None

            # Step 3: Get the final URL from the upload response
            final_url = upload_success.get("url")
            if not final_url:
                logger.error("No URL returned from upload")
                return None

            logger.info(f"Successfully uploaded video using manual API: {final_url}")
            return final_url

        except Exception as e:
            logger.error(f"Manual API upload failed: {e}")
            return None

    def _prepare_upload(self, filename: str, video_path: str) -> Optional[dict]:
        """Prepare upload using v7 API."""
        try:
            url = f"{self.config.uploadthing_api_url}/v7/prepareUpload"

            # Get file size
            file_size = os.path.getsize(video_path)

            payload = {
                "files": [{"name": filename, "type": "video/mp4", "size": file_size}]
            }

            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            if data and len(data) > 0:
                return data[0]

            return None

        except Exception as e:
            logger.error(f"Error preparing upload: {e}")
            return None

    def _upload_files(self, prepare_response: dict, video_path: str) -> Optional[dict]:
        """Upload files using v6 API."""
        try:
            url = f"{self.config.uploadthing_api_url}/v6/uploadFiles"

            # Get upload details from prepare response
            upload_url = prepare_response.get("presignedUrl", {}).get("url")
            fields = prepare_response.get("presignedUrl", {}).get("fields", {})
            file_key = prepare_response.get("key")

            if not upload_url or not file_key:
                logger.error("Invalid prepare response - missing upload URL or key")
                return None

            # Upload file to presigned URL
            with open(video_path, "rb") as file:
                # Create form data with required fields
                files = {"file": (os.path.basename(video_path), file, "video/mp4")}
                data = fields  # Include any required form fields

                # Use a separate session without API key for S3 upload
                upload_session = requests.Session()
                response = upload_session.post(upload_url, files=files, data=data)
                response.raise_for_status()

            # Now call the uploadFiles endpoint to finalize
            payload = {
                "files": [
                    {
                        "key": file_key,
                        "name": os.path.basename(video_path),
                        "size": os.path.getsize(video_path),
                    }
                ]
            }

            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()
            if data and len(data) > 0:
                return data[0]

            return None

        except Exception as e:
            logger.error(f"Error uploading files: {e}")
            return None


# Global service instance
_uploadthing_service: Optional[UploadThingService] = None


def get_uploadthing_service() -> UploadThingService:
    """Get the global UploadThing service instance."""
    global _uploadthing_service
    if _uploadthing_service is None:
        _uploadthing_service = UploadThingService()
    return _uploadthing_service
