"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Quiz from "@/components/shared/quiz";
import QuizResults from "@/components/shared/quiz-results";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { answeredQuiz, getQuizById } from "@/actions/quiz.actions";

export default function QuizClient() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quizData, setQuizData] = useState<Quiz | null>(null);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [score, setScore] = useState(0);
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [quizAnswers, setQuizAnswers] = useState<any[]>([]);
  const [hasGiven, setHasGiven] = useState(false);

  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(
          `Error attempting to enable full-screen mode: ${err.message}`
        );
      });
      setIsFullScreen(true);
    } else {
      document.exitFullscreen();
      setIsFullScreen(false);
    }
  };

  const id =
    typeof params?.id === "string"
      ? params.id
      : Array.isArray(params?.id)
      ? params.id[0]
      : null;

  useEffect(() => {
    const loadQuiz = async () => {
      if (!id) {
        setError("Invalid quiz ID.");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const quiz = await getQuizById(id);
        if (!quiz?.quiz) {
          throw new Error("No quiz found for this ID.");
        }

        // Convert correctAnswer from string to number for each question
        const fixedQuiz = {
          ...quiz.quiz,
          questions: quiz.quiz.questions.map((q: any) => ({
            ...q,
            correctAnswer: typeof q.correctAnswer === "string" && !isNaN(Number(q.correctAnswer))
              ? Number(q.correctAnswer)
              : q.correctAnswer,
          })),
        };
        setQuizData(fixedQuiz);
        setHasGiven(quiz.hasGiven);
        setLoading(false);
      } catch (err: any) {
        console.error("Quiz fetch failed:", err);
        setError(err.message || "Something went wrong while loading the quiz.");
        setLoading(false);
      }
    };

    loadQuiz();
  }, [id]);

  const handleQuizComplete = async (
    finalScore: number,
    questions: number,
    answers: any[] = []
  ) => {
    console.log("Quiz Completed with Score:", finalScore);
    // Call the 
    const answered = await answeredQuiz({
      id: id || "",
      marks: finalScore,
    });
    if (!answered.success) {
      console.error("Failed to save quiz results:", answered.message);
      setError("Failed to save quiz results. Please try again.");
      return;
    }
    setScore(finalScore);
    setTotalQuestions(questions);
    setQuizAnswers(answers);
    setQuizCompleted(true);
  };

  const handleBackToDocs = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="text-center">
          <div className="mb-4 h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto"></div>
          <h1 className="text-2xl font-bold mb-2">Generating Quiz</h1>
          <p className="text-muted-foreground">
            Our AI is creating challenging questions based on the content...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="text-center max-w-md">
          <h1 className="text-2xl font-bold mb-4 text-destructive">Error</h1>
          <p className="mb-6">{error}</p>
          <Button onClick={handleBackToDocs} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Documentation
          </Button>
        </div>
      </div>
    );
  }

  if (hasGiven) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="text-center max-w-md">
          <h1 className="text-2xl font-bold mb-4">Quiz Already Taken</h1>
          <p className="mb-6">
            You have already completed this quiz. Please check your results.
          </p>
          <Button onClick={handleBackToDocs} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Documentation
          </Button>
        </div>
      </div>
    );
  }

  if (quizCompleted && quizData) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <QuizResults
          score={score}
          totalQuestions={totalQuestions}
          title={quizData.title}
          answers={quizAnswers}
        />
      </div>
    );
  }

  if (quizData) {
    return (
      <div
        className={`flex min-h-screen flex-col items-center justify-center p-4 ${
          isFullScreen ? "bg-background" : ""
        }`}
      >
        <div className="w-full max-w-4xl mx-auto">
          <div className="flex justify-end mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullScreen}
              className="gap-2"
            >
              {isFullScreen ? (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3" />
                  </svg>
                  Exit Fullscreen
                </>
              ) : (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M3 3h7v7H3zm11 0h7v7h-7zm0 11h7v7h-7zM3 14h7v7H3z" />
                  </svg>
                  Enter Fullscreen
                </>
              )}
            </Button>
          </div>
          <Quiz
            title={quizData.title}
            description={quizData.description}
            questions={quizData.questions}
            onComplete={handleQuizComplete}
          />
        </div>
      </div>
    );
  }

  return null;
}
