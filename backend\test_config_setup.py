#!/usr/bin/env python3
"""
Configuration and setup test for UploadThing integration.

This script verifies that all required environment variables and services
are properly configured before running the main integration tests.

Usage:
    python test_config_setup.py
"""

import os
import sys
import asyncio
from dotenv import load_dotenv
import httpx
from datetime import datetime

load_dotenv()


class ConfigurationTester:
    """Test configuration and environment setup."""

    def __init__(self):
        self.issues = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0

    def check(
        self, name: str, condition: bool, error_msg: str = "", warning_msg: str = ""
    ):
        """Check a condition and log results."""
        self.total_checks += 1

        if condition:
            print(f"✅ {name}")
            self.success_count += 1
        else:
            if error_msg:
                print(f"❌ {name}: {error_msg}")
                self.issues.append(f"{name}: {error_msg}")
            elif warning_msg:
                print(f"⚠️  {name}: {warning_msg}")
                self.warnings.append(f"{name}: {warning_msg}")
            else:
                print(f"❌ {name}")
                self.issues.append(name)

    def check_environment_variables(self):
        """Check required environment variables."""
        print("\n🔧 Checking Environment Variables")
        print("-" * 40)

        # Required for UploadThing
        uploadthing_secret = os.getenv("UPLOADTHING_SECRET")
        self.check(
            "UPLOADTHING_SECRET",
            bool(
                uploadthing_secret
                and uploadthing_secret != "your_uploadthing_secret_key"
            ),
            "Not set or using placeholder value",
        )

        uploadthing_app_id = os.getenv("UPLOADTHING_APP_ID")
        self.check(
            "UPLOADTHING_APP_ID",
            bool(
                uploadthing_app_id and uploadthing_app_id != "your_uploadthing_app_id"
            ),
            "Not set or using placeholder value",
        )

        # Optional but recommended
        uploadthing_api_url = os.getenv(
            "UPLOADTHING_API_URL", "https://api.uploadthing.com"
        )
        self.check(
            "UPLOADTHING_API_URL",
            uploadthing_api_url == "https://api.uploadthing.com",
            warning_msg="Using non-standard API URL",
        )

        # Frontend API configuration
        frontend_api_url = os.getenv("FRONTEND_API_BASE_URL", "http://localhost:3000")
        self.check(
            "FRONTEND_API_BASE_URL",
            bool(frontend_api_url),
            warning_msg="Using default value",
        )

        # Queue configuration (should already exist)
        qstash_token = os.getenv("QSTASH_TOKEN")
        self.check(
            "QSTASH_TOKEN",
            bool(
                qstash_token
                and qstash_token != "your_qstash_token_from_upstash_console"
            ),
            "Not set or using placeholder value",
        )

        worker_base_url = os.getenv("WORKER_BASE_URL")
        self.check(
            "WORKER_BASE_URL",
            bool(worker_base_url and worker_base_url != "http://your-domain.com"),
            "Not set or using placeholder value",
        )

        worker_secret = os.getenv("WORKER_SECRET")
        self.check(
            "WORKER_SECRET",
            bool(worker_secret and worker_secret != "your_secure_random_string"),
            "Not set or using placeholder value",
        )

    def check_python_dependencies(self):
        """Check required Python packages."""
        print("\n📦 Checking Python Dependencies")
        print("-" * 40)

        required_packages = [
            "fastapi",
            "pydantic",
            "pydantic_settings",
            "requests",
            "httpx",
            "qstash",
        ]

        for package in required_packages:
            try:
                __import__(package)
                self.check(f"Package: {package}", True)
            except ImportError:
                self.check(f"Package: {package}", False, "Not installed")

    async def check_service_connectivity(self):
        """Check if services are accessible."""
        print("\n🌐 Checking Service Connectivity")
        print("-" * 40)

        # Check backend
        try:
            async with httpx.AsyncClient(timeout=5) as client:
                response = await client.get("http://localhost:8000/")
                self.check(
                    "Backend Server (localhost:8000)",
                    response.status_code == 200,
                    (
                        f"HTTP {response.status_code}"
                        if response.status_code != 200
                        else ""
                    ),
                )
        except Exception as e:
            self.check(
                "Backend Server (localhost:8000)", False, f"Connection failed: {e}"
            )

        # Check frontend
        try:
            async with httpx.AsyncClient(timeout=5) as client:
                response = await client.get(
                    "http://localhost:3000/api/entries/update?entryId=test"
                )
                self.check(
                    "Frontend API (localhost:3000)",
                    response.status_code
                    in [400, 404, 200],  # Any response means it's running
                    (
                        f"Unexpected status: {response.status_code}"
                        if response.status_code not in [400, 404, 200]
                        else ""
                    ),
                )
        except Exception as e:
            self.check(
                "Frontend API (localhost:3000)", False, f"Connection failed: {e}"
            )

        # Check UploadThing API (if credentials are set)
        uploadthing_secret = os.getenv("UPLOADTHING_SECRET")
        if uploadthing_secret and uploadthing_secret != "your_uploadthing_secret_key":
            try:
                async with httpx.AsyncClient(timeout=10) as client:
                    headers = {"X-Uploadthing-Api-Key": uploadthing_secret}
                    response = await client.get(
                        "https://api.uploadthing.com/api/health", headers=headers
                    )
                    self.check(
                        "UploadThing API",
                        response.status_code
                        in [200, 401, 403],  # Any response means API is reachable
                        (
                            f"HTTP {response.status_code}"
                            if response.status_code not in [200, 401, 403]
                            else ""
                        ),
                    )
            except Exception as e:
                self.check("UploadThing API", False, f"Connection failed: {e}")
        else:
            self.check(
                "UploadThing API", False, "Cannot test - credentials not configured"
            )

    def check_file_permissions(self):
        """Check file system permissions."""
        print("\n📁 Checking File System Permissions")
        print("-" * 40)

        # Check media directory
        media_dir = "media"
        try:
            os.makedirs(media_dir, exist_ok=True)
            self.check("Media directory writable", True)
        except Exception as e:
            self.check("Media directory writable", False, str(e))

        # Check tmp directory
        tmp_dir = "tmp"
        try:
            os.makedirs(tmp_dir, exist_ok=True)
            self.check("Tmp directory writable", True)
        except Exception as e:
            self.check("Tmp directory writable", False, str(e))

    def check_manim_installation(self):
        """Check if Manim is properly installed."""
        print("\n🎬 Checking Manim Installation")
        print("-" * 40)

        try:
            import manim

            self.check("Manim package", True)
        except ImportError:
            self.check("Manim package", False, "Not installed")
            return

        # Check if manim command is available
        import subprocess

        try:
            result = subprocess.run(
                ["manim", "--version"], capture_output=True, text=True, timeout=10
            )
            self.check(
                "Manim CLI", result.returncode == 0, "Command not found or failed"
            )
        except Exception as e:
            self.check("Manim CLI", False, str(e))

    async def run_all_checks(self):
        """Run all configuration checks."""
        print("🔍 Configuration and Setup Verification")
        print("=" * 60)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        self.check_environment_variables()
        self.check_python_dependencies()
        await self.check_service_connectivity()
        self.check_file_permissions()
        self.check_manim_installation()

        self.print_summary()

    def print_summary(self):
        """Print configuration check summary."""
        print("\n" + "=" * 60)
        print("📊 CONFIGURATION SUMMARY")
        print("=" * 60)

        print(f"Total Checks: {self.total_checks}")
        print(f"✅ Passed: {self.success_count}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        print(f"❌ Issues: {len(self.issues)}")

        success_rate = (
            (self.success_count / self.total_checks) * 100
            if self.total_checks > 0
            else 0
        )
        print(f"Success Rate: {success_rate:.1f}%")

        if self.warnings:
            print("\n⚠️  WARNINGS:")
            for warning in self.warnings:
                print(f"  - {warning}")

        if self.issues:
            print("\n❌ ISSUES TO FIX:")
            for issue in self.issues:
                print(f"  - {issue}")

            print("\n🔧 RECOMMENDED ACTIONS:")
            print("1. Copy .env.example to .env and configure your values")
            print("2. Set up UploadThing account and get API credentials")
            print("3. Ensure both backend and frontend servers are running")
            print("4. Install missing Python packages: pip install -r requirements.txt")

        if len(self.issues) == 0:
            print("\n🎉 All checks passed! You're ready to run the integration tests.")
        elif len(self.issues) <= 2:
            print("\n⚠️  Minor issues detected. Integration tests may still work.")
        else:
            print(
                "\n🚨 Multiple issues detected. Please fix them before running integration tests."
            )

        print("\n" + "=" * 60)


async def main():
    """Main configuration test runner."""
    tester = ConfigurationTester()
    await tester.run_all_checks()


if __name__ == "__main__":
    print("🔧 UploadThing Integration - Configuration Checker")
    print("This script verifies your environment is ready for testing.")
    print()

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Configuration check interrupted by user")
    except Exception as e:
        print(f"\n💥 Configuration check failed with error: {e}")
        sys.exit(1)
