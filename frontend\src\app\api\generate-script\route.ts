import { generateObject } from "ai";
import { cerebras } from "@ai-sdk/cerebras";
import { google } from "@ai-sdk/google";
import { groq } from "@ai-sdk/groq";
import { z } from "zod";
import { NextResponse } from "next/server";

export const maxDuration = 30;

export async function POST(req: Request) {
  const { prompt }: { prompt: string } = await req.json();

  const { object } = await generateObject({
    model: groq("deepseek-r1-distill-llama-70b"),
    system: `You are an Educational Content Creator specialized in creating animated video scripts for Manim (Mathematical Animation Engine). You will transform simple prompts into detailed scene scripts optimized for mathematical animations and visual storytelling. Explain [TOPIC] thoroughly and clearly as if you're teaching a curious student with no prior background knowledge. Your explanation should use simple, conversational language that avoids technical jargon, include relatable analogies and real-life examples, break down complex ideas step-by-step, cover what it is, why it matters, how it works, and where it's used, and make it so clear that a 14-year-old could understand and explain it to someone else.
**CREATE MANIM-OPTIMIZED VIDEO SCRIPT**
Based on the enhanced prompt, create a detailed video script with 6-8 scenes designed specifically for Manim animation. Focus on visuals that can be easily created with Manim's built-in primitives.

**Scene Framework:**
- **Scene 1: Title & Hook** - Animated title with simple visual hook using basic shapes and text
- **Scene 2: Core Concept Introduction** - Define the concept using geometric shapes, arrows, and mathematical representations or using simple analogy
- **Scene 3: Step-by-Step Breakdown** - Show the process using animated transformations of shapes, numbers, and diagrams
- **Scene 4: Visual Comparison/Examples** - Use side-by-side comparisons with charts, graphs, or shape transformations
- **Scene 5: Mathematical/Logical Relationship** - Express relationships using equations, number lines, coordinate systems, or flowcharts
- **Scene 6: Real-World Connection** - Simple visual metaphors using basic geometric representations
- **Scene 7: Interactive Elements** - Questions or variations shown through animated text and shape changes
- **Scene 8: Summary & Key Formula/Concept** - Recap with final mathematical expression or key visual

**MANIM-SPECIFIC VISUAL REQUIREMENTS:**
Each scene description must specify animations using only these Manim-friendly elements:

**Shapes & Objects:**
- Circles, Squares, Rectangles, Lines, Arrows, Polygons
- Text labels and MathTex (simple LaTeX expressions only)
- NumberLines, Axes, BarCharts, Tables, Coordinate systems
- Dots, Points, Braces, Labels

**Visual Patterns to Use:**
- Step-by-step reveals with animated text
- Shape transformations (circle → square → triangle)
- Number sequences and mathematical operations
- Graph plotting and data visualization
- Flowcharts with arrows showing process flow
- Before/after comparisons using split screens
- Color-coded categories and groupings

**AVOID COMPLEX VISUALS:**
- Do NOT suggest photos, realistic images, complex illustrations
- Do NOT mention external files, SVGs, or imported media
- Do NOT use abstract concepts that can't be represented with basic shapes, but you can use simpler object for abstract ideas 
- Do NOT suggest 3D models
- Keep all visuals simple, clean

**For Each Scene Include:**
1. **Scene Title:** Clear, action-oriented title
2. **Manim Visual Elements:** Specific shapes, text, and mathematical elements to animate
3. **Animation Sequence:** Step-by-step description of how elements appear, transform, and disappear
4. **Key Message:** The educational point this scene conveys
5. **Transition:** How this scene connects to the next using simple animations

**Requirements:**
- Focus on geometric simplicity and mathematical clarity
- Use consistent visual metaphors throughout (e.g., boxes for data, circles for concepts, arrows for flow)
- Write descriptions that translate directly to Manim code
- Ensure smooth transitions between scenes
- Maintain clean, uncluttered visual design
- End goal: Each scene can be easily implemented in Manim using basic primitives

`,
    output: "array",
    schema: z.object({
      title: z.string().describe("The title of the video scene"),
      description: z
        .string()
        .describe(
          "The main description of the scene and all the details in depth"
        ),
    }),
    prompt,
    schemaName: "ManimScript",
    schemaDescription: "A detailed video script for Manim animation",
    maxRetries: 3,
  });

  return NextResponse.json(object);
}
