# ClarifAI Queue-Based Rendering System

This document describes the queue-based video rendering system implemented using Upstash QStash.

## Overview

The system has been transformed from synchronous video rendering to an asynchronous queue-based architecture that provides:

- **Sequential Job Processing**: Jobs are processed one after another in FIFO order
- **Reliability**: Built-in retry mechanisms and error handling
- **Scalability**: Can handle multiple jobs without blocking the API
- **Monitoring**: Comprehensive job status tracking and queue statistics
- **Error Recovery**: Dead letter queue handling and manual retry capabilities

## Architecture

### Components

1. **Queue Manager** (`services/queue_manager.py`): Manages job queuing and status tracking
2. **Job Processor** (`workers/job_processor.py`): Processes jobs from the queue
3. **Worker Endpoints** (`workers/worker_endpoints.py`): HTTP endpoints called by QStash
4. **Job Models** (`models/job_models.py`): Data models for jobs and status tracking
5. **Configuration** (`config/queue_config.py`): Queue system configuration

### Flow

1. Client submits render job via `/render` or `/batch_render`
2. Job is queued in Upstash QStash with FIFO ordering
3. QStash calls worker endpoint when job is ready to process
4. Worker processes the job (renders video)
5. Job status is updated throughout the process
6. Client can check status via `/jobs/{job_id}`

## Setup

### 1. Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Required
QSTASH_TOKEN=your_qstash_token_from_upstash_console
WORKER_BASE_URL=http://your-domain.com  # Where QStash can reach your worker endpoints
WORKER_SECRET=your_secure_random_string

# Optional (with defaults)
RENDER_QUEUE_NAME=render-jobs
BATCH_RENDER_QUEUE_NAME=batch-render-jobs
QUEUE_PARALLELISM=1
MAX_RETRIES=3
JOB_TIMEOUT_SECONDS=300
CLEANUP_COMPLETED_JOBS_AFTER_HOURS=24
```

### 2. Get QStash Token

1. Sign up at [Upstash Console](https://console.upstash.com)
2. Create a QStash project
3. Copy your QStash token
4. Set up signing keys for webhook verification

### 3. Configure Worker URL

The `WORKER_BASE_URL` must be publicly accessible so QStash can call your worker endpoints:
- For development: Use ngrok or similar tunneling service
- For production: Use your actual domain

## API Endpoints

### Job Submission

#### POST /render
Queue a single render job.

```json
{
  "script": "from manim import *\nclass MyScene(Scene): ...",
  "scene_name": "MyScene",
  "priority": 0
}
```

Response:
```json
{
  "job_id": "abc123",
  "status": "queued",
  "message": "Job queued successfully. Use /jobs/abc123 to check status."
}
```

#### POST /batch_render
Queue a batch render job.

```json
{
  "scripts": [
    {"script": "...", "scene_name": "Scene1"},
    {"script": "...", "scene_name": "Scene2"}
  ],
  "priority": 0
}
```

### Job Monitoring

#### GET /jobs/{job_id}
Get job status and results.

```json
{
  "job_id": "abc123",
  "job_type": "render",
  "status": "completed",
  "created_at": "2023-...",
  "started_at": "2023-...",
  "completed_at": "2023-...",
  "progress_percentage": 100.0,
  "result": {
    "job_id": "abc123",
    "success": true,
    "output_urls": ["/media/abc123/MyScene.mp4"],
    "processing_time_seconds": 45.2
  }
}
```

#### GET /jobs
List jobs with optional filtering.

Query parameters:
- `status`: Filter by status (queued, processing, completed, failed, cancelled)
- `job_type`: Filter by type (render, batch_render)
- `limit`: Maximum number of jobs to return (default: 100, max: 1000)

#### DELETE /jobs/{job_id}
Cancel a queued job.

### Queue Management

#### GET /queues/stats
Get queue statistics.

```json
{
  "queues": {
    "render": {
      "queue_name": "render-jobs",
      "total_jobs": 10,
      "queued_jobs": 2,
      "processing_jobs": 1,
      "completed_jobs": 6,
      "failed_jobs": 1,
      "cancelled_jobs": 0
    },
    "batch_render": { ... }
  },
  "total_active_jobs": 3
}
```

### Error Handling

#### POST /jobs/{job_id}/retry
Retry a failed job.

#### GET /jobs/failed
Get all failed jobs that can be retried.

#### POST /admin/cleanup
Clean up completed jobs older than specified hours.

## Job States

- **queued**: Job is waiting to be processed
- **processing**: Job is currently being processed
- **completed**: Job finished successfully
- **failed**: Job failed (may be retryable)
- **cancelled**: Job was cancelled

## Error Handling

### Automatic Retries
- Jobs are automatically retried up to `MAX_RETRIES` times
- Exponential backoff between retries
- Failed jobs after max retries go to dead letter queue

### Manual Recovery
- View failed jobs: `GET /jobs/failed`
- Retry specific job: `POST /jobs/{job_id}/retry`
- Monitor with: `GET /jobs/{job_id}`

### Dead Letter Queue
Jobs that exhaust all retries are marked as permanently failed with "Dead letter" prefix in error message.

## Monitoring and Maintenance

### Health Checks
- Worker health: `GET /worker/health`
- Queue stats: `GET /queues/stats`

### Cleanup
- Automatic: Completed jobs are cleaned up after 24 hours (configurable)
- Manual: `POST /admin/cleanup?older_than_hours=24`

### Logging
All operations are logged with appropriate levels. Set `LOG_LEVEL=DEBUG` for detailed debugging.

## Development

### Running Locally

1. Install dependencies: `pip install -r requirements.txt`
2. Set up environment variables
3. Start the server: `uvicorn main:app --reload`
4. Use ngrok for webhook testing: `ngrok http 8000`

### Testing

The system includes comprehensive error handling and logging. Monitor logs for:
- Job queuing success/failure
- Worker processing status
- QStash webhook calls
- Retry attempts

## Production Considerations

1. **Database**: Replace in-memory job storage with Redis or PostgreSQL
2. **Monitoring**: Add metrics collection (Prometheus, DataDog, etc.)
3. **Scaling**: Configure queue parallelism based on server capacity
4. **Security**: Use strong worker secrets and HTTPS endpoints
5. **Backup**: Implement job data persistence and backup strategies

## Troubleshooting

### Common Issues

1. **Jobs stuck in queued state**
   - Check QStash can reach your worker endpoints
   - Verify WORKER_BASE_URL is publicly accessible
   - Check worker secret configuration

2. **Worker signature verification fails**
   - Ensure WORKER_SECRET matches QStash configuration
   - Check webhook URL configuration

3. **Jobs failing repeatedly**
   - Check manim installation and dependencies
   - Verify script syntax
   - Monitor worker logs for detailed errors

### Debug Commands

```bash
# Check job status
curl http://localhost:8000/jobs/{job_id}

# View queue stats
curl http://localhost:8000/queues/stats

# List failed jobs
curl http://localhost:8000/jobs/failed

# Worker health check
curl http://localhost:8000/worker/health
```
