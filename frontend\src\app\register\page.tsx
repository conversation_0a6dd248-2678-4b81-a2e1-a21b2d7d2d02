"use client"

import { CivicAuthIframeContainer } from "@civic/auth/react";
import { redirect } from "next/navigation";

export default function Login (){
  return (
      <div className="purple-gradient relative min-h-screen overflow-hidden bg-background">
        <div>
          <CivicAuthIframeContainer
            onClose={() => {
              redirect("/");
            }}
            closeOnRedirect={true}
          />
        </div>
      </div>
  );
};