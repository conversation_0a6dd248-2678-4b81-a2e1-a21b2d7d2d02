# ClarifAI Backend

## Overview

ClarifAI is a queue-based video rendering API that uses Manim to generate mathematical animations. The system has been upgraded from synchronous processing to an asynchronous queue-based architecture using Upstash QStash.

## Features

- **Queue-Based Processing**: Jobs are processed sequentially using Upstash QStash
- **Reliable Delivery**: Built-in retry mechanisms and error handling
- **Job Monitoring**: Real-time status tracking and progress monitoring
- **Batch Processing**: Support for rendering multiple scenes in sequence
- **Error Recovery**: Manual retry capabilities and dead letter queue handling
- **RESTful API**: Comprehensive API for job management and monitoring

## Quick Start

### Prerequisites

- Python 3.8+
- Manim and dependencies
- Upstash QStash account
- FFmpeg (for video processing)

### Installation

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start the server:

```bash
uvicorn main:app --reload
```

### Configuration

See [Queue System Documentation](README_QUEUE.md) for detailed setup instructions.

## API Usage

### Submit a Render Job

```bash
curl -X POST http://localhost:8000/render \
  -H "Content-Type: application/json" \
  -d '{
    "script": "from manim import *\nclass MyScene(Scene):\n    def construct(self):\n        text = Text(\"Hello World\")\n        self.play(Write(text))",
    "scene_name": "MyScene"
  }'
```

### Check Job Status

```bash
curl http://localhost:8000/jobs/{job_id}
```

### View Queue Statistics

```bash
curl http://localhost:8000/queues/stats
```

## Architecture

The system consists of:

- **API Layer**: FastAPI endpoints for job submission and monitoring
- **Queue Manager**: Handles job queuing using Upstash QStash
- **Worker Processes**: Process jobs from the queue
- **Job Tracking**: In-memory status tracking (upgradeable to database)

## Documentation

- [Queue System Guide](README_QUEUE.md) - Comprehensive queue system documentation
- [API Reference](http://localhost:8000/docs) - Interactive API documentation (when server is running)

## Development

### Project Structure

```
backend/
├── main.py                 # FastAPI application and API endpoints
├── config/
│   └── queue_config.py     # Queue system configuration
├── models/
│   └── job_models.py       # Data models for jobs and responses
├── services/
│   └── queue_manager.py    # Queue management and job tracking
├── workers/
│   ├── job_processor.py    # Job processing logic
│   └── worker_endpoints.py # Worker HTTP endpoints
└── media/                  # Generated video files
```

### Running Tests

```bash
python -m pytest
```

## Production Deployment

For production deployment, consider:

1. **Database Integration**: Replace in-memory storage with Redis/PostgreSQL
2. **Load Balancing**: Multiple worker instances for high throughput
3. **Monitoring**: Integrate with monitoring systems (Prometheus, DataDog)
4. **Security**: HTTPS endpoints and secure worker authentication
5. **Scaling**: Configure queue parallelism based on server capacity

## Support

For issues and questions:

- Check the [Queue System Documentation](README_QUEUE.md)
- Review API documentation at `/docs` endpoint
- Monitor application logs for debugging information
