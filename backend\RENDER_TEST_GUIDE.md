# 🧪 Render Endpoint Test Scripts Guide

This guide explains how to use the test scripts to test your render endpoints with UploadThing integration.

## 📁 Test Scripts Overview

### 1. `test_simple_render_post.py` - Single Scene Render
**Purpose**: Test single scene rendering with UploadThing upload
**What it does**:
- Sends POST to `/render` endpoint
- Includes `entryId`, `script`, and `scene_name`
- Monitors job until completion
- Shows UploadThing upload results

### 2. `test_batch_render_post.py` - Multi-Scene Batch Render
**Purpose**: Test batch rendering with video merging and UploadThing upload
**What it does**:
- Sends POST to `/batch_render` endpoint
- Processes 3 different scenes
- Merges them into a single video
- Uploads merged video to UploadThing

### 3. `test_end_to_end_render.py` - Complete Integration Test
**Purpose**: Comprehensive test including database verification
**What it does**:
- Full workflow testing
- Database entry verification
- UploadThing URL accessibility testing
- Detailed result analysis

## 🚀 Quick Start

### Prerequisites
```bash
# 1. Start your backend
cd backend
uvicorn main:app --reload --port 8000

# 2. Start your frontend
cd frontend
npm run dev

# 3. Configure UploadThing (in .env)
UPLOADTHING_SECRET=your_actual_secret_key
```

### Running Tests

#### Simple Single Render Test
```bash
python test_simple_render_post.py
```
**Expected Output**:
```
🚀 Sending POST request to render endpoint...
📋 Generated Entry ID: 12345678-1234-1234-1234-123456789abc
📡 Response Status: 200
✅ Request successful!
🆔 Job ID: job_67890
```

#### Batch Render Test
```bash
python test_batch_render_post.py
```
**Expected Output**:
```
🚀 Sending POST request to batch render endpoint...
📋 Generated Entry ID: 12345678-1234-1234-1234-123456789abc
📝 Number of scenes: 3
✅ Batch render request successful!
```

#### Complete End-to-End Test
```bash
python test_end_to_end_render.py
```

## 📋 Test Scenarios

### Scenario 1: Basic Functionality Test
```bash
# Test the basic POST → render → upload workflow
python test_simple_render_post.py
```

### Scenario 2: Batch Processing Test
```bash
# Test multiple scenes with merging
python test_batch_render_post.py
```

### Scenario 3: Full Integration Test
```bash
# Test everything including database updates
python test_end_to_end_render.py
```

## 📊 Understanding Test Results

### Successful Single Render
```
📊 FINAL RESULTS
📁 Local Media URLs (1):
   /media/job_12345/SimpleTest.mp4
🔗 UploadThing URLs (1):
   https://uploadthing.com/f/abc123.mp4
✅ UploadThing integration working!
```

### Successful Batch Render
```
📊 BATCH RENDER RESULTS
📝 Scenes Processed: 3
📁 Local: /media/job_67890/merged_output.mp4
🔗 UploadThing: https://uploadthing.com/f/def456.mp4
✅ Batch render + UploadThing integration working!
```

## 🔧 Customizing Tests

### Custom Script Example
```python
# Edit the TEST_SCRIPT variable in test_simple_render_post.py
TEST_SCRIPT = """from manim import *

class MyCustomScene(Scene):
    def construct(self):
        # Your custom Manim code here
        text = Text("My Custom Test", font_size=48)
        self.play(Write(text))
        self.wait(2)
"""
```

### Custom Entry ID
```python
# Use your own entry ID instead of generated UUID
entry_id = "your-specific-entry-id"
```

### Custom Scene Name
```python
payload = {
    "script": TEST_SCRIPT,
    "scene_name": "YourSceneName",  # Change this
    "priority": 1,
    "entry_id": entry_id
}
```

## 🐛 Troubleshooting

### Common Issues

#### 1. "Backend not accessible"
```bash
# Check if backend is running
curl http://localhost:8000/
```

#### 2. "Job submission failed"
- Check backend logs for errors
- Verify Manim script syntax
- Ensure all required fields are provided

#### 3. "No UploadThing URLs found"
- Check `UPLOADTHING_SECRET` in .env
- Verify UploadThing account status
- Check backend logs for upload errors

#### 4. "Job timeout"
- Increase `max_wait` parameter
- Check if Manim is properly installed
- Verify script complexity (simpler scripts render faster)

### Debug Commands

```bash
# Check job status manually
curl http://localhost:8000/jobs/{job_id}

# Check queue stats
curl http://localhost:8000/queues/stats

# Check if entry was updated
curl "http://localhost:3000/api/entries/update?entryId={entry_id}"
```

## 📝 Test Data

### Sample Scripts Included

#### Simple Test (test_simple_render_post.py)
- Basic text animation
- ~5-10 seconds render time
- Good for quick testing

#### Batch Test (test_batch_render_post.py)
- 3 different scenes with shapes
- ~30-60 seconds total render time
- Tests video merging functionality

#### Complex Test (test_end_to_end_render.py)
- Multiple animations and transformations
- ~20-30 seconds render time
- Comprehensive visual test

## 🎯 Expected Workflow

1. **POST Request** → Immediate response with job ID
2. **Job Processing** → Manim renders video(s)
3. **File Storage** → Video saved to `media/{job_id}/`
4. **UploadThing Upload** → Video uploaded to cloud
5. **Database Update** → Entry updated with video URL
6. **Response** → Job marked complete with URLs

## 📈 Performance Expectations

| Test Type | Render Time | Upload Time | Total Time |
|-----------|-------------|-------------|------------|
| Simple    | 5-15s       | 2-5s        | 10-25s     |
| Batch     | 30-90s      | 5-10s       | 40-120s    |
| Complex   | 15-45s      | 3-8s        | 20-60s     |

*Times vary based on script complexity and system performance*

## 🔄 Continuous Testing

### Automated Testing
```bash
# Run all tests in sequence
python test_simple_render_post.py && \
python test_batch_render_post.py && \
python test_end_to_end_render.py
```

### CI/CD Integration
```yaml
# Example GitHub Actions step
- name: Test Render Endpoints
  run: |
    python test_simple_render_post.py
    python test_end_to_end_render.py
```

## 📞 Support

If tests consistently fail:

1. **Check Configuration**
   - Verify .env file settings
   - Confirm UploadThing credentials
   - Test backend/frontend connectivity

2. **Review Logs**
   - Backend application logs
   - Manim rendering logs
   - UploadThing upload logs

3. **Validate Environment**
   - Python dependencies installed
   - Manim properly configured
   - Network connectivity for UploadThing

4. **Test Components**
   - Run `test_config_setup.py` first
   - Test UploadThing with `test_uploadthing_api.py`
   - Verify job processing manually
