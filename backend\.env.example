# QStash Configuration
QSTASH_TOKEN="eyJVc2VySUQiOiI3OWY0OGExOC0yYjI0LTQwM2UtYjFiNy1hYzVmNGE1ZDBkNzYiLCJQYXNzd29yZCI6ImNhZGJkZTc1MDcyNTRmNTA5NGYzMmQ5MzRjYzViNzkwIn0="
QSTASH_URL=https://qstash.upstash.io

# Queue Configuration
RENDER_QUEUE_NAME=render-jobs
BATCH_RENDER_QUEUE_NAME=batch-render-jobs
QUEUE_PARALLELISM=1
MAX_RETRIES=3

# Worker Configuration
WORKER_BASE_URL=http://localhost:8000
WORKER_SECRET=yoyo

# Job Settings
JOB_TIMEOUT_SECONDS=300
CLEANUP_COMPLETED_JOBS_AFTER_HOURS=24

# Application Settings
LOG_LEVEL=INFO
