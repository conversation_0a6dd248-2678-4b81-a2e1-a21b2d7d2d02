# UploadThing Integration Test Suite

This directory contains comprehensive tests for the UploadThing integration and entry update functionality.

## 📁 Test Files

### 1. `test_config_setup.py`

**Configuration and Environment Verification**

- Checks environment variables
- Verifies Python dependencies
- Tests service connectivity
- Validates file permissions
- Confirms Manim installation

**Usage:**

```bash
python test_config_setup.py
```

### 2. `test_unit_uploadthing.py`

**Unit Tests for Individual Components**

- Job models with entryId parameter
- UploadThing service functionality
- API client service
- Job processor integration

**Usage:**

```bash
# With pytest (recommended)
python -m pytest test_unit_uploadthing.py -v

# Without pytest
python test_unit_uploadthing.py
```

### 3. `test_uploadthing_api.py`

**UploadThing API v6/v7 Direct Testing**

- Tests UploadThing REST API integration
- Uploads from media folder (real file structure)
- Validates API endpoints and responses
- Quick verification of UploadThing credentials

**Usage:**

```bash
python test_uploadthing_api.py
```

### 4. `test_uploadthing_integration.py`

**End-to-End Integration Tests**

- Complete workflow testing
- Render jobs with/without entryId
- Batch render jobs
- Video upload verification
- Entry update confirmation

**Usage:**

```bash
python test_uploadthing_integration.py
```

## 🚀 Quick Start

### Prerequisites

1. **Environment Setup:**

   ```bash
   # Copy and configure environment variables
   cp .env.example .env
   # Edit .env with your actual values
   ```

2. **Required Environment Variables:**

   ```bash
   # UploadThing Configuration
   UPLOADTHING_SECRET=your_actual_uploadthing_secret
   UPLOADTHING_APP_ID=your_actual_app_id

   # Frontend API
   FRONTEND_API_BASE_URL=http://localhost:3000

   # Existing Queue Configuration
   QSTASH_TOKEN=your_qstash_token
   WORKER_BASE_URL=your_worker_url
   WORKER_SECRET=your_worker_secret
   ```

3. **Start Services:**

   ```bash
   # Terminal 1: Start backend
   cd backend
   uvicorn main:app --reload --port 8000

   # Terminal 2: Start frontend
   cd frontend
   npm run dev
   ```

### Running Tests

1. **Configuration Check (Run First):**

   ```bash
   python test_config_setup.py
   ```

2. **Unit Tests:**

   ```bash
   python -m pytest test_unit_uploadthing.py -v
   ```

3. **UploadThing API Test:**

   ```bash
   python test_uploadthing_api.py
   ```

4. **Integration Tests:**
   ```bash
   python test_uploadthing_integration.py
   ```

## 📋 Test Coverage

### Job Models

- ✅ RenderJobData with entryId
- ✅ CreateRenderJobRequest with entryId
- ✅ CreateBatchRenderJobRequest with entryId
- ✅ JobRequest with entryId
- ✅ Backward compatibility (without entryId)

### UploadThing Service

- ✅ Service initialization with v6/v7 API endpoints
- ✅ Configuration validation
- ✅ File upload from media folder (real Manim output)
- ✅ Presigned URL handling
- ✅ Error handling and logging
- ✅ Singleton pattern

### API Client Service

- ✅ Client initialization
- ✅ Entry update requests
- ✅ Entry retrieval
- ✅ Error handling
- ✅ HTTP status validation

### Job Processor

- ✅ Video upload integration
- ✅ Entry update integration
- ✅ Error resilience
- ✅ Backward compatibility

### End-to-End Workflow

- ✅ Render job without entryId
- ✅ Render job with entryId
- ✅ Batch render with entryId
- ✅ Video upload verification
- ✅ Entry update confirmation
- ✅ Job completion tracking

## 🔧 Configuration Details

### UploadThing Setup

1. Create account at [UploadThing](https://uploadthing.com)
2. Get your API secret and app ID
3. Configure environment variables

### Database Setup

The tests assume you have a MongoDB database configured with the Entries model:

```typescript
model Entries {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String
  prompt    String
  videoUrl  String?  // This field gets updated
  // ... other fields
}
```

## 🐛 Troubleshooting

### Common Issues

1. **"Backend not accessible"**

   - Ensure backend server is running on port 8000
   - Check if all dependencies are installed

2. **"Frontend API not accessible"**

   - Ensure frontend server is running on port 3000
   - Verify the entries API endpoint exists

3. **"UploadThing credentials not configured"**

   - Set UPLOADTHING_SECRET in .env file
   - Verify credentials are valid

4. **"Job timeout"**
   - Manim rendering can take time
   - Check if Manim is properly installed
   - Verify script syntax

### Debug Commands

```bash
# Check backend health
curl http://localhost:8000/

# Check frontend API
curl http://localhost:3000/api/entries/update?entryId=test

# Check job status
curl http://localhost:8000/jobs/{job_id}

# View queue stats
curl http://localhost:8000/queues/stats
```

## 📊 Expected Test Results

### Configuration Check

- All environment variables configured: ✅
- Services accessible: ✅
- Dependencies installed: ✅
- File permissions: ✅

### Unit Tests

- Job models: 8/8 tests passing
- UploadThing service: 4/4 tests passing
- API client: 4/4 tests passing
- Job processor: 2/2 tests passing

### Integration Tests

- Health checks: 2/2 passing
- Render jobs: 3/3 passing
- Job completion: 3/3 passing
- Upload verification: 1/1 passing

## 🎯 Success Criteria

The integration is working correctly when:

1. ✅ All configuration checks pass
2. ✅ Unit tests pass with 100% success rate
3. ✅ Integration tests complete successfully
4. ✅ Videos are uploaded to UploadThing
5. ✅ Database entries are updated with video URLs
6. ✅ Job results include both local and remote URLs

## 📝 Notes

- **Videos are uploaded from the `media` folder** where Manim stores rendered files
- Tests use mock entry IDs for safety
- Actual video rendering may take 30-60 seconds
- UploadThing uploads depend on file size and network speed
- Some tests require active internet connection
- Database operations require valid MongoDB connection
- Uses UploadThing REST API v6/v7 endpoints for proper integration

## 🔄 Continuous Testing

For ongoing development, consider:

1. **Pre-commit hooks** to run unit tests
2. **CI/CD integration** for automated testing
3. **Monitoring** for production UploadThing usage
4. **Load testing** for high-volume scenarios

## 📞 Support

If tests fail consistently:

1. Check the logs in both backend and frontend
2. Verify UploadThing account status and quotas
3. Ensure database connectivity
4. Review environment variable configuration
5. Check network connectivity for external services
