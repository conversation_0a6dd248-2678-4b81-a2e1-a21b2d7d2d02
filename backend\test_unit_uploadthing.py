#!/usr/bin/env python3
"""
Unit tests for UploadThing integration components.

This file tests individual components in isolation:
1. UploadThing service
2. API client service  
3. Job models with entryId
4. Queue manager with entryId

Usage:
    python -m pytest test_unit_uploadthing.py -v
    # OR
    python test_unit_uploadthing.py
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone

# Import the modules to test
from services.uploadthing_service import UploadThingService, get_uploadthing_service
from services.api_client import ApiClient, get_api_client
from models.job_models import (
    CreateRenderJobRequest, 
    CreateBatchRenderJobRequest,
    RenderJobData,
    JobRequest,
    JobType
)


class TestJobModels:
    """Test job models with entryId parameter."""
    
    def test_render_job_data_with_entry_id(self):
        """Test RenderJobData model with entry_id."""
        data = RenderJobData(
            script="test script",
            scene_name="TestScene",
            entry_id="test-entry-123"
        )
        
        assert data.script == "test script"
        assert data.scene_name == "TestScene"
        assert data.entry_id == "test-entry-123"
    
    def test_render_job_data_without_entry_id(self):
        """Test RenderJobData model without entry_id (backward compatibility)."""
        data = RenderJobData(
            script="test script",
            scene_name="TestScene"
        )
        
        assert data.script == "test script"
        assert data.scene_name == "TestScene"
        assert data.entry_id is None
    
    def test_create_render_job_request_with_entry_id(self):
        """Test CreateRenderJobRequest with entry_id."""
        request = CreateRenderJobRequest(
            script="test script",
            scene_name="TestScene",
            priority=1,
            entry_id="test-entry-123"
        )
        
        assert request.script == "test script"
        assert request.scene_name == "TestScene"
        assert request.priority == 1
        assert request.entry_id == "test-entry-123"
    
    def test_create_batch_render_job_request_with_entry_id(self):
        """Test CreateBatchRenderJobRequest with entry_id."""
        scripts = [
            RenderJobData(script="script1", scene_name="Scene1", entry_id="entry-1"),
            RenderJobData(script="script2", scene_name="Scene2", entry_id="entry-2")
        ]
        
        request = CreateBatchRenderJobRequest(
            scripts=scripts,
            priority=2,
            entry_id="batch-entry-123"
        )
        
        assert len(request.scripts) == 2
        assert request.priority == 2
        assert request.entry_id == "batch-entry-123"
        assert request.scripts[0].entry_id == "entry-1"
        assert request.scripts[1].entry_id == "entry-2"
    
    def test_job_request_with_entry_id(self):
        """Test JobRequest model with entry_id."""
        render_data = RenderJobData(
            script="test script",
            scene_name="TestScene",
            entry_id="test-entry-123"
        )
        
        job_request = JobRequest(
            job_type=JobType.RENDER,
            render_data=render_data,
            entry_id="test-entry-123"
        )
        
        assert job_request.job_type == JobType.RENDER
        assert job_request.render_data.entry_id == "test-entry-123"
        assert job_request.entry_id == "test-entry-123"


class TestUploadThingService:
    """Test UploadThing service functionality."""
    
    @patch('services.uploadthing_service.UploadThingConfig')
    def test_service_initialization(self, mock_config):
        """Test UploadThing service initialization."""
        mock_config.return_value.uploadthing_secret = "test-secret"
        mock_config.return_value.uploadthing_app_id = "test-app-id"
        mock_config.return_value.uploadthing_api_url = "https://api.uploadthing.com"
        
        service = UploadThingService()
        
        assert service.config is not None
        assert service.session is not None
    
    @patch('services.uploadthing_service.requests.Session')
    @patch('services.uploadthing_service.UploadThingConfig')
    def test_upload_video_no_secret(self, mock_config, mock_session):
        """Test upload_video when no secret is configured."""
        mock_config.return_value.uploadthing_secret = ""
        
        service = UploadThingService()
        
        # Create a temporary test file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_file.write(b"fake video content")
            temp_path = temp_file.name
        
        try:
            result = service.upload_video(temp_path)
            assert result is None
        finally:
            os.unlink(temp_path)
    
    @patch('services.uploadthing_service.requests.Session')
    @patch('services.uploadthing_service.UploadThingConfig')
    def test_upload_video_file_not_found(self, mock_config, mock_session):
        """Test upload_video with non-existent file."""
        mock_config.return_value.uploadthing_secret = "test-secret"
        
        service = UploadThingService()
        result = service.upload_video("/non/existent/file.mp4")
        
        assert result is None
    
    def test_get_uploadthing_service_singleton(self):
        """Test that get_uploadthing_service returns singleton."""
        service1 = get_uploadthing_service()
        service2 = get_uploadthing_service()
        
        assert service1 is service2


class TestApiClient:
    """Test API client service functionality."""
    
    @patch('services.api_client.ApiClientConfig')
    def test_api_client_initialization(self, mock_config):
        """Test API client initialization."""
        mock_config.return_value.frontend_api_base_url = "http://localhost:3000"
        mock_config.return_value.api_timeout_seconds = 30
        
        client = ApiClient()
        
        assert client.config is not None
        assert client.session is not None
    
    @patch('services.api_client.requests.Session')
    @patch('services.api_client.ApiClientConfig')
    def test_update_entry_video_url_success(self, mock_config, mock_session):
        """Test successful entry video URL update."""
        mock_config.return_value.frontend_api_base_url = "http://localhost:3000"
        mock_config.return_value.api_timeout_seconds = 30
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_response.raise_for_status.return_value = None
        
        mock_session_instance = Mock()
        mock_session_instance.post.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        client = ApiClient()
        result = client.update_entry_video_url("test-entry-123", "https://uploadthing.com/video.mp4")
        
        assert result is True
        mock_session_instance.post.assert_called_once()
    
    @patch('services.api_client.requests.Session')
    @patch('services.api_client.ApiClientConfig')
    def test_update_entry_video_url_failure(self, mock_config, mock_session):
        """Test failed entry video URL update."""
        mock_config.return_value.frontend_api_base_url = "http://localhost:3000"
        mock_config.return_value.api_timeout_seconds = 30
        
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.json.return_value = {"success": False, "error": "Internal error"}
        mock_response.raise_for_status.side_effect = Exception("HTTP 500")
        
        mock_session_instance = Mock()
        mock_session_instance.post.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        client = ApiClient()
        result = client.update_entry_video_url("test-entry-123", "https://uploadthing.com/video.mp4")
        
        assert result is False
    
    @patch('services.api_client.requests.Session')
    @patch('services.api_client.ApiClientConfig')
    def test_get_entry_details_success(self, mock_config, mock_session):
        """Test successful entry details retrieval."""
        mock_config.return_value.frontend_api_base_url = "http://localhost:3000"
        mock_config.return_value.api_timeout_seconds = 30
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "entry": {
                "id": "test-entry-123",
                "videoUrl": "https://uploadthing.com/video.mp4",
                "prompt": "Test prompt"
            }
        }
        mock_response.raise_for_status.return_value = None
        
        mock_session_instance = Mock()
        mock_session_instance.get.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        client = ApiClient()
        result = client.get_entry_details("test-entry-123")
        
        assert result is not None
        assert result["id"] == "test-entry-123"
        assert result["videoUrl"] == "https://uploadthing.com/video.mp4"
    
    def test_get_api_client_singleton(self):
        """Test that get_api_client returns singleton."""
        client1 = get_api_client()
        client2 = get_api_client()
        
        assert client1 is client2


class TestJobProcessorIntegration:
    """Test job processor integration with new services."""
    
    @patch('workers.job_processor.get_uploadthing_service')
    @patch('workers.job_processor.get_api_client')
    def test_upload_video_and_update_entry_success(self, mock_get_api_client, mock_get_uploadthing_service):
        """Test successful video upload and entry update."""
        from workers.job_processor import JobProcessor
        
        # Mock services
        mock_uploadthing_service = Mock()
        mock_uploadthing_service.upload_video.return_value = "https://uploadthing.com/video.mp4"
        mock_get_uploadthing_service.return_value = mock_uploadthing_service
        
        mock_api_client = Mock()
        mock_api_client.update_entry_video_url.return_value = True
        mock_get_api_client.return_value = mock_api_client
        
        # Create processor and test
        processor = JobProcessor()
        
        # Create a temporary test file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_file.write(b"fake video content")
            temp_path = temp_file.name
        
        try:
            result = processor._upload_video_and_update_entry(temp_path, "test-entry-123")
            
            assert result == "https://uploadthing.com/video.mp4"
            mock_uploadthing_service.upload_video.assert_called_once_with(temp_path)
            mock_api_client.update_entry_video_url.assert_called_once_with(
                "test-entry-123", "https://uploadthing.com/video.mp4"
            )
        finally:
            os.unlink(temp_path)
    
    @patch('workers.job_processor.get_uploadthing_service')
    @patch('workers.job_processor.get_api_client')
    def test_upload_video_and_update_entry_no_entry_id(self, mock_get_api_client, mock_get_uploadthing_service):
        """Test upload with no entry_id (should skip upload)."""
        from workers.job_processor import JobProcessor
        
        processor = JobProcessor()
        
        # Create a temporary test file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_file.write(b"fake video content")
            temp_path = temp_file.name
        
        try:
            result = processor._upload_video_and_update_entry(temp_path, None)
            
            assert result is None
            mock_get_uploadthing_service.assert_not_called()
            mock_get_api_client.assert_not_called()
        finally:
            os.unlink(temp_path)


def run_tests():
    """Run all tests manually (without pytest)."""
    print("🧪 Running Unit Tests for UploadThing Integration")
    print("=" * 60)
    
    test_classes = [TestJobModels, TestUploadThingService, TestApiClient, TestJobProcessorIntegration]
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        print(f"\n📋 Testing {test_class.__name__}")
        print("-" * 40)
        
        instance = test_class()
        methods = [method for method in dir(instance) if method.startswith('test_')]
        
        for method_name in methods:
            total_tests += 1
            try:
                method = getattr(instance, method_name)
                method()
                print(f"✅ {method_name}")
                passed_tests += 1
            except Exception as e:
                print(f"❌ {method_name}: {e}")
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")


if __name__ == "__main__":
    # Check if pytest is available
    try:
        import pytest
        print("Running tests with pytest...")
        pytest.main([__file__, "-v"])
    except ImportError:
        print("pytest not available, running manual tests...")
        run_tests()
