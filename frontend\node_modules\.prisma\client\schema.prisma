generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  civicId   String   @unique
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Entries {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String
  prompt    String
  videoUrl  String?
  quiz      Quiz?
  hasGiven  Boolean  @default(false)
  marks     Int?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

type Quiz {
  title       String
  description String
  questions   Question[]
}

type Question {
  type          String
  question      String
  options       String[]
  correctAnswer String
  explanation   String
  difficulty    String
}
