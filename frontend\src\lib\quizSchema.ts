import { z } from "zod";

// Reusable difficulty enum
const difficultyEnum = z.enum(["easy", "medium", "hard"]);

// Question types
const multipleChoiceSchema = z.object({
  type: z.literal("multiple-choice"),
  question: z.string(),
  options: z.array(z.string()).length(4), // exactly 4 options
  correctAnswer: z.number().min(0).max(3),
  explanation: z.string(),
  difficulty: difficultyEnum,
});

const trueFalseSchema = z.object({
  type: z.literal("true-false"),
  question: z.string(),
  correctAnswer: z.boolean(),
  explanation: z.string(),
  difficulty: difficultyEnum,
});

const fillBlankSchema = z.object({
  type: z.literal("fill-blank"),
  question: z.string(),
  correctAnswer: z.string(),
  explanation: z.string(),
  difficulty: difficultyEnum,
});

// Full quiz schema
export const quizSchema = z.object({
  title: z.string(),
  description: z.string(),
  questions: z.array(
    z.discriminatedUnion("type", [
      multipleChoiceSchema,
      trueFalseSchema,
      fillBlankSchema,
    ])
  ),
});

// TypeScript type for strong typing
export type Quiz = z.infer<typeof quizSchema>;
