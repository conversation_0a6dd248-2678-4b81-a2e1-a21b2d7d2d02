{"version": 3, "sources": [], "sections": [{"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/middleware.ts"], "sourcesContent": ["import { authMiddleware } from \"@civic/auth/nextjs/middleware\";\r\n\r\nexport default authMiddleware();\r\n\r\nexport const runtime = \"nodejs\";\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except:\r\n     * - _next directory (Next.js static files)\r\n     * - favicon.ico, sitemap.xml, robots.txt\r\n     * - image files\r\n     */\r\n    \"/((?!_next|favicon.ico|sitemap.xml|api|robots.txt|.*.jpg|.*.png|.*.svg|.*.gif).*)\",\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;;;;;;uCAEe,CAAA,GAAA,+JAAA,CAAA,iBAAc,AAAD;AAErB,MAAM,UAAU;AAEhB,MAAM,SAAS;IACpB,SAAS;QACP;;;;;KAKC,GACD;KACD;AACH", "debugId": null}}]}