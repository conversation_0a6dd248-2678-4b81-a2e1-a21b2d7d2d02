"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Video,
  Library,
  Brain,
  MessageCircle,
  Mail,
  FileText,
  Play,
  User,
  HelpCircle,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  ThumbsUp,
  ThumbsDown,
  Zap,
  CreditCard,
  Smartphone,
  Monitor,
  Tablet,
} from "lucide-react";
import { toast } from "sonner";

export default function Help() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("getting-started");
  const [feedbackRating, setFeedbackRating] = useState<number | null>(null);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  const categories = [
    {
      id: "getting-started",
      title: "Getting Started",
      icon: Play,
      description: "Learn the basics of video generation",
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      id: "video-generation",
      title: "Video Generation",
      icon: Video,
      description: "Master video creation features",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      id: "library-management",
      title: "Library Management",
      icon: Library,
      description: "Organize and manage your videos",
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      id: "quizzes",
      title: "Quizzes & Education",
      icon: Brain,
      description: "Create interactive content",
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      id: "account",
      title: "Account Management",
      icon: User,
      description: "Manage your account settings",
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
    },
    {
      id: "troubleshooting",
      title: "Troubleshooting",
      icon: AlertTriangle,
      description: "Solve common issues",
      color: "text-red-600",
      bgColor: "bg-red-50",
    },
  ];

  const quickLinks = [
    {
      title: "Generate Your First Video",
      description: "Step-by-step guide to creating videos",
      icon: Video,
      href: "#first-video",
      category: "getting-started",
    },
    {
      title: "Understanding Video Styles",
      description: "Learn about different generation styles",
      icon: Lightbulb,
      href: "#video-styles",
      category: "video-generation",
    },
    {
      title: "Managing Your Library",
      description: "Organize and search your videos",
      icon: Library,
      href: "#library-management",
      category: "library-management",
    },
    {
      title: "Billing & Subscriptions",
      description: "Manage your subscription plan",
      icon: CreditCard,
      href: "#billing",
      category: "account",
    },
    {
      title: "Video Generation Failed",
      description: "Troubleshoot generation issues",
      icon: AlertTriangle,
      href: "#generation-failed",
      category: "troubleshooting",
    },
    {
      title: "Contact Support",
      description: "Get help from our team",
      icon: MessageCircle,
      href: "#contact",
      category: "support",
    },
  ];

  const gettingStartedContent = [
    {
      id: "first-video",
      title: "Generate Your First Video",
      content: [
        {
          step: 1,
          title: "Navigate to Generate Video",
          description:
            "Click on 'Generate Video' in the sidebar or use the quick action button on the dashboard.",
          image:
            "/placeholder.svg?height=200&width=400&text=Generate+Video+Button",
        },
        {
          step: 2,
          title: "Write Your Prompt",
          description:
            "Describe the video you want to create. Be specific and descriptive. For example: 'A peaceful mountain lake at sunrise with mist rising from the water and pine trees in the foreground'.",
          image: "/placeholder.svg?height=200&width=400&text=Text+Prompt+Input",
        },
        {
          step: 3,
          title: "Configure Settings",
          description:
            "Choose your video duration (10-120 seconds), aspect ratio (16:9, 9:16, 1:1, 4:3), and style preset (realistic, artistic, cinematic, animated).",
          image: "/placeholder.svg?height=200&width=400&text=Video+Settings",
        },
        {
          step: 4,
          title: "Generate and Wait",
          description:
            "Click 'Generate Video' and wait 2-3 minutes for your video to be created. You can monitor progress in real-time.",
          image:
            "/placeholder.svg?height=200&width=400&text=Generation+Progress",
        },
      ],
    },
    {
      id: "platform-overview",
      title: "Platform Overview",
      content: [
        {
          step: 1,
          title: "Dashboard",
          description:
            "Your central hub showing recent videos, statistics, and quick actions.",
        },
        {
          step: 2,
          title: "Video Library",
          description:
            "Browse, search, and manage all your generated videos with YouTube-style interface.",
        },
        {
          step: 3,
          title: "Quizzes",
          description:
            "Create interactive quizzes based on your videos for educational content.",
        },
        {
          step: 4,
          title: "Profile Settings",
          description:
            "Manage your account, billing, preferences, and security settings.",
        },
      ],
    },
  ];

  const videoGenerationContent = [
    {
      id: "video-styles",
      title: "Understanding Video Styles",
      content: [
        {
          style: "Realistic",
          description:
            "Photorealistic videos with natural lighting and textures. Best for: Product demos, nature scenes, architectural visualizations.",
          examples: ["Ocean waves", "City landscapes", "Product showcases"],
        },
        {
          style: "Artistic",
          description:
            "Painterly and creative style with artistic flair. Best for: Creative content, abstract concepts, artistic expressions.",
          examples: [
            "Abstract art",
            "Stylized portraits",
            "Creative landscapes",
          ],
        },
        {
          style: "Cinematic",
          description:
            "Movie-like quality with dramatic lighting and composition. Best for: Storytelling, dramatic scenes, professional content.",
          examples: [
            "Epic landscapes",
            "Dramatic scenes",
            "Professional videos",
          ],
        },
        {
          style: "Animated",
          description:
            "Cartoon-style animation with vibrant colors. Best for: Educational content, children's content, explainer videos.",
          examples: [
            "Character animations",
            "Educational content",
            "Fun illustrations",
          ],
        },
      ],
    },
    {
      id: "prompt-writing",
      title: "Writing Effective Prompts",
      tips: [
        "Be specific and descriptive - include details about lighting, mood, and visual elements",
        "Mention the setting and environment clearly",
        "Describe the main subject or focus of the video",
        "Include information about camera angles or perspectives",
        "Specify the mood or atmosphere you want to create",
        "Use descriptive adjectives for better results",
      ],
      examples: [
        {
          bad: "A dog running",
          good: "A golden retriever running through a sunny meadow with wildflowers, shot from a low angle with warm golden hour lighting",
        },
        {
          bad: "City at night",
          good: "Bustling city street at night with neon lights reflecting on wet pavement, time-lapse style with flowing traffic",
        },
      ],
    },
  ];

  const troubleshootingContent = [
    {
      id: "generation-failed",
      title: "Video Generation Issues",
      issues: [
        {
          problem: "Video generation failed",
          causes: [
            "Prompt contains inappropriate content",
            "Server overload",
            "Technical error",
          ],
          solutions: [
            "Review your prompt for any inappropriate content",
            "Try generating again after a few minutes",
            "Simplify your prompt if it's too complex",
            "Contact support if the issue persists",
          ],
        },
        {
          problem: "Generation taking too long",
          causes: ["High server load", "Complex prompt", "Long video duration"],
          solutions: [
            "Wait patiently - complex videos take longer",
            "Try generating shorter videos",
            "Simplify your prompt",
            "Generate during off-peak hours",
          ],
        },
        {
          problem: "Poor video quality",
          causes: [
            "Vague prompt",
            "Inappropriate style choice",
            "Low resolution settings",
          ],
          solutions: [
            "Write more detailed and specific prompts",
            "Choose the appropriate style for your content",
            "Ensure high quality mode is enabled",
            "Try different aspect ratios",
          ],
        },
      ],
    },
  ];

  const faqs = [
    {
      category: "General",
      questions: [
        {
          question: "How long does it take to generate a video?",
          answer:
            "Video generation typically takes 1-3 minutes depending on the video length and complexity. Shorter videos (10-30 seconds) generate faster than longer ones (60-120 seconds).",
        },
        {
          question: "What makes a good text prompt?",
          answer:
            "Good prompts are descriptive and specific. Include details about the scene, lighting, mood, and any specific elements you want. For example: 'A peaceful mountain lake at sunrise with mist rising from the water and pine trees in the foreground' works better than just 'mountain lake'.",
        },
        {
          question: "Can I edit videos after they're generated?",
          answer:
            "Currently, you can edit video metadata (title, description) but not the video content itself. If you need changes to the video, you'll need to generate a new one with an updated prompt.",
        },
      ],
    },
    {
      category: "Billing & Plans",
      questions: [
        {
          question: "How many videos can I generate per month?",
          answer:
            "This depends on your subscription plan. The Free plan includes 10 videos per month, Pro plan includes 1000 videos, and Enterprise plans offer unlimited generation. Check your profile page to see your current usage.",
        },
        {
          question: "Can I use generated videos commercially?",
          answer:
            "Yes, all videos generated with Pro and Enterprise plans come with commercial usage rights. Free plan videos are for personal use only. Please review our terms of service for complete details.",
        },
        {
          question: "How do I upgrade my plan?",
          answer:
            "Go to your Profile > Billing section and click 'Upgrade Plan'. You can choose from Pro or Enterprise plans based on your needs.",
        },
      ],
    },
    {
      category: "Technical",
      questions: [
        {
          question: "What video formats are supported for download?",
          answer:
            "Generated videos are available in MP4 format with H.264 encoding. The resolution depends on your selected aspect ratio: 1080p for 16:9, 1080x1080 for 1:1, and 1920x1080 for 9:16 videos.",
        },
        {
          question: "How long are videos stored in my library?",
          answer:
            "Videos are stored permanently as long as your account is active. However, there are storage limits based on your plan. Free accounts get 5GB, Pro accounts get 100GB, and Enterprise accounts get unlimited storage.",
        },
        {
          question: "Can I download videos in different formats?",
          answer:
            "Currently, videos are only available in MP4 format. We're working on adding support for additional formats like MOV and AVI in future updates.",
        },
      ],
    },
  ];

  const filteredContent = () => {
    if (!searchQuery) return null;

    const allContent = [
      ...quickLinks,
      ...faqs.flatMap((category) =>
        category.questions.map((q) => ({
          title: q.question,
          description: q.answer,
          category: category.category.toLowerCase(),
          type: "faq",
        }))
      ),
      ...gettingStartedContent.map((item) => ({
        title: item.title,
        description: `Step-by-step guide for ${item.title.toLowerCase()}`,
        category: "getting-started",
        type: "guide",
      })),
    ];

    return allContent.filter(
      (item) =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const handleFeedback = (helpful: boolean) => {
    setFeedbackRating(helpful ? 1 : 0);
    setFeedbackSubmitted(true);
    toast.success(
      `Thank you for your feedback! We appreciate your input and will use it to improve our documentation.`
    );
  };

  const handleContactSubmit = () => {
    toast.success(
      "Your message has been sent! Our support team will get back to you shortly.")
  };

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">
            Help & Documentation
          </h1>
          <p className="text-muted-foreground">
            Find answers, guides, and support for VideoGen Pro
          </p>
        </div>
      </div>

      {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search help articles, guides, and FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 text-base"
            />
          </div>
          {searchQuery && (
            <div className="mt-4 space-y-2">
              <p className="text-sm text-muted-foreground">
                {filteredContent()?.length || 0} results found for "
                {searchQuery}"
              </p>
              {filteredContent()?.map((item, index) => (
                <div
                  key={index}
                  className="p-3 border rounded-lg hover:bg-muted/50 cursor-pointer"
                >
                  <h4 className="font-medium">{item.title}</h4>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {item.description}
                  </p>
                  <Badge variant="outline" className="mt-1 text-xs">
                    {item.category}
                  </Badge>
                </div>
              ))}
            </div>
          )}

      {!searchQuery && (
        <>

          {/* Main Content */}
          <Tabs value={activeCategory} onValueChange={setActiveCategory}>
            <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
              {categories.map((category) => (
                <TabsTrigger
                  key={category.id}
                  value={category.id}
                  className="text-xs"
                >
                  <category.icon className="h-4 w-4 mr-1" />
                  <span className="hidden sm:inline">{category.title}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {/* Getting Started */}
            <TabsContent value="getting-started" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Play className="h-5 w-5 text-green-600" />
                    Getting Started Guide
                  </CardTitle>
                  <CardDescription>
                    Everything you need to know to start generating videos
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {gettingStartedContent.map((section, index) => (
                      <AccordionItem key={section.id} value={section.id}>
                        <AccordionTrigger className="text-left">
                          {section.title}
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-6">
                            {section.content.map((step, stepIndex) => (
                              <div key={stepIndex} className="flex space-x-4">
                                <div className="flex-shrink-0">
                                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                                    {step.step || stepIndex + 1}
                                  </div>
                                </div>
                                <div className="flex-1 space-y-2">
                                  <h4 className="font-medium">{step.title}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {step.description}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Video Generation */}
            <TabsContent value="video-generation" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Video className="h-5 w-5 text-blue-600" />
                    Video Generation Guide
                  </CardTitle>
                  <CardDescription>
                    Master the art of AI video creation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {videoGenerationContent.map((section, index) => (
                      <AccordionItem key={section.id} value={section.id}>
                        <AccordionTrigger className="text-left">
                          {section.title}
                        </AccordionTrigger>
                        <AccordionContent>
                          {section.id === "video-styles" && (
                            <div className="space-y-4">
                              {section.content?.map((style, styleIndex) => (
                                <div
                                  key={styleIndex}
                                  className="border rounded-lg p-4"
                                >
                                  <h4 className="font-medium mb-2">
                                    {style.style}
                                  </h4>
                                  <p className="text-sm text-muted-foreground mb-3">
                                    {style.description}
                                  </p>
                                  <div className="space-y-1">
                                    <p className="text-xs font-medium text-muted-foreground">
                                      Examples:
                                    </p>
                                    <div className="flex flex-wrap gap-1">
                                      {style.examples?.map(
                                        (example, exampleIndex) => (
                                          <Badge
                                            key={exampleIndex}
                                            variant="outline"
                                            className="text-xs"
                                          >
                                            {example}
                                          </Badge>
                                        )
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                          {section.id === "prompt-writing" && (
                            <div className="space-y-6">
                              <div>
                                <h4 className="font-medium mb-3">
                                  Tips for Better Prompts
                                </h4>
                                <ul className="space-y-2">
                                  {section.tips?.map((tip, tipIndex) => (
                                    <li
                                      key={tipIndex}
                                      className="flex items-start space-x-2"
                                    >
                                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                      <span className="text-sm">{tip}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <h4 className="font-medium mb-3">
                                  Prompt Examples
                                </h4>
                                <div className="space-y-4">
                                  {section.examples?.map(
                                    (example, exampleIndex) => (
                                      <div
                                        key={exampleIndex}
                                        className="border rounded-lg p-4"
                                      >
                                        <div className="space-y-2">
                                          <div className="flex items-center space-x-2">
                                            <Badge
                                              variant="destructive"
                                              className="text-xs"
                                            >
                                              Poor
                                            </Badge>
                                            <span className="text-sm">
                                              {example.bad}
                                            </span>
                                          </div>
                                          <div className="flex items-center space-x-2">
                                            <Badge
                                              variant="default"
                                              className="text-xs"
                                            >
                                              Good
                                            </Badge>
                                            <span className="text-sm">
                                              {example.good}
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Troubleshooting */}
            <TabsContent value="troubleshooting" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    Troubleshooting Guide
                  </CardTitle>
                  <CardDescription>
                    Solutions for common issues and problems
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {troubleshootingContent.map((section, index) => (
                      <AccordionItem key={section.id} value={section.id}>
                        <AccordionTrigger className="text-left">
                          {section.title}
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-6">
                            {section.issues?.map((issue, issueIndex) => (
                              <div
                                key={issueIndex}
                                className="border rounded-lg p-4"
                              >
                                <h4 className="font-medium mb-3 text-red-600">
                                  {issue.problem}
                                </h4>
                                <div className="space-y-4">
                                  <div>
                                    <h5 className="text-sm font-medium mb-2">
                                      Possible Causes:
                                    </h5>
                                    <ul className="space-y-1">
                                      {issue.causes.map((cause, causeIndex) => (
                                        <li
                                          key={causeIndex}
                                          className="flex items-start space-x-2"
                                        >
                                          <AlertTriangle className="h-3 w-3 text-yellow-600 mt-1 flex-shrink-0" />
                                          <span className="text-sm text-muted-foreground">
                                            {cause}
                                          </span>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                  <div>
                                    <h5 className="text-sm font-medium mb-2">
                                      Solutions:
                                    </h5>
                                    <ul className="space-y-1">
                                      {issue.solutions.map(
                                        (solution, solutionIndex) => (
                                          <li
                                            key={solutionIndex}
                                            className="flex items-start space-x-2"
                                          >
                                            <CheckCircle className="h-3 w-3 text-green-600 mt-1 flex-shrink-0" />
                                            <span className="text-sm">
                                              {solution}
                                            </span>
                                          </li>
                                        )
                                      )}
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Other tabs content would go here... */}
            <TabsContent value="library-management" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Library Management</CardTitle>
                  <CardDescription>
                    Learn how to organize and manage your video library
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Content for library management will be added here...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="quizzes" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quizzes & Education</CardTitle>
                  <CardDescription>
                    Create interactive educational content
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Content for quizzes and education will be added here...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="account" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Account Management</CardTitle>
                  <CardDescription>
                    Manage your account settings and preferences
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Content for account management will be added here...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* FAQ Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-primary" />
                Frequently Asked Questions
              </CardTitle>
              <CardDescription>
                Find answers to common questions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="General">
                <TabsList className="grid w-full grid-cols-3">
                  {faqs.map((category) => (
                    <TabsTrigger
                      key={category.category}
                      value={category.category}
                    >
                      {category.category}
                    </TabsTrigger>
                  ))}
                </TabsList>
                {faqs.map((category) => (
                  <TabsContent
                    key={category.category}
                    value={category.category}
                  >
                    <Accordion type="single" collapsible className="w-full">
                      {category.questions.map((faq, index) => (
                        <AccordionItem
                          key={index}
                          value={`${category.category}-${index}`}
                        >
                          <AccordionTrigger className="text-left">
                            {faq.question}
                          </AccordionTrigger>
                          <AccordionContent className="text-muted-foreground">
                            {faq.answer}
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>

          {/* Feedback Section */}
          <Card>
            <CardHeader>
              <CardTitle>Was this helpful?</CardTitle>
              <CardDescription>
                Help us improve our documentation
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!feedbackSubmitted ? (
                <div className="flex items-center space-x-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFeedback(true)}
                    className="flex items-center space-x-2"
                  >
                    <ThumbsUp className="h-4 w-4" />
                    <span>Yes, helpful</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFeedback(false)}
                    className="flex items-center space-x-2"
                  >
                    <ThumbsDown className="h-4 w-4" />
                    <span>No, not helpful</span>
                  </Button>
                </div>
              ) : (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Thank you for your feedback!</span>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
