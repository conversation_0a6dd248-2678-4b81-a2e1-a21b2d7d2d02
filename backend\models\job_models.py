from datetime import datetime, timezone
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
import uuid


class JobStatus(str, Enum):
    """Job status enumeration."""

    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class JobType(str, Enum):
    """Job type enumeration."""

    RENDER = "render"
    BATCH_RENDER = "batch_render"


class RenderJobData(BaseModel):
    """Data model for a single render job."""

    script: str
    scene_name: Optional[str] = None


class BatchRenderJobData(BaseModel):
    """Data model for a batch render job."""

    scripts: List[RenderJobData]


class JobRequest(BaseModel):
    """Base job request model."""

    job_id: str = Field(default_factory=lambda: uuid.uuid4().hex)
    job_type: JobType
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Job-specific data
    render_data: Optional[RenderJobData] = None
    batch_render_data: Optional[BatchRenderJobData] = None

    # Metadata
    priority: int = Field(
        default=0, description="Job priority (higher = more priority)"
    )
    max_retries: int = Field(default=3, description="Maximum number of retries")
    timeout_seconds: int = Field(default=300, description="Job timeout in seconds")


class JobResult(BaseModel):
    """Job result model."""

    job_id: str
    success: bool
    output_urls: List[str] = Field(default_factory=list)
    error_message: Optional[str] = None
    processing_time_seconds: Optional[float] = None
    completed_at: Optional[datetime] = None


class JobStatusTracker(BaseModel):
    """Job status tracking model."""

    job_id: str
    job_type: JobType
    status: JobStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Progress tracking
    current_retry: int = Field(default=0)
    max_retries: int = Field(default=3)

    # Results
    result: Optional[JobResult] = None

    # Error tracking
    last_error: Optional[str] = None
    error_count: int = Field(default=0)

    # Metadata
    queue_name: str
    message_id: Optional[str] = None  # QStash message ID

    def is_terminal(self) -> bool:
        """Check if job is in a terminal state."""
        return self.status in [
            JobStatus.COMPLETED,
            JobStatus.FAILED,
            JobStatus.CANCELLED,
        ]

    def can_retry(self) -> bool:
        """Check if job can be retried."""
        return self.status == JobStatus.FAILED and self.current_retry < self.max_retries


class QueueStats(BaseModel):
    """Queue statistics model."""

    queue_name: str
    total_jobs: int = 0
    queued_jobs: int = 0
    processing_jobs: int = 0
    completed_jobs: int = 0
    failed_jobs: int = 0
    cancelled_jobs: int = 0

    def get_active_jobs(self) -> int:
        """Get count of active (non-terminal) jobs."""
        return self.queued_jobs + self.processing_jobs


# Request/Response models for API endpoints
class CreateRenderJobRequest(BaseModel):
    """Request model for creating a render job."""

    script: str
    scene_name: Optional[str] = None
    priority: int = Field(default=0, ge=0, le=10)


class CreateBatchRenderJobRequest(BaseModel):
    """Request model for creating a batch render job."""

    scripts: List[RenderJobData]
    priority: int = Field(default=0, ge=0, le=10)


class JobResponse(BaseModel):
    """Response model for job creation."""

    job_id: str
    status: JobStatus
    message: str
    estimated_completion_time: Optional[datetime] = None


class JobStatusResponse(BaseModel):
    """Response model for job status queries."""

    job_id: str
    job_type: JobType
    status: JobStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress_percentage: Optional[float] = None
    result: Optional[JobResult] = None
    error_message: Optional[str] = None
