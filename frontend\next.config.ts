import type { NextConfig } from "next";
import { createCivicAuthPlugin } from "@civic/auth/nextjs";

const nextConfig: NextConfig = {
  experimental: {
    nodeMiddleware: true,
  },
  images: {
    remotePatterns: [
      {
        hostname: "*",
      },
    ],
  },
};

const withCivicAuth = createCivicAuthPlugin({
  clientId: process.env.NEXT_PUBLIC_CIVIC_ID!,
  loginSuccessUrl: process.env.NEXT_PUBLIC_LOGIN_SUCCESS_URL,
  loginUrl: "/register"
});

export default withCivicAuth(nextConfig);
