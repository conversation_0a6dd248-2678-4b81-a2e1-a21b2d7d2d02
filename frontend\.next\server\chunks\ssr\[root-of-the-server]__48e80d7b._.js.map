{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_c943ad7.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_c943ad7-module__Cw_6NG__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_c943ad7.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22font%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,oJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,oJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,oJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON> } from \"next\";\r\nimport { <PERSON><PERSON><PERSON> } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport { ThemeProvider } from \"next-themes\";\r\nimport { CivicAuthProvider } from \"@civic/auth/nextjs\";\r\nimport { ModeToggle } from \"@/components/ui/mode-toggle\";\r\n\r\nconst font = Geist({ subsets: [\"latin\"] });\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"ClarifAI\",\r\n  description: \"Get detailed in depth animated explanations for any topic.\",\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <body className={font.className}>\r\n        <CivicAuthProvider>\r\n          <ThemeProvider attribute=\"class\">{children}</ThemeProvider>\r\n        </CivicAuthProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAAA;;;;;;;;;;AAKO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAW,wIAAA,CAAA,UAAI,CAAC,SAAS;sBAC7B,cAAA,8OAAC,gPAAA,CAAA,oBAAiB;0BAChB,cAAA,8OAAC,gJAAA,CAAA,gBAAa;oBAAC,WAAU;8BAAS;;;;;;;;;;;;;;;;;;;;;AAK5C", "debugId": null}}]}