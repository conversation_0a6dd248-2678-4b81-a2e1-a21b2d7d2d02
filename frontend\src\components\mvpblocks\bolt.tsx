"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Moon,
  Sun,
  Loader,
  Sparkles,
  Figma,
  FileUp,
  MonitorIcon,
  Terminal,
  CheckCircle2,
  Video,
  Clock,
  ArrowRight,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useCompletion } from "@ai-sdk/react";
import SyntaxHighlighter from "react-syntax-highlighter";
import { atomOneDark } from "react-syntax-highlighter/dist/cjs/styles/hljs";
import { useUser } from "@civic/auth/react";
import { redirect } from "next/navigation";
import { set } from "zod";
        
const EXAMPLE_ACTIONS = [
  { icon: <Figma className="h-4 w-4" />, text: "Bayesian Theorem in ML" },
  { icon: <FileUp className="h-4 w-4" />, text: "Hidden Markov Models (HMM)" },
  {
    icon: <MonitorIcon className="h-4 w-4" />,
    text: "Gaussian Mixture Models",
  },
  { icon: <Terminal className="h-4 w-4" />, text: "Linked Lists in DSA" },
  { icon: <FileUp className="h-4 w-4" />, text: "Binary Trees in DSA" },
  { icon: <Figma className="h-4 w-4" />, text: "Quadratic Equations in Maths" },
  {
    icon: <FileUp className="h-4 w-4" />,
    text: "Projectile Motion in Physics",
  },
  {
    icon: <MonitorIcon className="h-4 w-4" />,
    text: "Dynamic Programming in DSA",
  },
  {
    icon: <Terminal className="h-4 w-4" />,
    text: "Eigenvalues and Eigenvectors",
  },
  { icon: <FileUp className="h-4 w-4" />, text: "Fourier Transform in Maths" },
  { icon: <Figma className="h-4 w-4" />, text: "Convex Optimization in Maths" },
  { icon: <MonitorIcon className="h-4 w-4" />, text: "Graph Theory in DSA" },
  { icon: <Terminal className="h-4 w-4" />, text: "Quantum Mechanics Basics" },
  { icon: <FileUp className="h-4 w-4" />, text: "Neural Networks in ML" },
];

interface ScriptItem {
  title: string;
  description: string;
  code?: string;
}

interface Task {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "failed";
}

export default function Bolt() {
  const { user } = useUser();
  if (!user) {
    return redirect("/register");
  }

  console.log("User:", user.id);
  const [darkMode, setDarkMode] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [scriptHistory, setScriptHistory] = useState<ScriptItem[][]>([]);
  const [currentScripts, setCurrentScripts] = useState<ScriptItem[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [manimStreams, setManimStreams] = useState<Record<string, string>>({});
  const [isGeneratingScript, setIsGeneratingScript] = useState(false);
  const [currentGeneratingScene, setCurrentGeneratingScene] = useState<
    string | null
  >(null);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [isRenderingVideo, setIsRenderingVideo] = useState(false);
  const [videoJobId, setVideoJobId] = useState<string | null>(null);

  const [titles, setTitles] = useState<string[]>([]);
  const [description, setDescription] = useState<string[]>([]);

  const {
    complete: enhancePrompt,
    isLoading: isEnhancing,
    completion,
  } = useCompletion({
    api: "/api/enhance",
  });

  useEffect(() => {
    setInputValue(completion);
  }, [completion]);

  const generateScriptWithFetch = async (prompt: string) => {
    try {
      setIsGeneratingScript(true);
      const response = await fetch("/api/generate-script", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const generatedScripts: ScriptItem[] = await response.json();
      setTitles(generatedScripts.map((script) => script.title));
      setDescription(generatedScripts.map((script) => script.description));

      setCurrentScripts(generatedScripts);
      setScriptHistory((prev) => [...prev, generatedScripts]);

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === "1"
            ? { ...task, status: "completed" }
            : task.id === "2"
            ? { ...task, status: "completed" }
            : task
        )
      );

      // Generate quiz after scripts are generated
      await generateQuizWithFetch(
        generatedScripts.map((script) => script.title),
        generatedScripts.map((script) => script.description)
      );

      generateManimCodes(generatedScripts);
    } catch (error) {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.status === "in-progress"
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
    } finally {
      setIsGeneratingScript(false);
    }
  };

  const generateQuizWithFetch = async (
    title: string[],
    description: string[]
  ) => {
    try {
      const combinedTitle = title.map((t) => t.trim()).join(",");
      const combinedDescription = description.map((d) => d.trim()).join("\n");
      const userId = user.id;
      const response = await fetch("/api/ai-quiz", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: combinedTitle,
          content: combinedDescription,
          userId: userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === `quiz-${title}`
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
    }
  };

  const generateManimCodeWithFetch = async (
    title: string,
    description: string
  ) => {
    try {
      const response = await fetch("/api/manim", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          schema: { title, description },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Update manimStreams
      setManimStreams((prev) => ({
        ...prev,
        [title]: data.code,
      }));

      // Update currentScripts to add code to corresponding script
      setCurrentScripts((prev) =>
        prev.map((script) =>
          script.title === title ? { ...script, code: data.code } : script
        )
      );

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === `manim-${title}` ? { ...task, status: "completed" } : task
        )
      );
    } catch (error) {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === `manim-${title}`
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
    }
  };

  const renderVideoWithBackend = async (scripts: ScriptItem[]) => {
    try {
      setIsRenderingVideo(true);

      // Prepare scripts for backend
      const renderRequests = scripts
        .filter((script) => script.code) // Only include scripts with generated code
        .map((script) => ({
          script: script.code!,
          scene_name: "Scene1", // All scenes use Scene1 as per the Manim API
        }));

      if (renderRequests.length === 0) {
        throw new Error("No valid scripts to render");
      }

      // Send to backend for batch rendering
      const response = await fetch("http://localhost:8000/batch_render", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ scripts: renderRequests }),
      });

      if (!response.ok) {
        throw new Error(`Backend error: ${response.status}`);
      }

      const result = await response.json();
      setVideoJobId(result.job_id);

      // Update tasks to show video rendering started
      setTasks((prevTasks) => [
        ...prevTasks,
        {
          id: "4",
          name: "Rendering Video",
          status: "completed",
        },
      ]);

      // Show confirmation dialog
      setShowConfirmationDialog(true);
    } catch (error) {
      console.error("Error rendering video:", error);
      setTasks((prevTasks) => [
        ...prevTasks,
        {
          id: "4",
          name: `Video Rendering Failed: ${
            error instanceof Error ? error.message : String(error)
          }`,
          status: "failed",
        },
      ]);
    } finally {
      setIsRenderingVideo(false);
    }
  };

  const generateManimCodes = async (scripts: ScriptItem[]) => {
    const manimTasks: Task[] = scripts.map((script) => ({
      id: `manim-${script.title}`,
      name: `Generating: ${script.title}`,
      status: "pending",
    }));

    setTasks((prevTasks) => [
      ...prevTasks.filter((t) => t.id !== "3"),
      ...manimTasks,
      { id: "3", name: "Generating Animations", status: "in-progress" },
    ]);

    // Keep track of scripts with generated code
    const scriptsWithCode: ScriptItem[] = [];

    for (const scriptItem of scripts) {
      setCurrentGeneratingScene(scriptItem.title);
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === `manim-${scriptItem.title}`
            ? { ...task, status: "in-progress" }
            : task
        )
      );

      await generateManimCodeWithFetch(
        scriptItem.title,
        scriptItem.description
      );

      // Find the updated script with code from currentScripts
      const updatedScript = currentScripts.find(
        (s) => s.title === scriptItem.title && s.code
      );
      if (updatedScript) {
        scriptsWithCode.push(updatedScript);
      }
    }
    setCurrentGeneratingScene(null);

    // Mark animation generation as complete
    setTasks((prevTasks) =>
      prevTasks.map((task) =>
        task.id === "3" ? { ...task, status: "completed" } : task
      )
    );

    // Start video rendering after all Manim codes are generated
    if (scriptsWithCode.length > 0) {
      // Wait a bit to ensure state is updated
      setTimeout(async () => {
        const finalScripts = currentScripts.filter((script) => script.code);
        if (finalScripts.length > 0) {
          await renderVideoWithBackend(finalScripts);
        }
      }, 500);
    }
  };

  const handleGenerateClick = async () => {
    if (!inputValue.trim()) return;

    setIsGenerating(true);
    setCurrentScripts([]);
    setManimStreams({});
    setScriptHistory((prev) => [...prev]);
    setCurrentGeneratingScene(null);

    const initialTasks: Task[] = [
      { id: "1", name: "Analyzing Input", status: "in-progress" },
      { id: "2", name: "Generating Script", status: "pending" },
      { id: "3", name: "Generating Animations", status: "pending" },
    ];
    setTasks(initialTasks);

    try {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === "2" ? { ...task, status: "in-progress" } : task
        )
      );
      // Generate script using fetch
      await generateScriptWithFetch(inputValue);
    } catch (error) {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.status === "in-progress"
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
      setCurrentGeneratingScene(null);
    }
  };

  const getStatusColor = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return "text-green-500";
      case "in-progress":
        return "text-blue-500";
      case "pending":
        return "text-yellow-500";
      case "failed":
        return "text-destructive";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <AnimatePresence>
      {!isGenerating ? (
        <motion.div
          key="idle-ui"
          className="selection-accent flex flex-grow flex-col py-20 w-full items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4 }}
        >
          <div className="mx-4 flex flex-col items-center">
            <div className="mb-12 text-center">
              <h1 className="mb-6 text-5xl md:text-6xl font-medium tracking-tight text-transparent bg-clip-text bg-gradient-to-br from-foreground to-muted/70 via-foreground/80">
                What do you want to learn?
              </h1>
              <p className="text-lg text-muted-foreground max-w-md mx-auto">
                Create animated explanations for any complex topic in minutes.
                For both{" "}
                <span className="font-medium text-foreground">students</span>{" "}
                and{" "}
                <span className="font-medium text-foreground">teachers</span> .
              </p>
            </div>

            <div className="mx-auto mb-6 w-full max-w-xl">
              <div className="shadow-xl dark:shadow-primary/20 dark:shadow-2xl relative rounded-lg">
                <div className="flex flex-col rounded-lg border bg-gradient-to-b from-secondary/40 to-background p-3 pb-6 relative overflow-hidden">
                  <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none"></div>
                  <div className="absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none blur-2xl"></div>
                  <textarea
                    placeholder="Explain bayes theorem in machine learning"
                    className="h-32 w-full outline-none resize-none text-sm"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                  />
                  <div className="mt-auto flex gap-2 absolute bottom-2 right-2 ">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="backdrop-blur-lg shadow"
                      disabled={
                        !inputValue.trim() ||
                        isEnhancing ||
                        inputValue.length < 6 ||
                        inputValue.length > 300
                      }
                      onClick={() => {
                        if (!inputValue.trim()) return;
                        enhancePrompt(inputValue.trim());
                      }}
                    >
                      {isEnhancing ? (
                        <Loader className="animate-spin size-4" />
                      ) : (
                        <Sparkles className="size-4" />
                      )}
                      {isEnhancing ? "Enhancing..." : "Enhance"}
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleGenerateClick}
                      disabled={
                        !inputValue.trim() || isEnhancing || isGeneratingScript
                      }
                    >
                      {isGeneratingScript ? (
                        <>
                          <Loader className="animate-spin size-4 mr-2" />
                          Generating...
                        </>
                      ) : (
                        "Generate"
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="mx-auto mt-16 flex w-full max-w-6xl flex-wrap justify-center gap-2">
              {EXAMPLE_ACTIONS.map((action, index) => (
                <Button
                  key={index}
                  size="sm"
                  variant="outline"
                  className="rounded-full px-4 py-0.5 text-xs"
                >
                  {action.icon}
                  <span>{action.text}</span>
                </Button>
              ))}
            </div>
          </div>
        </motion.div>
      ) : (
        <div className="flex h-screen w-full bg-background text-foreground overflow-hidden">
          <motion.div
            className="w-full md:w-[30%] min-w-[300px] bg-gradient-to-b from-background to-secondary/20 border-r border-border/50 flex flex-col relative backdrop-blur-xl"
            animate={{
              width: isGenerating ? "30%" : "100%",
            }}
            transition={{ duration: 0.7, ease: [0.32, 0.72, 0, 1] }}
          >
            <div className="p-6 border-b border-border/50 bg-background/80 backdrop-blur-sm">
              <div className="flex items-center">Clarif.AI</div>
            </div>

            <div className="flex-1 overflow-auto">
              <div className="p-6">
                {/* Task List */}
                <div className="mb-8">
                  <div className="flex items-center mb-4">
                    <div className="w-1 h-6 bg-gradient-to-b from-primary to-primary/50 rounded-full mr-3"></div>
                    <h3 className="text-lg font-semibold">Progress</h3>
                  </div>
                  <div className="space-y-3">
                    {tasks.map((task) => (
                      <div
                        key={task.id}
                        className={`group flex items-center p-4 rounded-xl border transition-all duration-200 ${
                          task.status === "in-progress"
                            ? "bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20 shadow-lg shadow-primary/5"
                            : task.status === "completed"
                            ? "bg-gradient-to-r from-green-500/10 to-emerald-500/10 border-green-500/20"
                            : task.status === "failed"
                            ? "bg-gradient-to-r from-destructive/10 to-red-500/10 border-destructive/20"
                            : "bg-secondary/30 border-border/50 hover:bg-secondary/50"
                        }`}
                      >
                        <div className="relative mr-4">
                          <div
                            className={`w-3 h-3 rounded-full transition-all duration-300 ${
                              task.status === "completed"
                                ? "bg-gradient-to-r from-green-400 to-green-600 shadow-lg shadow-green-500/30"
                                : task.status === "in-progress"
                                ? "bg-gradient-to-r from-primary to-primary/70 shadow-lg shadow-primary/30 animate-pulse"
                                : task.status === "pending"
                                ? "bg-gradient-to-r from-yellow-400 to-orange-500 shadow-lg shadow-yellow-500/30"
                                : "bg-gradient-to-r from-destructive to-red-600 shadow-lg shadow-destructive/30"
                            }`}
                          />
                          {task.status === "in-progress" && (
                            <div className="absolute -inset-1 rounded-full bg-primary/20 animate-ping"></div>
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                            {task.name}
                          </div>
                          <div
                            className={`text-xs mt-1 font-medium ${getStatusColor(
                              task.status
                            )}`}
                          >
                            {task.status.replace("-", " ").toUpperCase()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* History Section */}
                {scriptHistory.length > 0 && (
                  <div>
                    <div className="flex items-center mb-4">
                      <div className="w-1 h-6 bg-gradient-to-b from-secondary to-muted rounded-full mr-3"></div>
                      <h3 className="text-lg font-semibold">History</h3>
                    </div>
                    <Accordion
                      type="single"
                      collapsible
                      className="w-full space-y-3"
                    >
                      {scriptHistory.map((scripts, index) => (
                        <AccordionItem
                          value={`history-${index}`}
                          key={`history-${index}`}
                          className="border-0"
                        >
                          <div className="bg-gradient-to-r from-secondary/30 to-muted/30 rounded-xl border border-border/50 overflow-hidden hover:shadow-md transition-all duration-200">
                            <AccordionTrigger className="hover:no-underline px-4 py-3 hover:bg-secondary/20 transition-colors">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-green-400 to-emerald-500 mr-3 shadow-lg shadow-green-500/30" />
                                <span className="font-medium">
                                  Generation {scriptHistory.length - index}
                                </span>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 pb-4">
                              <ul className="space-y-2 mb-3">
                                {scripts.map((script, sIndex) => (
                                  <li
                                    key={sIndex}
                                    className="text-sm p-3 hover:bg-primary/5 rounded-lg cursor-pointer border border-transparent hover:border-primary/10 transition-all duration-200 text-muted-foreground hover:text-foreground"
                                  >
                                    {script.title}
                                  </li>
                                ))}
                              </ul>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20 hover:from-primary/10 hover:to-secondary/10 hover:border-primary/30 transition-all duration-200"
                                onClick={() => {
                                  setCurrentScripts(scripts);
                                  setTasks([
                                    {
                                      id: "h1",
                                      name: "Previewing Script",
                                      status: "completed",
                                    },
                                    {
                                      id: "h2",
                                      name: "Previewing Animations",
                                      status: "completed",
                                    },
                                  ]);
                                }}
                              >
                                Preview
                              </Button>
                            </AccordionContent>
                          </div>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  </div>
                )}
              </div>
            </div>

            <div className="p-6 border-t border-border/50 bg-background/80 backdrop-blur-sm">
              <div className="flex justify-between items-center">
                <div className="text-xs text-muted-foreground font-medium">
                  <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                    {tasks.filter((t) => t.status === "completed").length}
                  </span>
                  <span className="mx-1">/</span>
                  <span>{tasks.length}</span>
                  <span className="ml-1">tasks completed</span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setDarkMode(!darkMode)}
                  className="text-muted-foreground hover:text-foreground hover:bg-secondary/50 transition-all duration-200 rounded-lg"
                >
                  {darkMode ? (
                    <Sun className="h-4 w-4" />
                  ) : (
                    <Moon className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </motion.div>

          {/* Right Panel */}
          <AnimatePresence>
            {isGenerating && (
              <motion.div
                className="bg-gradient-to-br from-background to-secondary/10 flex-1 flex flex-col absolute top-0 bottom-0 right-0 h-full backdrop-blur-xl border-l border-border/50"
                initial={{ width: "0%", x: "100%" }}
                animate={{ width: "70%", x: "0%" }}
                exit={{ width: "0%", x: "100%" }}
                transition={{ duration: 0.7, ease: [0.32, 0.72, 0, 1] }}
              >
                <div className="flex items-center justify-between px-6 py-4 border-b border-border/50 bg-background/80 backdrop-blur-sm">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-secondary animate-pulse"></div>
                    <div className="text-sm font-medium">Generated Content</div>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setIsGenerating(false)}
                    className="text-muted-foreground hover:text-foreground hover:bg-secondary/50 transition-all duration-200 rounded-lg"
                  >
                    Close
                  </Button>
                </div>

                <Tabs defaultValue="script" className="flex-1 flex flex-col">
                  <TabsList className="flex px-6 pt-3 pb-1 bg-transparent border-b border-border/50">
                    <TabsTrigger
                      value="script"
                      className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/10 data-[state=active]:to-secondary/10 data-[state=active]:text-primary data-[state=active]:border-primary/20 data-[state=active]:shadow-sm rounded-lg px-4 py-2 transition-all duration-200"
                    >
                      Script
                    </TabsTrigger>
                    <TabsTrigger
                      value="preview"
                      className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/10 data-[state=active]:to-secondary/10 data-[state=active]:text-primary data-[state=active]:border-primary/20 data-[state=active]:shadow-sm rounded-lg px-4 py-2 transition-all duration-200"
                    >
                      Animation Preview
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent
                    value="script"
                    className="flex-1 overflow-hidden"
                  >
                    <ScrollArea className="h-full p-6">
                      {currentScripts.length > 0 ? (
                        <div className="space-y-4">
                          {currentScripts.map((item, index) => (
                            <Card
                              key={index}
                              className="border-border/50 bg-gradient-to-r from-background to-secondary/5 hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 overflow-hidden"
                            >
                              <CardContent className="p-0">
                                <Accordion type="single" collapsible>
                                  <AccordionItem
                                    value={item.title}
                                    className="border-0"
                                  >
                                    <AccordionTrigger className="px-6 py-4 hover:no-underline font-medium hover:bg-secondary/20 transition-colors">
                                      <div className="flex items-center">
                                        <div className="relative mr-3">
                                          {item.code ? (
                                            <div className="w-3 h-3 rounded-full bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg shadow-green-500/30" />
                                          ) : tasks.find(
                                              (t) =>
                                                t.id === `manim-${item.title}`
                                            )?.status === "in-progress" ? (
                                            <>
                                              <div className="w-3 h-3 rounded-full bg-gradient-to-r from-primary to-secondary shadow-lg shadow-primary/30 animate-pulse" />
                                              <div className="absolute -inset-1 rounded-full bg-primary/20 animate-ping"></div>
                                            </>
                                          ) : (
                                            <div className="w-3 h-3 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 shadow-lg shadow-yellow-500/30" />
                                          )}
                                        </div>
                                        <span className="text-left">
                                          {item.title}
                                        </span>
                                      </div>
                                    </AccordionTrigger>
                                    <AccordionContent className="px-6 pb-6 pt-2">
                                      <div className="bg-gradient-to-r from-muted/50 to-secondary/30 rounded-lg p-4 mb-4 border border-border/30">
                                        <p className="text-sm text-muted-foreground leading-relaxed">
                                          {item.description}
                                        </p>
                                      </div>
                                      {item.code && (
                                        <div className="rounded-lg overflow-hidden border border-border/30 bg-gradient-to-br from-background to-secondary/20">
                                          <div className="flex items-center justify-between px-4 py-2 bg-secondary/30 border-b border-border/30">
                                            <span className="text-xs font-medium text-muted-foreground">
                                              Python (Manim)
                                            </span>
                                            <div className="flex space-x-1">
                                              <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                              <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                                              <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                            </div>
                                          </div>
                                          <SyntaxHighlighter
                                            language="python"
                                            style={atomOneDark}
                                            customStyle={{
                                              fontSize: "0.85rem",
                                              padding: "1.5rem",
                                              margin: 0,
                                              backgroundColor: "transparent",
                                              lineHeight: "1.6",
                                            }}
                                          >
                                            {item.code}
                                          </SyntaxHighlighter>
                                        </div>
                                      )}
                                    </AccordionContent>
                                  </AccordionItem>
                                </Accordion>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <div className="text-center">
                            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                              <Loader className="w-8 h-8 text-primary animate-spin" />
                            </div>
                            <p className="text-muted-foreground font-medium">
                              {tasks.some(
                                (t) =>
                                  t.status === "in-progress" ||
                                  t.status === "pending"
                              )
                                ? "Generating your script..."
                                : "No script generated yet."}
                            </p>
                          </div>
                        </div>
                      )}
                    </ScrollArea>
                  </TabsContent>

                  <TabsContent
                    value="preview"
                    className="flex-1 overflow-hidden bg-gradient-to-br from-slate-950 to-gray-900"
                  >
                    <ScrollArea className="h-full">
                      <div className="p-6 space-y-6">
                        {currentGeneratingScene && (
                          <div className="bg-gradient-to-br from-slate-800/80 to-gray-800/80 rounded-xl p-6 border border-primary/20 backdrop-blur-sm">
                            <div className="flex items-center mb-4">
                              <div className="relative mr-3">
                                <div className="w-3 h-3 rounded-full bg-gradient-to-r from-primary to-secondary animate-pulse shadow-lg shadow-primary/50" />
                                <div className="absolute -inset-1 rounded-full bg-primary/20 animate-ping"></div>
                              </div>
                              <h3 className="text-lg font-mono font-semibold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                                Generating: {currentGeneratingScene}
                              </h3>
                            </div>
                            {manimStreams[currentGeneratingScene] ? (
                              <div className="rounded-lg overflow-hidden border border-border/30">
                                <div className="flex items-center justify-between px-4 py-2 bg-slate-700/50 border-b border-border/30">
                                  <span className="text-xs font-medium text-slate-300">
                                    Live Generation
                                  </span>
                                  <div className="flex space-x-1">
                                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                    <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                  </div>
                                </div>
                                <SyntaxHighlighter
                                  language="python"
                                  style={atomOneDark}
                                  customStyle={{
                                    fontSize: "0.85rem",
                                    backgroundColor: "rgba(15, 23, 42, 0.8)",
                                    padding: "1.5rem",
                                    margin: 0,
                                    lineHeight: "1.6",
                                  }}
                                >
                                  {manimStreams[currentGeneratingScene]}
                                </SyntaxHighlighter>
                              </div>
                            ) : (
                              <div className="text-slate-400 text-sm bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
                                <div className="flex items-center">
                                  <Loader className="animate-spin h-4 w-4 mr-3 text-primary" />
                                  <span>Streaming MANIM code...</span>
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {currentScripts
                          .filter(
                            (script) =>
                              script.title !== currentGeneratingScene &&
                              script.code
                          )
                          .map((script, index) => (
                            <div
                              key={index}
                              className="bg-gradient-to-br from-slate-800/80 to-gray-800/80 rounded-xl p-6 border border-emerald-500/20 backdrop-blur-sm"
                            >
                              <h3 className="text-lg font-mono font-semibold bg-gradient-to-r from-emerald-400 to-green-500 bg-clip-text text-transparent mb-4">
                                {script.title}
                              </h3>
                              <div className="rounded-lg overflow-hidden border border-border/30">
                                <div className="flex items-center justify-between px-4 py-2 bg-slate-700/50 border-b border-border/30">
                                  <span className="text-xs font-medium text-slate-300">
                                    Completed
                                  </span>
                                  <div className="flex space-x-1">
                                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                    <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                  </div>
                                </div>
                                <SyntaxHighlighter
                                  language="python"
                                  style={atomOneDark}
                                  customStyle={{
                                    fontSize: "0.85rem",
                                    backgroundColor: "rgba(15, 23, 42, 0.8)",
                                    padding: "1.5rem",
                                    margin: 0,
                                    lineHeight: "1.6",
                                  }}
                                >
                                  {script.code || ""}
                                </SyntaxHighlighter>
                              </div>
                            </div>
                          ))}
                      </div>
                    </ScrollArea>
                  </TabsContent>
                </Tabs>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Video Rendering Confirmation Dialog */}
      <Dialog
        open={showConfirmationDialog}
        onOpenChange={setShowConfirmationDialog}
      >
        <DialogContent className="sm:max-w-md bg-gradient-to-br from-background to-secondary/10 border border-primary/20">
          <DialogHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full flex items-center justify-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              >
                <CheckCircle2 className="w-8 h-8 text-green-500" />
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                Video Generation Started!
              </DialogTitle>
            </motion.div>
          </DialogHeader>

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <DialogDescription className="text-center text-muted-foreground leading-relaxed">
              Your animated educational video is now being generated. This
              process typically takes 3-5 minutes.
            </DialogDescription>

            {videoJobId && (
              <div className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-4 border border-primary/10">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-secondary animate-pulse"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-foreground">
                      Job ID
                    </p>
                    <p className="text-xs text-muted-foreground font-mono">
                      {videoJobId}
                    </p>
                  </div>
                  <Video className="w-5 h-5 text-primary" />
                </div>
              </div>
            )}

            <div className="bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-lg p-4 border border-blue-500/10">
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <p className="text-sm font-medium text-foreground">
                    What happens next?
                  </p>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li className="flex items-center space-x-2">
                      <div className="w-1 h-1 rounded-full bg-blue-500"></div>
                      <span>
                        Your video will appear in the dashboard gallery
                      </span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1 h-1 rounded-full bg-blue-500"></div>
                      <span>You'll be able to download and share it</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1 h-1 rounded-full bg-blue-500"></div>
                      <span>Processing time: 3-5 minutes</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setShowConfirmationDialog(false)}
              className="w-full sm:w-auto"
            >
              Continue Working
            </Button>
            <Button
              onClick={() => {
                setShowConfirmationDialog(false);
                // Navigate to dashboard - you can implement this navigation
                window.location.href = "/dashboard";
              }}
              className="w-full sm:w-auto bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90"
            >
              <span>Go to Dashboard</span>
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AnimatePresence>
  );
}
