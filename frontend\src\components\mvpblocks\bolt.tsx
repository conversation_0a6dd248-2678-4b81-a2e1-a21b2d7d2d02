"use client";

import { useState } from "react";
import { useUser } from "@civic/auth/react";
import { redirect } from "next/navigation";
import { AnimatePresence } from "framer-motion";
import InputSection from "./InputSection";
import TaskProgressSidebar from "./TaskProgressSidebar";
import ContentDisplayPanel from "./ContentDisplayPanel";
import VideoConfirmationDialog from "./VideoConfirmationDialog";

interface ScriptItem {
  title: string;
  description: string;
  code?: string;
}

interface Task {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "failed";
}

export default function Bolt() {
  const { user } = useUser();
  if (!user) {
    return redirect("/register");
  }

  const [isGenerating, setIsGenerating] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [scriptHistory, setScriptHistory] = useState<ScriptItem[][]>([]);
  const [currentScripts, setCurrentScripts] = useState<ScriptItem[]>([]);
  const [manimStreams, setManimStreams] = useState<Record<string, string>>({});
  const [isGeneratingScript, setIsGeneratingScript] = useState(false);
  const [currentGeneratingScene, setCurrentGeneratingScene] = useState<
    string | null
  >(null);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [isRenderingVideo, setIsRenderingVideo] = useState(false);
  const [videoJobId, setVideoJobId] = useState<string | null>(null);
  const [titles, setTitles] = useState<string[]>([]);
  const [description, setDescription] = useState<string[]>([]);

  const generateScriptWithFetch = async (prompt: string) => {
    try {
      setIsGeneratingScript(true);
      const response = await fetch("/api/generate-script", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const generatedScripts: ScriptItem[] = await response.json();
      setTitles(generatedScripts.map((script) => script.title));
      setDescription(generatedScripts.map((script) => script.description));

      setCurrentScripts(generatedScripts);
      setScriptHistory((prev) => [...prev, generatedScripts]);

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === "1"
            ? { ...task, status: "completed" }
            : task.id === "2"
            ? { ...task, status: "completed" }
            : task
        )
      );

      await generateQuizWithFetch(
        generatedScripts.map((script) => script.title),
        generatedScripts.map((script) => script.description)
      );

      generateManimCodes(generatedScripts);
    } catch (error) {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.status === "in-progress"
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
    } finally {
      setIsGeneratingScript(false);
    }
  };

  const generateQuizWithFetch = async (
    title: string[],
    description: string[]
  ) => {
    try {
      const combinedTitle = title.map((t) => t.trim()).join(",");
      const combinedDescription = description.map((d) => d.trim()).join("\n");
      const userId = user.id;
      const response = await fetch("/api/ai-quiz", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: combinedTitle,
          content: combinedDescription,
          userId: userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === `quiz-${title}`
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
    }
  };

  const generateManimCodeWithFetch = async (
    title: string,
    description: string
  ) => {
    try {
      const mainTheme = localStorage.getItem("currentPrompt") || "";

      const response = await fetch("/api/manim", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          schema: { title, description },
          mainTheme: mainTheme,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      setManimStreams((prev) => ({
        ...prev,
        [title]: data.code,
      }));

      setCurrentScripts((prev) =>
        prev.map((script) =>
          script.title === title ? { ...script, code: data.code } : script
        )
      );

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === `manim-${title}` ? { ...task, status: "completed" } : task
        )
      );
    } catch (error) {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === `manim-${title}`
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
    }
  };

  const renderVideoWithBackend = async (scripts: ScriptItem[]) => {
    try {
      setIsRenderingVideo(true);

      const renderRequests = scripts
        .filter((script) => script.code)
        .map((script) => ({
          script: script.code!,
          scene_name: "Scene1",
        }));

      if (renderRequests.length === 0) {
        throw new Error("No valid scripts to render");
      }

      const response = await fetch("http://localhost:8000/batch_render", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ scripts: renderRequests }),
      });

      if (!response.ok) {
        throw new Error(`Backend error: ${response.status}`);
      }

      const result = await response.json();
      setVideoJobId(result.job_id);

      setTasks((prevTasks) => [
        ...prevTasks,
        {
          id: "4",
          name: "Rendering Video",
          status: "completed",
        },
      ]);

      setShowConfirmationDialog(true);
    } catch (error) {
      console.error("Error rendering video:", error);
      setTasks((prevTasks) => [
        ...prevTasks,
        {
          id: "4",
          name: `Video Rendering Failed: ${
            error instanceof Error ? error.message : String(error)
          }`,
          status: "failed",
        },
      ]);
    } finally {
      setIsRenderingVideo(false);
    }
  };

  const generateManimCodes = async (scripts: ScriptItem[]) => {
    const manimTasks: Task[] = scripts.map((script) => ({
      id: `manim-${script.title}`,
      name: `Generating: ${script.title}`,
      status: "pending",
    }));

    setTasks((prevTasks) => [
      ...prevTasks.filter((t) => t.id !== "3"),
      ...manimTasks,
      { id: "3", name: "Generating Animations", status: "in-progress" },
    ]);

    const scriptsWithCode: ScriptItem[] = [];

    for (const scriptItem of scripts) {
      setCurrentGeneratingScene(scriptItem.title);
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === `manim-${scriptItem.title}`
            ? { ...task, status: "in-progress" }
            : task
        )
      );

      await generateManimCodeWithFetch(
        scriptItem.title,
        scriptItem.description
      );

      const updatedScript = currentScripts.find(
        (s) => s.title === scriptItem.title && s.code
      );
      if (updatedScript) {
        scriptsWithCode.push(updatedScript);
      }
    }
    setCurrentGeneratingScene(null);

    setTasks((prevTasks) =>
      prevTasks.map((task) =>
        task.id === "3" ? { ...task, status: "completed" } : task
      )
    );

    if (scriptsWithCode.length > 0) {
      setTimeout(async () => {
        const finalScripts = currentScripts.filter((script) => script.code);
        if (finalScripts.length > 0) {
          await renderVideoWithBackend(finalScripts);
        }
      }, 500);
    }
  };

  const handleGenerateClick = async (prompt: string) => {
    setIsGenerating(true);
    setCurrentScripts([]);
    setManimStreams({});
    setScriptHistory((prev) => [...prev]);
    setCurrentGeneratingScene(null);

    const initialTasks: Task[] = [
      { id: "1", name: "Analyzing Input", status: "in-progress" },
      { id: "2", name: "Generating Script", status: "pending" },
      { id: "3", name: "Generating Animations", status: "pending" },
    ];
    setTasks(initialTasks);

    try {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === "2" ? { ...task, status: "in-progress" } : task
        )
      );
      await generateScriptWithFetch(prompt);
    } catch (error) {
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.status === "in-progress"
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
      setCurrentGeneratingScene(null);
    }
  };

  return (
    <AnimatePresence>
      <InputSection
        isGenerating={isGenerating}
        onGenerate={handleGenerateClick}
      />

      {isGenerating && (
        <div className="flex h-screen w-full bg-background text-foreground overflow-hidden">
          <TaskProgressSidebar
            isGenerating={isGenerating}
            tasks={tasks}
            scriptHistory={scriptHistory}
            currentGeneratingScene={currentGeneratingScene}
          />

          <ContentDisplayPanel
            isGenerating={isGenerating}
            currentScripts={currentScripts}
            manimStreams={manimStreams}
            onClose={() => setIsGenerating(false)}
          />
        </div>
      )}

      <VideoConfirmationDialog
        isOpen={showConfirmationDialog}
        onClose={() => setShowConfirmationDialog(false)}
        videoJobId={videoJobId}
      />
    </AnimatePresence>
  );
}
