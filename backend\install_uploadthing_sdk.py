#!/usr/bin/env python3
"""
Installation script for UploadThing Python SDK.

This script helps install the official UploadThing Python SDK and verifies the installation.
If the SDK installation fails, it provides guidance on using the manual API fallback.

Usage:
    python install_uploadthing_sdk.py
"""

import subprocess
import sys
import os
from datetime import datetime


def run_command(command, description):
    """Run a command and return success status."""
    try:
        print(f"📦 {description}...")
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=60
        )
        
        if result.returncode == 0:
            print(f"✅ {description} successful")
            return True
        else:
            print(f"❌ {description} failed")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ {description} failed with error: {e}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✅ Python version is compatible")
        return True
    else:
        print("❌ Python 3.8+ is required for UploadThing SDK")
        return False


def install_uploadthing_sdk():
    """Install the UploadThing Python SDK."""
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install uploadthing.py", "Installing UploadThing Python SDK"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True


def verify_installation():
    """Verify that the SDK was installed correctly."""
    try:
        print("🔍 Verifying UploadThing SDK installation...")
        
        # Try to import the SDK
        from uploadthing_py import UTApi
        print("✅ UploadThing SDK imported successfully")
        
        # Check if we can create an instance (without API key)
        try:
            # This might fail due to missing API key, but import should work
            print("✅ UploadThing SDK is ready to use")
            return True
        except Exception as e:
            print(f"⚠️  SDK imported but initialization failed: {e}")
            print("This is normal if UPLOADTHING_SECRET is not configured")
            return True
            
    except ImportError as e:
        print(f"❌ Failed to import UploadThing SDK: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during verification: {e}")
        return False


def check_environment():
    """Check environment configuration."""
    print("\n🔧 Checking environment configuration...")
    
    uploadthing_secret = os.getenv("UPLOADTHING_SECRET")
    if uploadthing_secret and uploadthing_secret != "your_uploadthing_secret_key":
        print("✅ UPLOADTHING_SECRET is configured")
    else:
        print("⚠️  UPLOADTHING_SECRET not configured")
        print("   Set this in your .env file for the SDK to work")
    
    # Check if .env file exists
    if os.path.exists(".env"):
        print("✅ .env file found")
    else:
        print("⚠️  .env file not found")
        print("   Copy .env.example to .env and configure your values")


def test_service_integration():
    """Test the UploadThing service integration."""
    try:
        print("\n🧪 Testing service integration...")
        
        from services.uploadthing_service import get_uploadthing_service
        service = get_uploadthing_service()
        
        print("✅ UploadThing service created successfully")
        
        # Check if SDK is being used
        if hasattr(service, 'utapi') and service.utapi is not None:
            print("✅ Service is using the official UploadThing SDK")
        else:
            print("⚠️  Service is using manual API implementation")
            print("   This is normal if the SDK is not installed or configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Service integration test failed: {e}")
        return False


def main():
    """Main installation and verification process."""
    print("🚀 UploadThing Python SDK Installation")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check Python version
    if not check_python_version():
        print("\n❌ Installation aborted due to incompatible Python version")
        return False
    
    print()
    
    # Install SDK
    if install_uploadthing_sdk():
        print("\n✅ UploadThing SDK installation completed")
    else:
        print("\n❌ UploadThing SDK installation failed")
        print("\n📝 Fallback Information:")
        print("The UploadThing service will automatically use manual API implementation")
        print("when the SDK is not available. Your integration will still work.")
        return False
    
    print()
    
    # Verify installation
    if verify_installation():
        print("✅ UploadThing SDK verification passed")
    else:
        print("❌ UploadThing SDK verification failed")
        return False
    
    # Check environment
    check_environment()
    
    # Test service integration
    if test_service_integration():
        print("✅ Service integration test passed")
    else:
        print("❌ Service integration test failed")
    
    print("\n" + "=" * 60)
    print("🎉 UploadThing SDK Setup Complete!")
    print()
    print("Next steps:")
    print("1. Configure UPLOADTHING_SECRET in your .env file")
    print("2. Run the test suite: python test_uploadthing_api.py")
    print("3. Start using the enhanced UploadThing integration!")
    print("=" * 60)
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Installation failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
