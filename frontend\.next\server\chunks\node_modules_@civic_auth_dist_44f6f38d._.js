module.exports = {

"[project]/node_modules/@civic/auth/dist/lib/logger.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createLogger": (()=>createLogger),
    "loggers": (()=>loggers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$debug$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/debug/src/index.js [app-route] (ecmascript)");
;
const PACKAGE_NAME = "@civic/auth";
class DebugLogger {
    debugLogger;
    infoLogger;
    warnLogger;
    errorLogger;
    constructor(namespace){
        // Format: @org/package:library:component:level
        this.debugLogger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$debug$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(`${PACKAGE_NAME}:${namespace}:debug`);
        this.infoLogger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$debug$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(`${PACKAGE_NAME}:${namespace}:info`);
        this.warnLogger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$debug$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(`${PACKAGE_NAME}:${namespace}:warn`);
        this.errorLogger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$debug$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(`${PACKAGE_NAME}:${namespace}:error`);
        this.debugLogger.color = "4";
        this.infoLogger.color = "2";
        this.warnLogger.color = "3";
        this.errorLogger.color = "1";
    }
    debug(message, ...args) {
        this.debugLogger(message, ...args);
    }
    info(message, ...args) {
        this.infoLogger(message, ...args);
    }
    warn(message, ...args) {
        this.warnLogger(message, ...args);
    }
    error(message, ...args) {
        this.errorLogger(message, ...args);
    }
}
const createLogger = (namespace)=>new DebugLogger(namespace);
const loggers = {
    // Next.js specific loggers
    nextjs: {
        routes: createLogger("api:routes"),
        middleware: createLogger("api:middleware"),
        handlers: {
            auth: createLogger("api:handlers:auth")
        }
    },
    // React specific loggers
    react: {
        components: createLogger("react:components"),
        hooks: createLogger("react:hooks"),
        context: createLogger("react:context")
    },
    // Shared utilities loggers
    services: {
        validation: createLogger("utils:validation"),
        network: createLogger("utils:network")
    }
}; //# sourceMappingURL=logger.js.map
}),
"[project]/node_modules/@civic/auth/dist/types.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AuthStatus": (()=>AuthStatus),
    "tokenKeys": (()=>tokenKeys)
});
var AuthStatus;
(function(AuthStatus) {
    AuthStatus["AUTHENTICATED"] = "authenticated";
    AuthStatus["UNAUTHENTICATED"] = "unauthenticated";
    AuthStatus["AUTHENTICATING"] = "authenticating";
    AuthStatus["ERROR"] = "error";
    AuthStatus["SIGNING_OUT"] = "signing_out";
})(AuthStatus || (AuthStatus = {}));
const tokenKeys = [
    "sub",
    "idToken",
    "accessToken",
    "refreshToken",
    "forwardedTokens"
];
;
 //# sourceMappingURL=types.js.map
}),
"[project]/node_modules/@civic/auth/dist/utils.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "convertToAuthStatus": (()=>convertToAuthStatus),
    "isPopupBlocked": (()=>isPopupBlocked),
    "withoutUndefined": (()=>withoutUndefined)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/types.js [app-route] (ecmascript)");
;
const isPopupBlocked = ()=>{
    // First we try to open a small popup window. It either returns a window object or null.
    const popup = window.open("", "", "width=1,height=1");
    // If window.open() returns null, popup is definitely blocked
    if (!popup) {
        return true;
    }
    try {
        // Try to access a property of the popup to check if it's usable
        if (typeof popup.closed === "undefined") {
            throw new Error("Popup is blocked");
        }
    } catch  {
        // Accessing the popup's properties throws an error if the popup is blocked
        return true;
    }
    // Close the popup immediately if it was opened
    popup.close();
    return false;
};
const withoutUndefined = (obj)=>{
    const result = {};
    for(const key in obj){
        if (obj[key] !== undefined) {
            // TypeScript needs assurance that key is a valid key in WithoutUndefined<T>
            // We use type assertion here
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            result[key] = obj[key];
        }
    }
    return result;
};
const convertToAuthStatus = (status)=>{
    switch(status){
        case "authenticated":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthStatus"].AUTHENTICATED;
        case "unauthenticated":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthStatus"].UNAUTHENTICATED;
        case "authenticating":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthStatus"].AUTHENTICATING;
        case "error":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthStatus"].ERROR;
        case "signing_out":
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthStatus"].SIGNING_OUT;
        default:
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthStatus"].UNAUTHENTICATED;
    }
}; //# sourceMappingURL=utils.js.map
}),
"[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AUTH_SERVER_LEGACY_SESSION": (()=>AUTH_SERVER_LEGACY_SESSION),
    "AUTH_SERVER_SESSION": (()=>AUTH_SERVER_SESSION),
    "CodeVerifier": (()=>CodeVerifier),
    "OAuthTokenTypes": (()=>OAuthTokenTypes),
    "UserStorage": (()=>UserStorage)
});
var OAuthTokenTypes;
(function(OAuthTokenTypes) {
    OAuthTokenTypes["ID_TOKEN"] = "id_token";
    OAuthTokenTypes["ACCESS_TOKEN"] = "access_token";
    OAuthTokenTypes["REFRESH_TOKEN"] = "refresh_token";
    OAuthTokenTypes["OIDC_SESSION_EXPIRES_AT"] = "oidc_session_expires_at";
})(OAuthTokenTypes || (OAuthTokenTypes = {}));
const AUTH_SERVER_SESSION = "_session";
const AUTH_SERVER_LEGACY_SESSION = "_session.legacy";
var CodeVerifier;
(function(CodeVerifier) {
    CodeVerifier["COOKIE_NAME"] = "code_verifier";
    CodeVerifier["APP_URL"] = "app_url";
})(CodeVerifier || (CodeVerifier = {}));
var UserStorage;
(function(UserStorage) {
    UserStorage["USER"] = "user";
})(UserStorage || (UserStorage = {})); //# sourceMappingURL=types.js.map
}),
"[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AUTOREFRESH_TIMEOUT_NAME": (()=>AUTOREFRESH_TIMEOUT_NAME),
    "DARK_BACKGROUND_COLOR": (()=>DARK_BACKGROUND_COLOR),
    "DEFAULT_AUTH_SERVER": (()=>DEFAULT_AUTH_SERVER),
    "DEFAULT_DISPLAY_MODE": (()=>DEFAULT_DISPLAY_MODE),
    "DEFAULT_EXPIRES_IN": (()=>DEFAULT_EXPIRES_IN),
    "DEFAULT_OAUTH_GET_PARAMS": (()=>DEFAULT_OAUTH_GET_PARAMS),
    "DEFAULT_SCOPES": (()=>DEFAULT_SCOPES),
    "JWT_PAYLOAD_KNOWN_CLAIM_KEYS": (()=>JWT_PAYLOAD_KNOWN_CLAIM_KEYS),
    "LIGHT_BACKGROUND_COLOR": (()=>LIGHT_BACKGROUND_COLOR),
    "LOGOUT_STATE": (()=>LOGOUT_STATE),
    "LOGOUT_SUCCESS_TEXT": (()=>LOGOUT_SUCCESS_TEXT),
    "REFRESH_IN_PROGRESS": (()=>REFRESH_IN_PROGRESS),
    "TOKEN_EXCHANGE_SUCCESS_TEXT": (()=>TOKEN_EXCHANGE_SUCCESS_TEXT),
    "TOKEN_EXCHANGE_TRIGGER_TEXT": (()=>TOKEN_EXCHANGE_TRIGGER_TEXT)
});
const DEFAULT_SCOPES = [
    "openid",
    "profile",
    "email",
    "forwardedTokens",
    "offline_access"
];
const DEFAULT_AUTH_SERVER = "https://auth.civic.com/oauth";
const DEFAULT_OAUTH_GET_PARAMS = [
    "code",
    "state",
    "iss"
];
const DEFAULT_EXPIRES_IN = 3600; // 1 hour in seconds
// The server's callback handler renders this text if it needs the front-end to make an additional token exchange call,
// for the iframe case where cookies are not sent along with the initial redirect.
const TOKEN_EXCHANGE_TRIGGER_TEXT = "sameDomainCodeExchangeRequired";
const TOKEN_EXCHANGE_SUCCESS_TEXT = "serverSideTokenExchangeSuccess";
const LOGOUT_SUCCESS_TEXT = "serverSideLogoutSuccess";
const DEFAULT_DISPLAY_MODE = "iframe";
const JWT_PAYLOAD_KNOWN_CLAIM_KEYS = [
    "iss",
    "aud",
    "sub",
    "iat",
    "exp"
];
const AUTOREFRESH_TIMEOUT_NAME = "civicAuthAutorefreshTimeout";
const REFRESH_IN_PROGRESS = "civicAuthRefreshInProgress";
const LOGOUT_STATE = "logout_state";
const DARK_BACKGROUND_COLOR = "rgb(30, 41, 59)";
const LIGHT_BACKGROUND_COLOR = "white";
;
 //# sourceMappingURL=constants.js.map
}),
"[project]/node_modules/@civic/auth/dist/nextjs/utils.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "resolveCallbackUrl": (()=>resolveCallbackUrl),
    "sanitizeBasePath": (()=>sanitizeBasePath)
});
const resolveCallbackUrl = (config, baseUrl)=>{
    const callbackUrl = new URL(config?.callbackUrl, baseUrl).toString();
    return callbackUrl.toString();
};
function sanitizeBasePath(path) {
    if (!path || path === "/") return "";
    // Ensure it starts with a slash
    const withLeadingSlash = path.startsWith("/") ? path : `/${path}`;
    // Remove all trailing slashes (not just one)
    return withLeadingSlash.replace(/\/+$/, "");
} //# sourceMappingURL=utils.js.map
}),
"[project]/node_modules/@civic/auth/dist/nextjs/config.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createCivicAuthPlugin": (()=>createCivicAuthPlugin),
    "defaultAuthConfig": (()=>defaultAuthConfig),
    "resolveAuthConfig": (()=>resolveAuthConfig)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/logger.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ts$2d$deepmerge$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ts-deepmerge/esm/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/utils.js [app-route] (ecmascript)");
;
;
;
;
;
;
const logger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["loggers"].nextjs.handlers.auth;
const defaultServerSecure = !(("TURBOPACK compile-time value", "development") === "development");
const defaultCookiesMaxAge = 60 * 60; // 1 hour
const defaultAuthConfig = {
    oauthServer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_AUTH_SERVER"],
    callbackUrl: "/api/auth/callback",
    loginSuccessUrl: undefined,
    challengeUrl: "/api/auth/challenge",
    refreshUrl: "/api/auth/refresh",
    logoutUrl: "/api/auth/logout",
    logoutCallbackUrl: "/api/auth/logoutcallback",
    loginUrl: "/",
    include: [
        "/**"
    ],
    exclude: [
        "/api/auth/**"
    ],
    basePath: "",
    cookies: {
        tokens: {
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ID_TOKEN]: {
                secure: defaultServerSecure,
                httpOnly: true,
                sameSite: "strict",
                path: "/",
                maxAge: defaultCookiesMaxAge
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ACCESS_TOKEN]: {
                secure: defaultServerSecure,
                httpOnly: true,
                sameSite: "strict",
                path: "/",
                maxAge: defaultCookiesMaxAge
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].REFRESH_TOKEN]: {
                secure: defaultServerSecure,
                httpOnly: true,
                sameSite: "strict",
                path: "/",
                maxAge: defaultCookiesMaxAge
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].OIDC_SESSION_EXPIRES_AT]: {
                secure: defaultServerSecure,
                httpOnly: false,
                sameSite: "strict",
                path: "/",
                maxAge: defaultCookiesMaxAge
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].COOKIE_NAME]: {
                secure: defaultServerSecure,
                httpOnly: true,
                sameSite: "strict",
                path: "/",
                maxAge: defaultCookiesMaxAge
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].APP_URL]: {
                secure: defaultServerSecure,
                httpOnly: true,
                sameSite: "strict",
                path: "/",
                maxAge: defaultCookiesMaxAge
            }
        },
        user: {
            secure: defaultServerSecure,
            httpOnly: false,
            sameSite: "strict",
            path: "/",
            maxAge: defaultCookiesMaxAge
        }
    }
};
const resolveAuthConfig = (config = {})=>{
    // Read configuration that was set by the plugin via environment variables
    const configFromEnv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withoutUndefined"])({
        clientId: ("TURBOPACK compile-time value", "1510a0ab-4bdc-4229-868b-8066cfb811d1"),
        oauthServer: ("TURBOPACK compile-time value", "https://auth.civic.com/oauth"),
        callbackUrl: ("TURBOPACK compile-time value", "/api/auth/callback"),
        loginSuccessUrl: ("TURBOPACK compile-time value", "http://localhost:3000/dashboard"),
        challengeUrl: ("TURBOPACK compile-time value", "/api/auth/challenge"),
        loginUrl: ("TURBOPACK compile-time value", "/register"),
        logoutUrl: ("TURBOPACK compile-time value", "/api/auth/logout"),
        logoutCallbackUrl: ("TURBOPACK compile-time value", "/api/auth/logoutcallback"),
        refreshUrl: ("TURBOPACK compile-time value", "/api/auth/refresh"),
        include: ("TURBOPACK compile-time value", "/**")?.split(","),
        exclude: ("TURBOPACK compile-time value", "/api/auth/**")?.split(","),
        cookies: ("TURBOPACK compile-time truthy", 1) ? JSON.parse(("TURBOPACK compile-time value", '{"tokens":{"id_token":{"secure":false,"httpOnly":true,"sameSite":"strict","path":"/","maxAge":3600},"access_token":{"secure":false,"httpOnly":true,"sameSite":"strict","path":"/","maxAge":3600},"refresh_token":{"secure":false,"httpOnly":true,"sameSite":"strict","path":"/","maxAge":3600},"oidc_session_expires_at":{"secure":false,"httpOnly":false,"sameSite":"strict","path":"/","maxAge":3600},"code_verifier":{"secure":false,"httpOnly":true,"sameSite":"strict","path":"/","maxAge":3600},"app_url":{"secure":false,"httpOnly":true,"sameSite":"strict","path":"/","maxAge":3600}},"user":{"secure":false,"httpOnly":false,"sameSite":"strict","path":"/","maxAge":3600}}')) : "TURBOPACK unreachable",
        basePath: ("TURBOPACK compile-time value", "") || ""
    });
    // Ensure "/api/auth/**" is always excluded
    const finalExclude = new Set([
        ...defaultAuthConfig.exclude,
        ...configFromEnv.exclude || [],
        ...config.exclude ?? []
    ]);
    // Perform a deep merge of the configurations
    const mergedConfig = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ts$2d$deepmerge$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["merge"].withOptions({
        mergeArrays: false
    }, defaultAuthConfig, configFromEnv, config);
    // Override the exclude list with the ensured list
    mergedConfig.exclude = Array.from(finalExclude);
    if (mergedConfig.clientId === undefined) {
        throw new Error("Civic Auth client ID is required");
    }
    return mergedConfig;
};
const createCivicAuthPlugin = (authConfig)=>{
    return (nextConfig)=>{
        logger.debug("createCivicAuthPlugin nextConfig", JSON.stringify(nextConfig, null, 2));
        // Extract basePath from Next.js config
        const basePath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeBasePath"])(nextConfig?.basePath || "");
        // Create a copy of default URLs with basePath added
        const defaultUrlsWithBasePath = {};
        // Only apply to URLs that aren't explicitly set in authConfig
        if (basePath) {
            if (!authConfig.callbackUrl) defaultUrlsWithBasePath.callbackUrl = `${basePath}/api/auth/callback`;
            if (!authConfig.challengeUrl) defaultUrlsWithBasePath.challengeUrl = `${basePath}/api/auth/challenge`;
            if (!authConfig.refreshUrl) defaultUrlsWithBasePath.refreshUrl = `${basePath}/api/auth/refresh`;
            if (!authConfig.logoutUrl) defaultUrlsWithBasePath.logoutUrl = `${basePath}/api/auth/logout`;
            if (!authConfig.logoutCallbackUrl) defaultUrlsWithBasePath.logoutCallbackUrl = `${basePath}/api/auth/logoutcallback`;
            if (!authConfig.loginUrl && authConfig.loginUrl !== "") defaultUrlsWithBasePath.loginUrl = basePath;
        }
        // Create final config with basePath and possibly modified URLs
        const resolvedConfig = resolveAuthConfig({
            ...defaultUrlsWithBasePath,
            ...authConfig,
            basePath
        });
        return {
            ...nextConfig,
            env: {
                ...nextConfig?.env,
                // Internal environment variables - do not set these manually
                _civic_auth_client_id: resolvedConfig.clientId,
                _civic_oauth_server: resolvedConfig.oauthServer,
                _civic_auth_callback_url: resolvedConfig.callbackUrl,
                _civic_auth_login_success_url: resolvedConfig.loginSuccessUrl,
                _civic_auth_challenge_url: resolvedConfig.challengeUrl,
                _civic_auth_login_url: resolvedConfig.loginUrl,
                _civic_auth_logout_url: resolvedConfig.logoutUrl,
                _civic_auth_logout_callback_url: resolvedConfig.logoutCallbackUrl,
                _civic_auth_refresh_url: resolvedConfig.refreshUrl,
                _civic_auth_includes: resolvedConfig.include.join(","),
                _civic_auth_excludes: resolvedConfig.exclude.join(","),
                _civic_auth_cookie_config: JSON.stringify(resolvedConfig.cookies),
                _civic_auth_base_path: resolvedConfig.basePath
            }
        };
    };
}; //# sourceMappingURL=config.js.map
}),
"[project]/node_modules/@civic/auth/dist/shared/lib/storage.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CookieStorage": (()=>CookieStorage),
    "DEFAULT_COOKIE_DURATION": (()=>DEFAULT_COOKIE_DURATION)
});
const DEFAULT_COOKIE_DURATION = 60 * 15; // 15 minutes
class CookieStorage {
    settings;
    constructor(settings = {}){
        this.settings = {
            httpOnly: settings.httpOnly ?? true,
            secure: settings.secure ?? true,
            // the callback request comes the auth server
            // 'lax' ensures the code_verifier cookie is sent with the request
            sameSite: settings.sameSite ?? "lax",
            expires: settings.expires ?? new Date(Date.now() + 1000 * DEFAULT_COOKIE_DURATION),
            path: settings.path ?? "/"
        };
    }
} //# sourceMappingURL=storage.js.map
}),
"[project]/node_modules/@civic/auth/dist/lib/oauth.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "displayModeFromState": (()=>displayModeFromState),
    "generateState": (()=>generateState),
    "getIssuerVariations": (()=>getIssuerVariations),
    "getOauthEndpoints": (()=>getOauthEndpoints),
    "loginSuccessUrlFromState": (()=>loginSuccessUrlFromState),
    "serverTokenExchangeFromState": (()=>serverTokenExchangeFromState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-node/v4.js [app-route] (ecmascript) <export default as v4>");
;
const getIssuerVariations = (issuer)=>{
    const issuerWithoutSlash = issuer.endsWith("/") ? issuer.slice(0, issuer.length - 1) : issuer;
    const issuerWithSlash = `${issuerWithoutSlash}/`;
    return [
        issuerWithoutSlash,
        issuerWithSlash
    ];
};
const addSlashIfNeeded = (url)=>url.endsWith("/") ? url : `${url}/`;
const cache = {};
const getOauthEndpoints = async (oauthServer)=>{
    if (cache[oauthServer]) {
        return cache[oauthServer];
    }
    const openIdConfigResponse = await fetch(`${addSlashIfNeeded(oauthServer)}.well-known/openid-configuration`);
    const openIdConfig = await openIdConfigResponse.json();
    const endpoints = {
        jwks: openIdConfig.jwks_uri,
        auth: openIdConfig.authorization_endpoint,
        token: openIdConfig.token_endpoint,
        userinfo: openIdConfig.userinfo_endpoint,
        endsession: openIdConfig.end_session_endpoint
    };
    cache[oauthServer] = endpoints;
    return endpoints;
};
const generateState = ({ displayMode, serverTokenExchange, loginSuccessUrl })=>{
    const jsonString = JSON.stringify({
        uuid: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
        displayMode,
        ...serverTokenExchange ? {
            serverTokenExchange
        } : {},
        ...loginSuccessUrl ? {
            loginSuccessUrl
        } : {}
    });
    return btoa(jsonString);
};
/**
 * parses the state string from the OAuth2 flow, decoding the display mode too
 * @param state
 * @param sessionDisplayMode
 * @returns { uuid: string, displayMode: DisplayMode }
 */ const displayModeFromState = (state, sessionDisplayMode)=>{
    try {
        const jsonString = atob(state);
        return JSON.parse(jsonString).displayMode;
    } catch (e) {
        console.error("Failed to parse displayMode from state:", state, e);
        return sessionDisplayMode;
    }
};
const decodeState = (state)=>{
    try {
        const jsonString = atob(state);
        return JSON.parse(jsonString);
    } catch  {
        console.error("Failed to parse state string to json:", state);
        return undefined;
    }
};
const serverTokenExchangeFromState = (state)=>decodeState(state)?.serverTokenExchange;
const loginSuccessUrlFromState = (state)=>state ? decodeState(state)?.loginSuccessUrl : undefined;
;
 //# sourceMappingURL=oauth.js.map
}),
"[project]/node_modules/@civic/auth/dist/lib/jwt.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "convertForwardedTokenFormat": (()=>convertForwardedTokenFormat)
});
const convertForwardedTokenFormat = (inputTokens)=>Object.fromEntries(Object.entries(inputTokens).map(([source, tokens])=>[
            source,
            {
                idToken: tokens?.id_token,
                accessToken: tokens?.access_token,
                refreshToken: tokens?.refresh_token
            }
        ])); //# sourceMappingURL=jwt.js.map
}),
"[project]/node_modules/@civic/auth/dist/shared/lib/UserSession.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GenericUserSession": (()=>GenericUserSession)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/jwt.js [app-route] (ecmascript)");
;
;
class GenericUserSession {
    storage;
    constructor(storage){
        this.storage = storage;
    }
    async get() {
        const user = await this.storage.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserStorage"].USER);
        return user ? JSON.parse(user) : null;
    }
    async set(user) {
        const forwardedTokens = user?.forwardedTokens ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["convertForwardedTokenFormat"])(user?.forwardedTokens) : null;
        const value = user ? JSON.stringify({
            ...user,
            forwardedTokens
        }) : "";
        await this.storage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserStorage"].USER, value);
    }
    async clear() {
        await this.storage.delete(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserStorage"].USER);
    }
} //# sourceMappingURL=UserSession.js.map
}),
"[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "buildOauth2Client": (()=>buildOauth2Client),
    "clearAuthServerSession": (()=>clearAuthServerSession),
    "clearTokens": (()=>clearTokens),
    "clearUser": (()=>clearUser),
    "deriveCodeChallenge": (()=>deriveCodeChallenge),
    "exchangeTokens": (()=>exchangeTokens),
    "generateOauthLoginUrl": (()=>generateOauthLoginUrl),
    "generateOauthLogoutUrl": (()=>generateOauthLogoutUrl),
    "getCookiesMaxAge": (()=>getCookiesMaxAge),
    "getEndpointsWithOverrides": (()=>getEndpointsWithOverrides),
    "retrieveOidcSessionExpiredAt": (()=>retrieveOidcSessionExpiredAt),
    "retrieveTokens": (()=>retrieveTokens),
    "setOidcSessionExpiresAt": (()=>setOidcSessionExpiresAt),
    "storeServerTokens": (()=>storeServerTokens),
    "storeTokens": (()=>storeTokens),
    "validateOauth2Tokens": (()=>validateOauth2Tokens)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/oslo/oauth2 [external] (oslo/oauth2, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/oauth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$jwks$2f$remote$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/node/esm/jwks/remote.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$jwt$2f$verify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/node/esm/jwt/verify.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$UserSession$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/UserSession.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$util$2f$decode_jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/node/esm/util/decode_jwt.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/logger.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
const logger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["loggers"].services.validation;
async function deriveCodeChallenge(codeVerifier, method = "S256") {
    if (method === "Plain") {
        console.warn("Using insecure plain code challenge method");
        return codeVerifier;
    }
    const encoder = new TextEncoder();
    const data = encoder.encode(codeVerifier);
    const digest = await crypto.subtle.digest("SHA-256", data);
    return btoa(String.fromCharCode(...new Uint8Array(digest))).replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "");
}
async function getEndpointsWithOverrides(oauthServer, endpointOverrides = {}) {
    const endpoints = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getOauthEndpoints"])(oauthServer);
    return {
        ...endpoints,
        ...endpointOverrides
    };
}
async function generateOauthLoginUrl(config) {
    const endpoints = await getEndpointsWithOverrides(config.oauthServer, config.endpointOverrides);
    const oauth2Client = buildOauth2Client(config.clientId, config.redirectUrl, endpoints);
    const oAuthUrl = await oauth2Client.createAuthorizationURL({
        state: config.state,
        scopes: config.scopes
    });
    // Only add PKCE parameters if a pkceConsumer is provided
    if (config.pkceConsumer) {
        const challenge = await config.pkceConsumer.getCodeChallenge();
        // The OAuth2 client supports PKCE, but does not allow passing in a code challenge from some other source
        // It only allows passing in a code verifier which it then hashes itself.
        oAuthUrl.searchParams.append("code_challenge", challenge);
        oAuthUrl.searchParams.append("code_challenge_method", "S256");
    }
    if (config.nonce) {
        // nonce isn't supported by oslo, so we add it manually
        oAuthUrl.searchParams.append("nonce", config.nonce);
    }
    // Required by the auth server for offline_access scope
    oAuthUrl.searchParams.append("prompt", "consent");
    return oAuthUrl;
}
async function generateOauthLogoutUrl(config) {
    const endpoints = await getEndpointsWithOverrides(config.oauthServer, config.endpointOverrides);
    const endSessionUrl = new URL(endpoints.endsession);
    endSessionUrl.searchParams.append("client_id", config.clientId);
    endSessionUrl.searchParams.append("id_token_hint", config.idToken);
    endSessionUrl.searchParams.append("state", config.state);
    endSessionUrl.searchParams.append("post_logout_redirect_uri", config.redirectUrl);
    return endSessionUrl;
}
function buildOauth2Client(clientId, redirectUri, endpoints) {
    return new __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__["OAuth2Client"](clientId, endpoints.auth, endpoints.token, {
        redirectURI: redirectUri
    });
}
async function exchangeTokens(code, state, pkceProducer, oauth2Client, oauthServer, endpoints, clientSecret) {
    // Get code verifier if using PKCE
    const codeVerifier = pkceProducer ? await pkceProducer.getCodeVerifier() : null;
    // Ensure at least one authentication method is provided
    if (!codeVerifier && !clientSecret) {
        throw new Error("Either PKCE code verifier or client secret must be provided");
    }
    // Build options for validateAuthorizationCode
    const validationOptions = {};
    if (codeVerifier) {
        validationOptions.codeVerifier = codeVerifier;
    }
    if (clientSecret) {
        validationOptions.credentials = clientSecret;
        validationOptions.authenticateWith = "request_body"; // Use client_secret_post method
    }
    const tokens = await oauth2Client.validateAuthorizationCode(code, validationOptions);
    // Validate relevant tokens
    try {
        await validateOauth2Tokens(tokens, endpoints.jwks, oauth2Client, oauthServer);
    } catch (error) {
        console.error("tokenExchange error", {
            error,
            tokens
        });
        throw new Error(`OIDC tokens validation failed: ${error.message}`);
    }
    return tokens;
}
const getCookiesMaxAge = (tokens)=>{
    const DEFAULT_TTL = 60 * 60; // 1 hour default
    let idTokenMaxAge = DEFAULT_TTL;
    let accessTokenMaxAge = DEFAULT_TTL;
    // The ID token takes priority, as it represents the OIDC session
    if (tokens.id_token) {
        // If no access token exists, try to get expiration from ID token
        const parsedIdToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$util$2f$decode_jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["decodeJwt"])(tokens.id_token);
        if (parsedIdToken?.exp) {
            const now = Math.floor(Date.now() / 1000);
            idTokenMaxAge = parsedIdToken.exp - now;
        }
    }
    if (tokens.access_token) {
        // Get access token TTL from the token if it exists
        const parsedAccessToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$util$2f$decode_jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["decodeJwt"])(tokens.access_token);
        accessTokenMaxAge = Number(parsedAccessToken?.accessTokenTTL) || DEFAULT_TTL;
        // If access token has exp claim, use that directly
        if (parsedAccessToken?.exp) {
            const now = Math.floor(Date.now() / 1000);
            accessTokenMaxAge = parsedAccessToken.exp - now;
        }
    }
    return {
        accessTokenMaxAge,
        idTokenMaxAge
    };
};
async function setOidcSessionExpiresAt(storage, tokens) {
    const now = Math.floor(Date.now() / 1000);
    const { idTokenMaxAge } = getCookiesMaxAge(tokens);
    // The OIDC session expiry is linked to the ID token expiry, since this is primarily an OIDC client.
    await storage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].OIDC_SESSION_EXPIRES_AT, (idTokenMaxAge + now).toString());
}
async function storeTokens(storage, tokens) {
    // ID token is the primary token and must always be stored
    await storage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ID_TOKEN, tokens.id_token);
    // Only store access token if it exists (no longer required)
    if (tokens.access_token) {
        await storage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ACCESS_TOKEN, tokens.access_token);
    }
    // Store refresh token if it exists
    if (tokens.refresh_token) {
        await storage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].REFRESH_TOKEN, tokens.refresh_token);
    }
    // Still set access token expiration even if no access token
    // (will get expiration from ID token in this case)
    await setOidcSessionExpiresAt(storage, tokens);
}
async function storeServerTokens(storage, tokens) {
    // Get maxAge values based on token TTLs (refresh token TTL will be undefined)
    const now = Math.floor(Date.now() / 1000);
    const { idTokenMaxAge, accessTokenMaxAge } = getCookiesMaxAge(tokens);
    // The OIDC session expiry is linked to the ID token expiry, since this is primarily an OIDC client.
    const oidcSessionExpiresAt = now + idTokenMaxAge;
    const cookieStorage = storage;
    // Apply maxAge to cookie options
    const accessTokenCookieOptions = {
        maxAge: accessTokenMaxAge
    };
    const refreshTokenCookieOptions = {
        maxAge: undefined
    };
    // ID token is always stored (primary authentication token)
    await cookieStorage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ID_TOKEN, tokens.id_token, {
        maxAge: idTokenMaxAge
    });
    // Access token is optional - only set if it exists
    if (tokens.access_token) {
        await cookieStorage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ACCESS_TOKEN, tokens.access_token, accessTokenCookieOptions);
    }
    // Set refresh token if present as a session cookie (no expiry)
    if (tokens.refresh_token) {
        await cookieStorage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].REFRESH_TOKEN, tokens.refresh_token, refreshTokenCookieOptions);
    }
    // Still store the access token expiration timestamp even if no access token
    await storage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].OIDC_SESSION_EXPIRES_AT, oidcSessionExpiresAt.toString(), {
        // This is primarily an OIDC client, so we use the ID token max age for the session timeout / refresh scheduling.
        maxAge: idTokenMaxAge
    });
    logger.debug("storeServerTokens", {
        oidcSessionExpiresAt,
        refreshTokenMaxAge: "undefined (session cookie)",
        idTokenMaxAge,
        hasAccessToken: !!tokens.access_token
    });
}
async function clearTokens(storage) {
    // clear all local storage keys related to OAuth and CivicAuth SDK
    const clearOAuthPromises = [
        ...Object.values(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"]),
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["REFRESH_IN_PROGRESS"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AUTOREFRESH_TIMEOUT_NAME"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LOGOUT_STATE"]
    ].map(async (key)=>{
        await storage.delete(key);
    });
    await Promise.all([
        ...clearOAuthPromises
    ]);
}
async function clearAuthServerSession(storage) {
    await storage.delete(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AUTH_SERVER_SESSION"]);
    await storage.delete(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AUTH_SERVER_LEGACY_SESSION"]);
}
async function clearUser(storage) {
    const userSession = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$UserSession$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericUserSession"](storage);
    await userSession.clear();
}
async function retrieveTokens(storage) {
    const idToken = await storage.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ID_TOKEN);
    const accessToken = await storage.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ACCESS_TOKEN);
    const refreshToken = await storage.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].REFRESH_TOKEN);
    const oidcSessionExpiresAt = await storage.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].OIDC_SESSION_EXPIRES_AT);
    return {
        id_token: idToken ?? undefined,
        access_token: accessToken ?? undefined,
        refresh_token: refreshToken ?? undefined,
        oidc_session_expires_at: oidcSessionExpiresAt !== null ? parseInt(oidcSessionExpiresAt, 10) : undefined
    };
}
async function retrieveOidcSessionExpiredAt(storage) {
    const value = await storage.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].OIDC_SESSION_EXPIRES_AT);
    if (!value) {
        return null;
    }
    const expiresAt = Number(value);
    return isNaN(expiresAt) ? null : expiresAt;
}
// Single JWKS instance that persists for the lifetime of the SDK session
let cachedJWKS = null;
let cachedJwksUrl = null;
async function validateOauth2Tokens(tokens, jwksEndpoint, oauth2Client, issuer) {
    // Only create a new JWKS instance if one doesn't exist yet
    if (!cachedJWKS || cachedJwksUrl !== jwksEndpoint) {
        cachedJWKS = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$jwks$2f$remote$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createRemoteJWKSet"])(new URL(jwksEndpoint));
        cachedJwksUrl = jwksEndpoint;
    }
    // Validate the ID token - this is now the primary token for authentication
    const idTokenResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$jwt$2f$verify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jwtVerify"])(tokens.id_token, cachedJWKS, {
        issuer: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getIssuerVariations"])(issuer),
        audience: oauth2Client.clientId
    });
    // Only validate the access token if it exists, but if present it must be valid
    let accessTokenPayload;
    if (tokens.access_token) {
        const accessTokenResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$jwt$2f$verify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jwtVerify"])(tokens.access_token, cachedJWKS, {
            issuer: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getIssuerVariations"])(issuer)
        });
        accessTokenPayload = accessTokenResponse.payload;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withoutUndefined"])({
        id_token: idTokenResponse.payload,
        access_token: accessTokenPayload,
        ...tokens?.refresh_token ? {
            refresh_token: tokens.refresh_token
        } : {}
    });
} //# sourceMappingURL=util.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/shared/lib/session.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "clearAuthCookies": (()=>clearAuthCookies),
    "getTokens": (()=>getTokens),
    "getUser": (()=>getUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$util$2f$decode_jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/node/esm/util/decode_jwt.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
// Function to omit keys from an object
const omitKeys = (keys, obj)=>{
    const result = {
        ...obj
    };
    keys.forEach((key)=>{
        delete result[key];
    });
    return result;
};
const parseJWTToType = (jwt)=>{
    const parseResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$node$2f$esm$2f$util$2f$decode_jwt$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["decodeJwt"])(jwt);
    return parseResult;
};
async function getUser(storage) {
    const tokens = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retrieveTokens"])(storage);
    if (!tokens || !tokens.id_token) return null;
    const parsedToken = parseJWTToType(tokens.id_token);
    // it might be preferable to throw here
    if (!parsedToken.sub) return null;
    // set the user ID from the token sub
    const userWithAdditionalTokenFields = {
        ...parsedToken,
        id: parsedToken.sub
    };
    // Assumes all information is in the ID token
    // remove the token keys from the user object to stop it getting too large
    return omitKeys([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["JWT_PAYLOAD_KNOWN_CLAIM_KEYS"],
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["tokenKeys"]
    ], userWithAdditionalTokenFields);
}
async function getTokens(storage) {
    const storageData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retrieveTokens"])(storage);
    if (!storageData) return null;
    return {
        idToken: storageData.id_token,
        accessToken: storageData.access_token,
        refreshToken: storageData.refresh_token
    };
}
const clearAuthCookies = async (storage)=>{
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearTokens"])(storage);
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearUser"])(storage);
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthServerSession"])(storage);
}; //# sourceMappingURL=session.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/nextjs/cookies.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "NextjsCookieStorage": (()=>NextjsCookieStorage),
    "clearAuthCookies": (()=>clearAuthCookies)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/storage.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/session.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
/**
 * Clears all authentication cookies on server. Note, this can only be called by the server
 */ const clearAuthCookies = async ()=>{
    const cookieStorage = new NextjsCookieStorage(); // no cookie storage needed to simply clear it
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])(cookieStorage);
};
class NextjsCookieStorage extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CookieStorage"] {
    config;
    constructor(config = {}){
        super({
            secure: true,
            httpOnly: true
        });
        this.config = config;
    }
    async get(key) {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        return cookieStore.get(key)?.value || null;
    }
    async set(key, value, cookieConfigOverride = {}) {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const cookieSettings = this.config?.[key] || {
            ...this.settings
        };
        const useCookieSettings = {
            ...cookieSettings,
            ...cookieConfigOverride
        };
        cookieStore.set(key, value, useCookieSettings);
    }
    async delete(key) {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        cookieStore.delete(key);
    }
}
;
 //# sourceMappingURL=cookies.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/browser/storage.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "LocalStorageAdapter": (()=>LocalStorageAdapter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$eventemitter3$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/eventemitter3/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$eventemitter3$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__EventEmitter$3e$__ = __turbopack_context__.i("[project]/node_modules/eventemitter3/index.js [app-route] (ecmascript) <export default as EventEmitter>");
;
class LocalStorageAdapter {
    static _emitter;
    static get emitter() {
        if (!LocalStorageAdapter._emitter) {
            LocalStorageAdapter._emitter = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$eventemitter3$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__EventEmitter$3e$__["EventEmitter"]();
        }
        return LocalStorageAdapter._emitter;
    }
    async get(key) {
        return Promise.resolve(localStorage.getItem(key) || "");
    }
    async set(key, value) {
        localStorage.setItem(key, value);
    }
    async delete(key) {
        localStorage.removeItem(key);
    }
} //# sourceMappingURL=storage.js.map
}),
"[project]/node_modules/@civic/auth/dist/services/PKCE.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "BrowserPublicClientPKCEProducer": (()=>BrowserPublicClientPKCEProducer),
    "ConfidentialClientPKCEConsumer": (()=>ConfidentialClientPKCEConsumer),
    "GenericPublicClientPKCEProducer": (()=>GenericPublicClientPKCEProducer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/oslo/oauth2 [external] (oslo/oauth2, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/browser/storage.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
class ConfidentialClientPKCEConsumer {
    pkceChallengeEndpoint;
    basePath;
    constructor(pkceChallengeEndpoint, basePath){
        this.pkceChallengeEndpoint = pkceChallengeEndpoint;
        this.basePath = basePath;
    }
    async getCodeChallenge() {
        // Get only the origin from location
        const origin = window.location.origin;
        // Use only the origin plus basePath if provided, no need for pathname anymore
        const appUrl = this.basePath ? `${origin}${this.basePath}` : origin;
        const response = await fetch(`${this.pkceChallengeEndpoint}?appUrl=${encodeURIComponent(appUrl)}`);
        const data = await response.json();
        return data.challenge;
    }
}
class GenericPublicClientPKCEProducer {
    storage;
    constructor(storage){
        this.storage = storage;
    }
    // if there is already a verifier, return it,
    // If not, create a new one and store it
    async getCodeChallenge() {
        let verifier = await this.getCodeVerifier();
        if (!verifier) {
            verifier = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__["generateCodeVerifier"])();
            this.storage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].COOKIE_NAME, verifier);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deriveCodeChallenge"])(verifier);
    }
    // if there is already a verifier, return it,
    async getCodeVerifier() {
        return this.storage.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].COOKIE_NAME);
    }
}
class BrowserPublicClientPKCEProducer extends GenericPublicClientPKCEProducer {
    constructor(){
        super(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"]());
    }
} //# sourceMappingURL=PKCE.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/server/ServerAuthenticationResolver.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "ServerAuthenticationResolver": (()=>ServerAuthenticationResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/services/PKCE.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/oslo/oauth2 [external] (oslo/oauth2, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/logger.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
const logger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["loggers"].services.validation;
class ServerAuthenticationResolver {
    authConfig;
    storage;
    endpointOverrides;
    pkceProducer;
    oauth2client;
    endpoints;
    constructor(authConfig, storage, endpointOverrides){
        this.authConfig = authConfig;
        this.storage = storage;
        this.endpointOverrides = endpointOverrides;
        // Determine if PKCE should be used based on config
        // Default to true
        const usePkce = authConfig.pkce !== false;
        // Only create PKCE producer if we're using PKCE
        this.pkceProducer = usePkce ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericPublicClientPKCEProducer"](storage) : null;
    }
    /**
     * Attempts to refresh tokens if a refresh token is available
     * @param sessionData Current session data
     * @returns Updated session data
     */ async tryRefreshTokens(sessionData) {
        logger.debug("tryRefreshTokens", {
            sessionData
        });
        // If there's a refresh token, attempt to refresh tokens
        if (sessionData?.refreshToken) {
            try {
                // Only attempt refresh if we have necessary components
                if (!this.oauth2client || !this.endpoints?.jwks) {
                    await this.init();
                }
                if (!this.oauth2client || !this.endpoints?.jwks) {
                    throw new Error("Failed to initialize OAuth client for token refresh");
                }
                // Use the oauth2client to refresh the access token
                const refreshOptions = {};
                if (this.authConfig.clientSecret) {
                    refreshOptions.credentials = this.authConfig.clientSecret;
                    refreshOptions.authenticateWith = "request_body";
                }
                const tokenResponseBody = await this.oauth2client.refreshAccessToken(sessionData.refreshToken, refreshOptions);
                if (!tokenResponseBody) {
                    throw new Error("Failed to get token response from refresh");
                }
                // Validate the refreshed tokens
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateOauth2Tokens"])(tokenResponseBody, this.endpoints.jwks, this.oauth2client, this.oauthServer);
                // Store the refreshed tokens
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["storeServerTokens"])(this.storage, tokenResponseBody);
                // Construct a refreshed session with the new tokens
                return {
                    authenticated: true,
                    idToken: tokenResponseBody.id_token,
                    accessToken: tokenResponseBody.access_token,
                    refreshToken: tokenResponseBody.refresh_token,
                    oidcSessionExpiresAt: tokenResponseBody.oidc_session_expires_at
                };
            } catch (error) {
                logger.warn("Failed to refresh tokens", error);
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearTokens"])(this.storage);
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearUser"])(this.storage);
                return {
                    ...sessionData,
                    authenticated: false
                };
            }
        }
        // No refresh token available
        return {
            ...sessionData,
            authenticated: false
        };
    }
    /**
     * returns The session data if the session is valid, otherwise an unauthenticated session
     * @returns {Promise<SessionData>}
     */ async validateExistingSession(autoRefresh = true) {
        // TODO: investigate a more peformant way to validate a server session
        // other than using JWKS and JWT verification which is what validateOauth2Tokens uses
        const sessionData = await this.getSessionData();
        // If we don't have an ID token, try to refresh if we have a refresh token
        // Access token is no longer required for authentication
        if (!sessionData?.idToken) {
            if (autoRefresh) {
                const refreshedSessionData = await this.tryRefreshTokens(sessionData);
                if (refreshedSessionData.authenticated) {
                    return refreshedSessionData;
                }
            }
            return {
                ...sessionData,
                authenticated: false
            };
        }
        // Initialize if needed
        if (!this.endpoints?.jwks || !this.oauth2client) await this.init();
        if (!this.endpoints?.jwks) {
            throw new Error("JWKS endpoint not found");
        }
        try {
            // Validate existing tokens - access token validation happens only if it exists
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateOauth2Tokens"])({
                access_token: sessionData.accessToken,
                id_token: sessionData.idToken,
                refresh_token: sessionData.refreshToken,
                oidc_session_expires_at: sessionData.oidcSessionExpiresAt
            }, this.endpoints.jwks, this.oauth2client, this.oauthServer);
            return sessionData;
        } catch (error) {
            logger.warn("Error validating tokens", {
                error,
                autoRefresh
            });
            if (autoRefresh) {
                // If token validation fails, try to refresh tokens
                const refreshedSessionData = await this.tryRefreshTokens(sessionData);
                if (refreshedSessionData.authenticated) {
                    return refreshedSessionData;
                }
            }
            return {
                ...sessionData,
                authenticated: false
            };
        }
    }
    get oauthServer() {
        return this.authConfig.oauthServer || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_AUTH_SERVER"];
    }
    async init() {
        // resolve oauth config
        this.endpoints = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpointsWithOverrides"])(this.oauthServer, this.endpointOverrides);
        this.oauth2client = new __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__["OAuth2Client"](this.authConfig.clientId, this.endpoints.auth, this.endpoints.token, {
            redirectURI: this.authConfig.redirectUrl
        });
        return this;
    }
    async tokenExchange(code, state) {
        if (!this.oauth2client) await this.init();
        // Check if we're using PKCE and validate code verifier if needed
        if (this.pkceProducer) {
            const codeVerifier = await this.pkceProducer.getCodeVerifier();
            if (!codeVerifier) throw new Error("Code verifier not found in storage");
        }
        // exchange auth code for tokens
        const tokens = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["exchangeTokens"])(code, state, this.pkceProducer, this.oauth2client, this.oauthServer, this.endpoints, this.authConfig.clientSecret);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["storeServerTokens"])(this.storage, tokens);
        // the code verifier should be single-use, so we delete it if using PKCE
        if (this.pkceProducer) {
            await this.storage.delete(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].COOKIE_NAME);
        }
        return tokens;
    }
    async getSessionData() {
        const storageData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retrieveTokens"])(this.storage);
        if (!storageData) return null;
        return {
            authenticated: !!storageData.id_token,
            idToken: storageData.id_token,
            accessToken: storageData.access_token,
            refreshToken: storageData.refresh_token,
            oidcSessionExpiresAt: storageData.oidc_session_expires_at
        };
    }
    async getEndSessionEndpoint() {
        if (!this.endpoints) {
            return null;
        }
        return this.endpoints.endsession;
    }
    static async build(authConfig, storage, endpointOverrides) {
        const resolver = new ServerAuthenticationResolver(authConfig, storage, endpointOverrides);
        await resolver.init();
        return resolver;
    }
} //# sourceMappingURL=ServerAuthenticationResolver.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/shared/version.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// This is an auto-generated file. Do not edit.
__turbopack_context__.s({
    "VERSION": (()=>VERSION)
});
const VERSION = "@civic/auth:0.8.0"; //# sourceMappingURL=version.js.map
}),
"[project]/node_modules/@civic/auth/dist/shared/lib/BrowserCookieStorage.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "BrowserCookieStorage": (()=>BrowserCookieStorage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/storage.js [app-route] (ecmascript)");
;
// Ensure only runs in a browser environment
function documentObj() {
    if (typeof globalThis.window !== "undefined") return globalThis.document;
    const stack = new Error().stack;
    throw new Error("Document is not available in this environment:" + JSON.stringify(stack));
}
const split = (separator)=>(str)=>str.split(separator);
const cookieStringFromSettings = (settings)=>{
    let cookieSettings = "";
    if (settings.path) {
        cookieSettings += `Path=${settings.path}; `;
    }
    if (settings.expires) {
        cookieSettings += `Expires=${settings.expires}; `;
    }
    if (settings.secure) {
        cookieSettings += `Secure; `;
    }
    if (settings.httpOnly) {
        // HttpOnly cannot be set from client-side JavaScript, so this clause can be omitted.
        console.warn("HttpOnly cannot be set on client-side cookies. Ignoring this setting.");
    }
    if (settings.sameSite) {
        cookieSettings += `SameSite=${settings.sameSite}; `;
    }
    return cookieSettings.trim();
};
class BrowserCookieStorage extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CookieStorage"] {
    constructor(config = {}){
        super({
            // sensible browser defaults
            secure: false,
            httpOnly: false,
            ...config
        });
    }
    // Synchronous methods
    getSync(key) {
        const encodedValue = documentObj().cookie.split(";").map(split("=")).find(([cookieKey])=>cookieKey?.trim() === key)?.[1];
        return encodedValue ? decodeURIComponent(encodedValue) : null;
    }
    setSync(key, value, cookieConfigOverride = {}) {
        const encodedValue = encodeURIComponent(value);
        const settings = {
            ...this.settings,
            ...cookieConfigOverride
        };
        const cookieString = cookieStringFromSettings(settings);
        documentObj().cookie = `${key}=${encodedValue}; ${cookieString}`;
    }
    deleteSync(key) {
        documentObj().cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    }
    // Async methods (for API compatibility)
    async get(key) {
        return this.getSync(key);
    }
    async set(key, value, cookieConfigOverride = {}) {
        this.setSync(key, value, cookieConfigOverride);
    }
    async delete(key) {
        this.deleteSync(key);
    }
} //# sourceMappingURL=BrowserCookieStorage.js.map
}),
"[project]/node_modules/@civic/auth/dist/shared/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "getVersion": (()=>getVersion),
    "printVersion": (()=>printVersion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/version.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$BrowserCookieStorage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/BrowserCookieStorage.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)"); //# sourceMappingURL=index.js.map
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
let versionPrinted = false;
const getVersion = ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VERSION"];
const printVersion = ()=>{
    if (!versionPrinted) {
        versionPrinted = true;
        if (getVersion() && "undefined" !== "undefined" && typeof document !== "undefined") //TURBOPACK unreachable
        ;
    }
};
;
;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/shared/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/version.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$BrowserCookieStorage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/BrowserCookieStorage.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/index.js [app-route] (ecmascript) <locals>");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js [app-route] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CivicNextAuthProvider": (()=>CivicNextAuthProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-route] (ecmascript)");
;
const CivicNextAuthProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call CivicNextAuthProvider() from the server but CivicNextAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js <module evaluation>", "CivicNextAuthProvider");
}),
"[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js [app-route] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CivicNextAuthProvider": (()=>CivicNextAuthProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-route] (ecmascript)");
;
const CivicNextAuthProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call CivicNextAuthProvider() from the server but CivicNextAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js", "CivicNextAuthProvider");
}),
"[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$providers$2f$NextAuthProvider$2e$js__$5b$app$2d$route$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js [app-route] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$providers$2f$NextAuthProvider$2e$js__$5b$app$2d$route$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js [app-route] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$providers$2f$NextAuthProvider$2e$js__$5b$app$2d$route$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/node_modules/@civic/auth/dist/nextjs/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "getTokens": (()=>getTokens),
    "getUser": (()=>getUser),
    "isLoggedIn": (()=>isLoggedIn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/config.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/cookies.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/server/ServerAuthenticationResolver.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/session.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$routeHandler$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/routeHandler.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$providers$2f$NextAuthProvider$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js [app-route] (ecmascript)"); //# sourceMappingURL=index.js.map
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$routeHandler$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$routeHandler$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["printVersion"])();
;
;
;
const getAuthResolver = async ()=>{
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])();
    const clientStorage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextjsCookieStorage"]();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ServerAuthenticationResolver"].build({
        ...config,
        redirectUrl: config.callbackUrl
    }, clientStorage);
};
const isLoggedIn = async ()=>{
    const authResolver = await getAuthResolver();
    const existingSession = await authResolver.validateExistingSession(false);
    return existingSession.authenticated;
};
const getUser = async ()=>{
    const hasValidSession = await isLoggedIn();
    if (!hasValidSession) return null;
    const clientStorage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextjsCookieStorage"]();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUser"])(clientStorage);
};
const getTokens = async ()=>{
    const hasValidSession = await isLoggedIn();
    if (!hasValidSession) return null;
    const clientStorage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextjsCookieStorage"]();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getTokens"])(clientStorage);
};
;
;
;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/services/types.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PopupError": (()=>PopupError)
});
class PopupError extends Error {
    constructor(message){
        super(message);
        Object.setPrototypeOf(this, PopupError.prototype);
    }
} //# sourceMappingURL=types.js.map
}),
"[project]/node_modules/@civic/auth/dist/lib/windowUtil.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Detects if the current window is running inside an iframe
 * Uses multiple methods to improve detection reliability
 * If detected as iframe, adds an immediate style blocker to prevent content flashes
 */ __turbopack_context__.s({
    "isWindowInIframe": (()=>isWindowInIframe),
    "removeParamsWithoutReload": (()=>removeParamsWithoutReload)
});
const isWindowInIframe = (window1)=>{
    // Make sure we're in a browser environment
    if (typeof window1 === "undefined") {
        return false;
    }
    // Check if running in Cypress
    const isCypress = Boolean(window1.Cypress || window1.parent?.Cypress || window1.top?.Cypress);
    // If we're in Cypress, don't treat it as an iframe
    if (isCypress) {
        return false;
    }
    let isInIframe = false;
    try {
        // Method 1: Check for frameElement
        // This can throw a cross-origin error, but works reliably for same-origin frames
        if (window1.frameElement !== null) {
            isInIframe = true;
        }
        // Method 2: Check specifically for our civic auth iframe
        // Use explicit type assertion to handle the TypeScript issue
        const frameEl = window1.frameElement;
        if (frameEl && frameEl.id === "civic-auth-iframe") {
            isInIframe = true;
        }
    } catch  {
        // If we get a security/cross-origin error, it's likely because
        // we're in an iframe from a different origin
        // So this is actually a positive signal that we're in an iframe
        isInIframe = true;
    }
    return isInIframe;
};
const removeParamsWithoutReload = (paramsToRemove)=>{
    const url = new URL(window.location.href);
    paramsToRemove.forEach((param)=>{
        url.searchParams.delete(param);
    });
    try {
        window.history.replaceState({}, "", url);
        window.dispatchEvent(new Event("popstate"));
    } catch (error) {
        console.warn("window.history.replaceState failed", error);
    }
};
;
 //# sourceMappingURL=windowUtil.js.map
}),
"[project]/node_modules/@civic/auth/dist/lib/postMessage.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "validateLoginAppPostMessage": (()=>validateLoginAppPostMessage)
});
const validateLoginAppPostMessage = (event, clientId)=>{
    const caseEvent = event;
    if (!caseEvent.clientId || !caseEvent.source || !caseEvent.type || caseEvent.clientId !== clientId || caseEvent.source !== "civicloginApp") {
        return false;
    }
    return true;
};
;
 //# sourceMappingURL=postMessage.js.map
}),
"[project]/node_modules/@civic/auth/dist/shared/lib/iframeUtils.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Our custom iframe resizer doesn't add any special methods to the iframe element
// so we can just return the iframe element directly
__turbopack_context__.s({
    "getIframeRef": (()=>getIframeRef)
});
const getIframeRef = (iframeRef, allowNull = false)=>{
    if (!iframeRef && !allowNull) {
        throw new Error("iframeRef is required for displayMode 'iframe'");
    }
    return iframeRef;
}; //# sourceMappingURL=iframeUtils.js.map
}),
"[project]/node_modules/@civic/auth/dist/shared/lib/GenericAuthenticationRefresher.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "GenericAuthenticationRefresher": (()=>GenericAuthenticationRefresher)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
class GenericAuthenticationRefresher {
    onError;
    authConfig;
    storage;
    constructor(onError){
        this.onError = onError;
    }
    get oauthServer() {
        return this.authConfig?.oauthServer || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_AUTH_SERVER"];
    }
    async getRefreshToken() {
        if (!this.storage) throw new Error("No storage available");
        const tokens = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retrieveTokens"])(this.storage);
        if (!tokens?.refresh_token) throw new Error("No refresh token available");
        return tokens.refresh_token;
    }
    async refreshTokens() {
        try {
            const result = await this.refreshAccessToken();
            return result;
        } catch (error) {
            // Check if this is an AbortError or network-related error
            const errorMessage = error.message || "";
            const isNetworkError = errorMessage.includes("network") || errorMessage.includes("abort") || errorMessage.includes("cancel");
            if (isNetworkError) {
                console.warn("GenericAuthenticationRefresher: Network error during token refresh:", error);
            } else {
                console.error("GenericAuthenticationRefresher: Failed to refresh tokens:", error);
            }
            await this.onError(error);
            throw error;
        }
    }
} //# sourceMappingURL=GenericAuthenticationRefresher.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/shared/lib/AuthenticationRefresherImpl.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "AuthenticationRefresherImpl": (()=>AuthenticationRefresherImpl)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/oslo/oauth2 [external] (oslo/oauth2, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$GenericAuthenticationRefresher$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/GenericAuthenticationRefresher.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$GenericAuthenticationRefresher$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$GenericAuthenticationRefresher$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
class AuthenticationRefresherImpl extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$GenericAuthenticationRefresher$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericAuthenticationRefresher"] {
    endpointOverrides;
    endpoints;
    oauth2client;
    constructor(authConfig, storage, onError, endpointOverrides){
        super(onError);
        this.endpointOverrides = endpointOverrides;
        this.authConfig = authConfig;
        this.storage = storage;
        this.init();
    }
    async init() {
        if (!this.authConfig) throw new Error("No auth config available");
        // resolve oauth config
        this.endpoints = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpointsWithOverrides"])(this.oauthServer, this.endpointOverrides);
        this.oauth2client = new __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__["OAuth2Client"](this.authConfig.clientId, this.endpoints.auth, this.endpoints.token, {
            redirectURI: this.authConfig.redirectUrl
        });
        return this;
    }
    static async build(authConfig, storage, onError, endpointOverrides) {
        const refresher = new AuthenticationRefresherImpl(authConfig, storage, onError, endpointOverrides);
        await refresher.init();
        return refresher;
    }
    async storeTokens(tokenResponseBody) {
        if (!this.storage) throw new Error("No storage available");
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["storeTokens"])(this.storage, tokenResponseBody);
    }
    async refreshAccessToken() {
        if (!this.storage) throw new Error("No storage available");
        try {
            const refreshToken = await this.getRefreshToken();
            if (!this.oauth2client) {
                await this.init();
            }
            if (!this.endpoints?.jwks) {
                throw new Error("No jwks endpoint");
            }
            const oauth2Client = this.oauth2client;
            try {
                const tokenResponseBody = await oauth2Client.refreshAccessToken(refreshToken);
                // Validate the refreshed tokens - ID token is the primary token
                // Access token validation is now optional
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateOauth2Tokens"])(tokenResponseBody, this.endpoints.jwks, oauth2Client, this.oauthServer);
                await this.storeTokens(tokenResponseBody);
                return tokenResponseBody;
            } catch (tokenRequestError) {
                console.error("Error during refresh token network request:", tokenRequestError);
                throw new Error(`Token refresh failed: ${tokenRequestError.message}`);
            }
        } catch (error) {
            console.warn("refreshAccessToken failed");
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearTokens"])(this.storage);
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearUser"])(this.storage);
            throw error;
        }
    }
} //# sourceMappingURL=AuthenticationRefresherImpl.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/shared/lib/BrowserAuthenticationRefresher.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "BrowserAuthenticationRefresher": (()=>BrowserAuthenticationRefresher)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$AuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/AuthenticationRefresherImpl.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$AuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$AuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
class BrowserAuthenticationRefresher extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$AuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthenticationRefresherImpl"] {
    refreshEventCallbacks;
    static async build(authConfig, storage, onError, endpointOverrides, refreshEventCallbacks) {
        const refresher = new BrowserAuthenticationRefresher(authConfig, storage, onError, endpointOverrides);
        refresher.refreshEventCallbacks = refreshEventCallbacks;
        await refresher.init();
        return refresher;
    }
    handleError(error) {
        console.error("BrowserAuthenticationRefresher: Error", error);
        this.clearAutorefresh();
        this.onError(error);
    }
    async handleAutoRefresh() {
        try {
            const existingUser = await this.storage?.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserStorage"].USER);
            if (!existingUser) {
                console.warn("BrowserAuthenticationRefresher: No user found, skipping refresh");
                this.clearAutorefresh();
                return;
            }
            // ensure only one refresh is in progress
            if (localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["REFRESH_IN_PROGRESS"]) !== "true") {
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["REFRESH_IN_PROGRESS"], "true");
                // Call onRefreshStarted callback
                this.refreshEventCallbacks?.onRefreshStarted?.();
                await this.refreshTokens();
                // Call onRefreshComplete callback
                this.refreshEventCallbacks?.onRefreshComplete?.();
                await this.setupAutorefresh(); // Reset the timeout after successful refresh
            }
        } catch (error) {
            console.error("BrowserAuthenticationRefresher: Failed to refresh tokens:", error);
            // Call onRefreshError callback
            this.refreshEventCallbacks?.onRefreshError?.(error);
            // TODO detect if refresh token has expired and if yes then logout
            this.handleError(error);
        }
    }
    async setupAutorefresh() {
        // clear any existing state
        localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["REFRESH_IN_PROGRESS"]);
        if (!this.storage) throw new Error("No storage available");
        // Clear any existing timeout
        this.clearAutorefresh();
        // get expires_in
        const now = Math.floor(Date.now() / 1000);
        const expiresAt = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retrieveOidcSessionExpiredAt"])(this.storage);
        // If there's no expiration time, don't set up auto-refresh
        if (!expiresAt) {
            console.warn("BrowserAuthenticationRefresher: No OIDC_SESSION_EXPIRES_AT found, skipping auto-refresh setup");
            return;
        }
        // Calculate time until expiry (subtract 30 seconds as buffer)
        const bufferTime = 30; // 30 seconds
        const refreshTime = Math.max(0, expiresAt - bufferTime - now); // handle case were token has expired in the past
        const refreshTimeout = setTimeout(()=>{
            this.handleAutoRefresh();
        }, 1000 * refreshTime);
        localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AUTOREFRESH_TIMEOUT_NAME"], refreshTimeout.toString());
    }
    clearAutorefresh() {
        // use local storage to store the timeout id so that if multiple instances
        // of the refresher are created they can all clear the same timeout
        const existingTimeout = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AUTOREFRESH_TIMEOUT_NAME"]);
        if (existingTimeout) {
            clearTimeout(Number(existingTimeout));
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AUTOREFRESH_TIMEOUT_NAME"]);
        }
    }
} //# sourceMappingURL=BrowserAuthenticationRefresher.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/services/AuthenticationService.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// Proposals for revised versions of the SessionService AKA AuthSessionService
__turbopack_context__.s({
    "BrowserAuthenticationInitiator": (()=>BrowserAuthenticationInitiator),
    "BrowserAuthenticationService": (()=>BrowserAuthenticationService),
    "GenericAuthenticationInitiator": (()=>GenericAuthenticationInitiator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/services/PKCE.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/oauth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/oslo/oauth2 [external] (oslo/oauth2, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/browser/storage.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/services/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$windowUtil$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/windowUtil.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$postMessage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/postMessage.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/session.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$UserSession$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/UserSession.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$iframeUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/iframeUtils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-node/v4.js [app-route] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$BrowserAuthenticationRefresher$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/BrowserAuthenticationRefresher.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$BrowserAuthenticationRefresher$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$BrowserAuthenticationRefresher$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const clearStorageAndEmitSignOut = async ()=>{
    const localStorage1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"]();
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearTokens"])(localStorage1);
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearUser"])(localStorage1);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"].emitter.emit("signOut");
};
const defaultSetDesignOptions = (value)=>{
    localStorage.setItem("loginAppDesign", JSON.stringify(value));
};
class BrowserAuthenticationInitiator {
    setDesignOptions;
    postMessageHandler = null;
    config;
    setDisplayMode(displayMode) {
        this.config.displayMode = displayMode;
    }
    get displayMode() {
        return this.config.displayMode;
    }
    get isServerTokenExchange() {
        return this.config.pkceConsumer instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConfidentialClientPKCEConsumer"];
    }
    get state() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateState"])({
            displayMode: this.config.displayMode,
            serverTokenExchange: this.isServerTokenExchange,
            loginSuccessUrl: this.config.loginSuccessUrl
        });
    }
    instanceId;
    constructor(config, setDesignOptions = defaultSetDesignOptions){
        this.setDesignOptions = setDesignOptions;
        this.instanceId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        this.config = config;
        this.postMessageHandler = (event)=>{
            const thisURL = new URL(window.location.href);
            if (event.origin.endsWith("civic.com") || thisURL.hostname === "localhost") {
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$postMessage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateLoginAppPostMessage"])(event.data, this.config.clientId)) {
                    return;
                }
                const loginMessage = event.data;
                if (loginMessage.type === "generatePopupFailed") {
                    this.handleLoginAppPopupFailed(loginMessage.data.url);
                    return;
                }
                if (loginMessage.type === "design") {
                    // TODO handle the design message
                    this.handleLoginAppDesignUpdate(loginMessage.data);
                    return;
                }
            }
        };
        window.addEventListener("message", this.postMessageHandler);
    }
    async handleLoginAppPopupFailed(redirectUrl) {
        console.warn("Login app popup failed open a popup, using redirect mode instead...", redirectUrl);
        window.location.href = redirectUrl;
    }
    async handleLoginAppDesignUpdate(options) {
        this.setDesignOptions(options);
    }
    // Use the config (Client ID, scopes OAuth Server, Endpoints, PKCEConsumer) to generate a new login url
    // and then use the display mode to decide how to send the user there
    async signIn(iframeRef) {
        const url = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateOauthLoginUrl"])({
            ...this.config,
            state: this.state
        });
        if (this.config.displayMode === "iframe") {
            const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$iframeUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getIframeRef"])(iframeRef);
            ref.setAttribute("src", url.toString());
        }
        if (this.config.displayMode === "redirect") {
            window.location.href = url.toString();
        }
        if (this.config.displayMode === "new_tab") {
            try {
                const popupWindow = window.open(url.toString(), "_blank");
                if (!popupWindow) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PopupError"]("Failed to open popup window");
                }
            // TODO handle the 'onclose' event to clean up and reset the authStatus
            } catch (error) {
                console.error("popupWindow", error);
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PopupError"]("window.open has thrown: Failed to open popup window");
            }
        }
        return url;
    }
    async signOut(idToken, iframeRef) {
        let url;
        const localStorage1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"]();
        const state = this.state;
        if (this.isServerTokenExchange) {
            if (!this.config.logoutUrl) {
                throw new Error("logoutUrl is required for server token exchange");
            }
            url = new URL(this.config.logoutUrl, window.location.origin);
            url.searchParams.append("state", state);
        } else {
            if (!idToken) {
                throw new Error("idToken is required for non-server token exchange");
            }
            url = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateOauthLogoutUrl"])({
                ...this.config,
                idToken,
                state,
                redirectUrl: this.config.logoutRedirectUrl
            });
        }
        if (this.config.displayMode === "iframe") {
            // Clear storage before calling server by setting iframe src to the logout url
            await clearStorageAndEmitSignOut();
            await localStorage1.delete(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LOGOUT_STATE"]);
            const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$iframeUtils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getIframeRef"])(iframeRef);
            ref.setAttribute("src", url.toString());
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"].emitter.emit("signOut");
        }
        if (this.config.displayMode === "redirect") {
            // we don't clear any storage here as we're redirecting to the logout url
            // and the server should handle that
            await localStorage1.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LOGOUT_STATE"], state);
            window.location.href = url.toString();
        }
        if (this.config.displayMode === "new_tab") {
            try {
                // Clear storage before calling server by setting iframe src to the logout url
                await clearStorageAndEmitSignOut();
                const popupWindow = window.open(url.toString(), "_blank");
                if (!popupWindow) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PopupError"]("Failed to open popup window");
                }
            } catch (error) {
                console.error("popupWindow", error);
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PopupError"]("window.open has thrown: Failed to open popup window");
            }
        }
        return url;
    }
    cleanup() {
        if (this.postMessageHandler) {
            window.removeEventListener("message", this.postMessageHandler);
        }
    }
}
class GenericAuthenticationInitiator {
    config;
    constructor(config){
        this.config = config;
    }
    // Use the config (Client ID, scopes OAuth Server, Endpoints, PKCEConsumer) to generate a new login url
    // and simply return the url
    async signIn() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateOauthLoginUrl"])(this.config);
    }
    async signOut(idToken) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateOauthLogoutUrl"])({
            ...this.config,
            idToken
        });
    }
}
class BrowserAuthenticationService extends BrowserAuthenticationInitiator {
    pkceProducer;
    oauth2client;
    endpoints;
    // TODO WIP - perhaps we want to keep resolver and initiator separate here
    constructor(config, // Since we are running fully on the client, we produce as well as consume the PKCE challenge
    pkceProducer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BrowserPublicClientPKCEProducer"]()){
        super({
            ...config,
            // Store and retrieve the PKCE challenge in local storage
            pkceConsumer: pkceProducer
        });
        this.pkceProducer = pkceProducer;
    }
    // TODO too much code duplication here between the browser and the server variant.
    // Suggestion for refactor: Standardise the config for AuthenticationResolvers and create a one-shot
    // function for generating an oauth2client from it
    async init() {
        // resolve oauth config
        this.endpoints = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEndpointsWithOverrides"])(this.oauthServer, this.config.endpointOverrides);
        this.oauth2client = new __TURBOPACK__imported__module__$5b$externals$5d2f$oslo$2f$oauth2__$5b$external$5d$__$28$oslo$2f$oauth2$2c$__esm_import$29$__["OAuth2Client"](this.config.clientId, this.endpoints.auth, this.endpoints.token, {
            redirectURI: this.config.redirectUrl
        });
        return this;
    }
    async storeTokensOnLogin(tokens) {
        const clientStorage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"]();
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["storeTokens"])(clientStorage, tokens);
        // delete code verifier as it should be single-use
        await clientStorage.delete(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].COOKIE_NAME);
        const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUser"])(clientStorage);
        if (!user) {
            throw new Error("Failed to get user info");
        }
        const userSession = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$UserSession$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericUserSession"](clientStorage);
        await userSession.set(user);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"].emitter.emit("signIn");
    }
    // Two responsibilities:
    // 1. resolve the auth code to get the tokens (should use library code)
    // 2. store the tokens in local storage
    async tokenExchange(code, state) {
        if (!this.oauth2client) await this.init();
        const codeVerifier = await this.pkceProducer.getCodeVerifier();
        if (!codeVerifier) throw new Error("Code verifier not found in storage");
        // exchange auth code for tokens
        const tokens = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["exchangeTokens"])(code, state, this.pkceProducer, this.oauth2client, this.oauthServer, this.endpoints);
        await this.storeTokensOnLogin(tokens);
        // cleanup the browser window if needed
        const parsedDisplayMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["displayModeFromState"])(state, this.config.displayMode);
        if (parsedDisplayMode === "new_tab") {
            // Close the popup window
            window.addEventListener("beforeunload", ()=>{
                window?.opener?.focus();
            });
            window.close();
        }
        // these are the default oAuth params that get added to the URL in redirect which we want to remove if present
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$windowUtil$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["removeParamsWithoutReload"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_OAUTH_GET_PARAMS"]);
        return tokens;
    }
    // Get the session data from local storage
    async getSessionData() {
        const storageData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["retrieveTokens"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"]());
        if (!storageData) return null;
        return {
            authenticated: !!storageData.id_token,
            idToken: storageData.id_token,
            accessToken: storageData.access_token,
            refreshToken: storageData.refresh_token,
            oidcSessionExpiresAt: storageData.oidc_session_expires_at
        };
    }
    async tryRefreshTokens(sessionData) {
        // If token validation fails but we have a refresh token, attempt to refresh
        if (sessionData?.refreshToken) {
            try {
                const clientStorage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"]();
                // Create a BrowserAuthenticationRefresher to handle token refresh using the build method
                const authConfig = {
                    clientId: this.config.clientId,
                    oauthServer: this.oauthServer,
                    redirectUrl: this.config.redirectUrl
                };
                // Use build method which handles initialization
                const refresher = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$BrowserAuthenticationRefresher$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BrowserAuthenticationRefresher"].build(authConfig, clientStorage, async (error)=>{
                    console.warn("Failed to refresh tokens during validation", error);
                }, this.config.endpointOverrides);
                try {
                    // Perform token refresh (no need to call init explicitly)
                    const tokenResponse = await refresher.refreshAccessToken();
                    // Return a new session with the refreshed tokens
                    const refreshedSession = await this.getSessionData();
                    if (refreshedSession && refreshedSession.authenticated) {
                        await this.storeTokensOnLogin(tokenResponse);
                        return {
                            ...refreshedSession,
                            authenticated: true
                        };
                    } else {
                        throw new Error("Failed to get refreshed session data");
                    }
                } catch (refreshApiError) {
                    console.error("Error during token refresh API call:", refreshApiError);
                    throw refreshApiError; // Re-throw to be caught by outer catch block
                }
            } catch (error) {
                const refreshError = error;
                console.error("Token refresh failed with error:", refreshError);
                // Only delete refresh token if it's invalid, not for network errors
                // which might be temporary
                if (refreshError.message.includes("invalid") || refreshError.message.includes("expired")) {
                    const clientStorage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"]();
                    console.log("Deleting invalid refresh token");
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearTokens"])(clientStorage);
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearUser"])(clientStorage);
                }
                console.warn("Failed to refresh tokens", refreshError);
            }
        }
        return {
            ...sessionData,
            authenticated: false
        };
    }
    async validateExistingSession() {
        try {
            const sessionData = await this.getSessionData();
            if (!sessionData?.idToken) {
                const refreshedSessionData = await this.tryRefreshTokens(sessionData);
                if (refreshedSessionData.authenticated) {
                    return refreshedSessionData;
                }
                const unAuthenticatedSession = {
                    ...sessionData,
                    authenticated: false
                };
                return unAuthenticatedSession;
            }
            if (!this.endpoints?.jwks || !this.oauth2client) await this.init();
            if (!this.endpoints?.jwks) {
                throw new Error("No jwks endpoint");
            }
            // this function will throw if the idToken is invalid
            // Note: Access token is no longer required for authentication
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateOauth2Tokens"])({
                id_token: sessionData.idToken,
                refresh_token: sessionData.refreshToken,
                access_token: sessionData.accessToken,
                oidc_session_expires_at: sessionData.oidcSessionExpiresAt
            }, this.endpoints.jwks, this.oauth2client, this.oauthServer);
            return sessionData;
        } catch (error) {
            console.warn("Failed to validate existing tokens", error);
            const unAuthenticatedSession = {
                authenticated: false
            };
            const storage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$browser$2f$storage$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LocalStorageAdapter"]();
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearTokens"])(storage);
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearUser"])(storage);
            return unAuthenticatedSession;
        }
    }
    get oauthServer() {
        return this.config.oauthServer || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_AUTH_SERVER"];
    }
    async getEndSessionEndpoint() {
        if (!this.endpoints) {
            return null;
        }
        return this.endpoints?.endsession;
    }
    static async build(config) {
        const resolver = new BrowserAuthenticationService(config);
        await resolver.init();
        return resolver;
    }
} //# sourceMappingURL=AuthenticationService.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/server/login.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "buildLoginUrl": (()=>buildLoginUrl),
    "isLoggedIn": (()=>isLoggedIn),
    "resolveOAuthAccessCode": (()=>resolveOAuthAccessCode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$AuthenticationService$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/services/AuthenticationService.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/services/PKCE.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/server/ServerAuthenticationResolver.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$AuthenticationService$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$AuthenticationService$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
async function resolveOAuthAccessCode(code, state, storage, config) {
    const authSessionService = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ServerAuthenticationResolver"].build({
        ...config,
        oauthServer: config.oauthServer ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_AUTH_SERVER"]
    }, storage, config.endpointOverrides);
    return authSessionService.tokenExchange(code, state);
}
async function isLoggedIn(storage) {
    return !!await storage.get("id_token");
}
async function buildLoginUrl(config, storage) {
    // generate a random state if not provided
    const state = config.state ?? Math.random().toString(36).substring(2);
    const scopes = config.scopes ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_SCOPES"];
    // Determine if PKCE should be used based on config
    // Default to true for backward compatibility if not specified
    const usePkce = config.pkce !== false;
    // Only create PKCE producer if we're using PKCE and have storage
    const pkceProducer = usePkce && storage ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericPublicClientPKCEProducer"](storage) : null;
    const authInitiator = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$AuthenticationService$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericAuthenticationInitiator"]({
        ...config,
        state,
        scopes,
        oauthServer: config.oauthServer ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_AUTH_SERVER"],
        // When retrieving the PKCE challenge on the server-side, we produce it and store it in the session
        // For confidential clients not using PKCE, this will be undefined
        pkceConsumer: pkceProducer ?? undefined
    });
    return authInitiator.signIn();
} //# sourceMappingURL=login.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/nextjs/NextServerAuthenticationRefresherImpl.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "NextServerAuthenticationRefresherImpl": (()=>NextServerAuthenticationRefresherImpl)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$AuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/AuthenticationRefresherImpl.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$AuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$AuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
class NextServerAuthenticationRefresherImpl extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$AuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthenticationRefresherImpl"] {
    endpointOverrides;
    storage;
    constructor(authConfig, storage, onError, endpointOverrides){
        super(authConfig, storage, onError, endpointOverrides);
        this.endpointOverrides = endpointOverrides;
        this.storage = storage;
    }
    async storeTokens(tokenResponseBody) {
        if (!this.storage) throw new Error("No storage available");
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["storeServerTokens"])(this.storage, tokenResponseBody);
    }
    static async build(authConfig, storage, onError, endpointOverrides) {
        const refresher = new NextServerAuthenticationRefresherImpl(authConfig, storage, onError, endpointOverrides);
        await refresher.init();
        return refresher;
    }
} //# sourceMappingURL=NextServerAuthenticationRefresherImpl.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/nextjs/routeHandler.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "handleLogout": (()=>handleLogout),
    "handleLogoutCallback": (()=>handleLogoutCallback),
    "handler": (()=>handler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/constants.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/logger.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/lib/oauth.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/config.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/cookies.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$login$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/server/login.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/services/PKCE.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/types.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$UserSession$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/UserSession.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/util.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$NextServerAuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/NextServerAuthenticationRefresherImpl.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$login$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$NextServerAuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$login$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$NextServerAuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const logger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$logger$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["loggers"].nextjs.handlers.auth;
class AuthError extends Error {
    status;
    constructor(message, status = 401){
        super(message);
        this.status = status;
        this.name = "AuthError";
    }
}
const tryUriDecode = (value)=>{
    try {
        return decodeURIComponent(value);
    } catch (e) {
        logger.error("Error decoding URI component:", e);
        return value;
    }
};
const getDecodedQueryParam = (request, paramName)=>{
    const queryParam = request.nextUrl.searchParams.get(paramName);
    if (queryParam) {
        return tryUriDecode(queryParam);
    }
    return null;
};
const getCookieOrQueryParam = (request, cookieName, queryName)=>{
    // First check the cookie as it might have the full path with base directory
    const cookieValue = request.cookies.get(cookieName)?.value;
    if (cookieValue) {
        return tryUriDecode(cookieValue);
    }
    // Fallback to query parameter
    return getDecodedQueryParam(request, queryName);
};
const getAppUrl = (request)=>getCookieOrQueryParam(request, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].APP_URL, "appUrl");
// The loginSuccessUrl can either be decoded from the state parameter, or passed as a cookie or query parameter
const getLoginSuccessUrl = (request, baseUrl)=>{
    const loginSuccessUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["loginSuccessUrlFromState"])(request.nextUrl.searchParams.get("state")) || getDecodedQueryParam(request, "loginSuccessUrl");
    if (!loginSuccessUrl) {
        return null;
    }
    return baseUrl ? new URL(loginSuccessUrl, baseUrl).href : loginSuccessUrl;
};
const getIdToken = async (config)=>{
    const cookieStorage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextjsCookieStorage"](config.cookies?.tokens ?? {});
    return cookieStorage.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OAuthTokenTypes"].ID_TOKEN);
};
/**
 * create a code verifier and challenge for PKCE
 * saving the verifier in a cookie for later use
 * @returns {Promise<NextResponse>}
 */ async function handleChallenge(request, config) {
    const cookieStorage = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextjsCookieStorage"](config.cookies?.tokens ?? {});
    const pkceProducer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$services$2f$PKCE$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericPublicClientPKCEProducer"](cookieStorage);
    const challenge = await pkceProducer.getCodeChallenge();
    const appUrl = request.nextUrl.searchParams.get("appUrl");
    if (appUrl) {
        await cookieStorage.set(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].APP_URL, appUrl);
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        status: "success",
        challenge
    });
}
const getCookieStorageWithUserOverrides = (config)=>{
    const resolvedConfigs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])(config);
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextjsCookieStorage"]({
        ...resolvedConfigs.cookies.tokens,
        user: resolvedConfigs.cookies.user
    });
};
async function performTokenExchangeAndSetCookies(config, code, state, appUrl) {
    const resolvedConfigs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])(config);
    // TODO This is messy, better would be to fix the config.cookies type to always be <name: settings>
    // rather than nesting the tokens-related ones *and* code-verifier inside "tokens"
    // (despite code-verifier not relating directly to tokens)
    const cookieStorage = getCookieStorageWithUserOverrides(config);
    const callbackUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveCallbackUrl"])(resolvedConfigs, appUrl);
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$login$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveOAuthAccessCode"])(code, state, cookieStorage, {
            ...resolvedConfigs,
            redirectUrl: callbackUrl
        });
    } catch (error) {
        logger.error("Token exchange failed:", error);
        throw new AuthError("Failed to authenticate user", 401);
    }
    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUser"])();
    if (!user) {
        throw new AuthError("Failed to get user info", 401);
    }
    const userSession = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$UserSession$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericUserSession"](cookieStorage);
    await userSession.set(user);
}
async function handleRefresh(request, config) {
    const resolvedConfigs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])(config);
    const cookieStorage = getCookieStorageWithUserOverrides(config);
    const userSession = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$UserSession$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GenericUserSession"](cookieStorage);
    try {
        const onError = (error)=>{
            logger.error("handleRefresh: Token refresh failed:", error);
            throw new AuthError("Failed to refresh tokens", 500);
        };
        const refresher = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$NextServerAuthenticationRefresherImpl$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextServerAuthenticationRefresherImpl"].build({
            clientId: resolvedConfigs.clientId,
            oauthServer: resolvedConfigs.oauthServer,
            redirectUrl: resolvedConfigs.callbackUrl,
            refreshUrl: resolvedConfigs.refreshUrl
        }, cookieStorage, onError);
        const tokens = await refresher.refreshAccessToken();
        const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUser"])();
        if (!user) {
            throw new AuthError("Failed to get user info", 401);
        }
        await userSession.set(user);
        const targetUrl = request.nextUrl.searchParams.get("targetUrl");
        if (targetUrl) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(targetUrl);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: "success",
            tokens
        });
    } catch (error) {
        logger.error("handleRefresh: Token refresh failed, clearing tokens:", error);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearTokens"])(cookieStorage);
        await userSession.clear();
        const targetUrl = request.nextUrl.searchParams.get("targetUrl");
        if (targetUrl) {
            logger.warn("redirecting to targetUrl", targetUrl);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(targetUrl);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: "failed"
        });
    }
}
const generateHtmlResponseWithCallback = (request, callbackUrl, loginSuccessUrl)=>{
    // we need to replace the URL with resolved config in case the server is hosted
    // behind a reverse proxy or load balancer
    const requestUrl = new URL(request.url);
    const fetchUrl = `${callbackUrl}?${requestUrl.searchParams.toString()}&sameDomainCallback=true`;
    const loginSuccessSegment = loginSuccessUrl ? `&loginSuccessUrl=${encodeURIComponent(loginSuccessUrl)}` : "";
    const html = `<html lang="en">
  <body>
      <span style="display:none">
          <script>
              window.onload = function () {
                  // Get the complete URL including origin and path
                  // This ensures we capture any base path like /directory
                  const appUrl = window.location.href.substring(
                    0,
                    window.location.href.indexOf("/api/auth")
                  );
                  fetch('${fetchUrl}&appUrl=' + encodeURIComponent(appUrl) + '${loginSuccessSegment}').then((response) => {
                      response.json().then((jsonResponse) => {
                        // For login: Redirect back to the callback route, so Case 2 in handleTokenExchangeComplete will be triggered
                        // For logout: Redirect to the postLogoutRedirectUrl
                        if(jsonResponse.redirectUrl) {
                          window.location.href = jsonResponse.redirectUrl;
                        }
                      });
                  });
              };
          </script>
      </span>
  </body>
</html>
`;
    const response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](html);
    response.headers.set("Content-Type", "text/html; charset=utf-8");
    return response;
};
const handleTokenExchangeComplete = async (params)=>{
    const { request, config, appUrl, loginSuccessUrl, state } = params;
    // Case 1: We are being called via fetch to facilitate access to the cookies. Return success json. The iframe has javascript that will reload this route so Case 2 below will be triggered.
    if (isCalledFromBrowserFetch(request)) {
        logger.debug("CASE 1: sameDomainCallback=true, returning JSON response with redirect URL", {
            appUrl,
            loginSuccessUrl,
            callbackUrl: config.callbackUrl
        });
        const currentUrl = new URL(request.url);
        // When the client-side JS redirects back here, we don't want to hit this branch again because we can't return JSON from a redirect.
        // So we strip off the sameDomainCallback parameter from the URL.
        const newSearchParams = new URLSearchParams(currentUrl.search);
        newSearchParams.delete("sameDomainCallback");
        // We strip off the origin so reverse proxies don't break the redirect.
        const redirectUrl = `${currentUrl.pathname}?${newSearchParams.toString()}${currentUrl.hash}`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: "success",
            // This makes the iframe redirect back to this route, so Case 2 below will be triggered.
            redirectUrl
        });
    }
    // Case 2: We are already authenticated and in iframe mode.
    //    Case 2a: We have a custom loginSuccessUrl, so we have to trigger a top-level redirect to it. We do this by rendering a page with the TOKEN_EXCHANGE_SUCCESS_TEXT, which is then picked up by the iframe container.
    //    Case 2b: We don't have a custom loginSuccessUrl, so we just redirect to the appUrl. If we don't do this, Cypress tests will fail in the 'no custom loginSuccessUrl' case, because in Cypress an iframe redirect is converted to a top-level redirect,
    //      which means the iframe container no longer exists and so can't action the redirect.
    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUser"])();
    if (!!user && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["displayModeFromState"])(state, "iframe") === "iframe") {
        if (loginSuccessUrl) {
            logger.debug("CASE 2a: iframe mode with loginSuccessUrl configured. Returning TOKEN_EXCHANGE_SUCCESS_TEXT to trigger redirect to loginSuccessUrl if any", {
                loginSuccessUrl
            });
            const response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](`<html lang="en"><span style="display:none">${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TOKEN_EXCHANGE_SUCCESS_TEXT"]}</span></html>`);
            response.headers.set("Content-Type", "text/html; charset=utf-8");
            return response;
        } else {
            logger.debug("CASE 2b: iframe mode with no loginSuccessUrl configured. Doing a normal redirect without relying on the iframe container to redirect.");
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(`${appUrl}`);
        }
    }
    // CASE 3: We're not in iframe mode. We can just do a stright http redirect to the final destination, which is either the loginSuccessUrl if specified, or the appUrl.
    logger.debug("CASE 3: non-iframe mode, redirecting to loginSuccessUrl");
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(`${loginSuccessUrl || appUrl}`);
};
async function handleCallback(request, config) {
    const resolvedConfigs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])(config);
    const code = request.nextUrl.searchParams.get("code");
    const state = request.nextUrl.searchParams.get("state");
    if (!code || !state) throw new AuthError("Bad parameters", 400);
    // appUrl is passed from the client to the server in the query string
    // this is necessary because the server does not have access to the client's window.location.origin
    // and can not accurately determine the appUrl (specially if the app is behind a reverse proxy)
    const appUrl = getAppUrl(request);
    // If the integrator has specified a loginSuccessUrl, we'll send the user there after the login completes (including token exchange)
    // We pass in the basePath from config to use as the baseUrl, because we might not have access to the app_url cookie at this point if this was a third-party redirect.
    const loginSuccessUrl = getLoginSuccessUrl(request, appUrl);
    const tokenExchangeCompleteParams = {
        request,
        config,
        appUrl,
        state,
        loginSuccessUrl
    };
    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUser"])();
    if (user) {
        // User already authenticated.
        return handleTokenExchangeComplete(tokenExchangeCompleteParams);
    }
    // User not authenticated yet.
    // If we have a code_verifier cookie and the appUrl, we can do a token exchange.
    // Otherwise, just render an empty page.
    // The initial redirect back from the auth server does not send cookies, because the redirect is from a 3rd-party domain.
    // The client will make an additional call to this route with cookies included, at which point we do the token exchange.
    const codeVerifier = request.cookies.get(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$types$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeVerifier"].COOKIE_NAME);
    if (!codeVerifier || !appUrl) {
        logger.debug("handleCallback no code_verifier found", {
            state,
            serverTokenExchange: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serverTokenExchangeFromState"])(`${state}`)
        });
        let response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](`<html lang="en"><body><span style="display:none">${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TOKEN_EXCHANGE_TRIGGER_TEXT"]}</span></body></html>`);
        // in server-side token exchange mode we need to launch a page that will trigger the token exchange
        // from the same domain, allowing it access to the code_verifier cookie
        // we only need to do this in redirect mode, as the iframe already triggers a client-side token exchange
        // if no code-verifier cookie is found
        if (state && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serverTokenExchangeFromState"])(state)) {
            logger.debug("handleCallback serverTokenExchangeFromState, launching redirect page...", {
                requestUrl: request.url,
                configCallbackUrl: resolvedConfigs.callbackUrl
            });
            // generate a page that will callback to the same domain, allowing access
            // to the code_verifier cookie and passing the appUrl.
            response = generateHtmlResponseWithCallback(request, resolvedConfigs.callbackUrl, loginSuccessUrl || undefined);
        }
        logger.debug(`handleCallback no code_verifier found, returning ${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TOKEN_EXCHANGE_TRIGGER_TEXT"]}`);
        return response;
    }
    await performTokenExchangeAndSetCookies(resolvedConfigs, code, state, appUrl);
    return handleTokenExchangeComplete(tokenExchangeCompleteParams);
}
/**
 * If redirectPath is an absolute path, return it as-is.
 * Otherwise for relative paths, append it to the current domain.
 * @param redirectPath
 * @param currentBasePath
 * @returns
 */ const getAbsoluteRedirectPath = (redirectPath, currentBasePath)=>new URL(redirectPath, currentBasePath).href;
const getPostLogoutRedirectUrl = (request, config)=>{
    const { loginUrl } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])(config);
    // if we have a target URL in the request, it's come from civic middleware
    // and we should use it as the redirect target.
    const targetUrl = request.nextUrl.searchParams.get("targetUrl");
    if (targetUrl) {
        // If a targetUrl is provided, use it as the redirect target.
        // This is useful for redirecting to a specific page after logout.
        return targetUrl;
    }
    const redirectTarget = loginUrl ?? "/";
    // if the optional loginUrl is provided and it is an absolute URL,
    // use it as the redirect target
    const isAbsoluteRedirect = /^(https?:\/\/|www\.).+/i.test(redirectTarget);
    if (isAbsoluteRedirect) {
        return redirectTarget;
    }
    // if loginUrl is not defined, the appUrl is passed from the client to the server
    // in the query string or cookies. This is necessary because the server does not
    // have access to the client's window.location and can not accurately determine
    // the appUrl (specially if the app is behind a reverse proxy).
    const appUrl = getAppUrl(request);
    if (appUrl) return getAbsoluteRedirectPath(redirectTarget, appUrl);
    // If we can't determine the post-logout redirect URL, fallback to the app root as it's the most likely location of the login page.
    return request.nextUrl.origin;
};
const revalidateUrlPath = async (url)=>{
    try {
        const path = new URL(url).pathname;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["revalidatePath"])(path);
    } catch (error) {
        logger.warn("Failed to revalidate path after logout:", error);
    }
};
async function handleLogout(request, config) {
    const resolvedConfigs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])(config);
    // Ensure we have the proper app URL including any base path
    const appBaseUrl = getAppUrl(request) || request.url;
    // Construct the post-logout URL with the base path included
    const postLogoutUrl = new URL(resolvedConfigs.logoutCallbackUrl, appBaseUrl);
    // read the id_token from the cookies
    const idToken = await getIdToken(resolvedConfigs);
    // read the state from the query parameters
    const state = request.nextUrl.searchParams.get("state");
    if (!state || !idToken) {
        logger.error("handleLogout: missing state or idToken", {
            state,
            idToken
        });
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
        // if token or state is missing, the logout call to the server will fail,
        // (token has potentially expired already) so go straight to the postLogoutUrl
        //  so the user can be signed out.
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(`${postLogoutUrl}${state ? "?state=" + state : ""}`);
    }
    const displayMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["displayModeFromState"])(state, "iframe");
    if (displayMode === "iframe") {
        // clear auth cookies immediately before calling the logout endpoint to give faster UX
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
        await revalidateUrlPath(request.url);
    }
    const logoutUrl = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$util$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateOauthLogoutUrl"])({
        clientId: resolvedConfigs.clientId,
        idToken,
        state,
        redirectUrl: postLogoutUrl.href,
        oauthServer: resolvedConfigs.oauthServer
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(`${logoutUrl.href}`);
}
const isCalledFromBrowserFetch = (request)=>request.url.includes("sameDomainCallback=true");
const handleLogoutComplete = async (params)=>{
    const { request, resolvedConfigs } = params;
    const state = request.nextUrl.searchParams.get("state");
    const postLogoutRedirectUrl = getPostLogoutRedirectUrl(request, resolvedConfigs);
    // If this is a FETCH call, we can only return json. Trying to redirect or return HTML will fail.
    if (isCalledFromBrowserFetch(request)) {
        logger.debug("handleLogoutComplete: sameDomainCallback=true, returning JSON response with redirect URL", {
            postLogoutRedirectUrl
        });
        // The client-side JS will do a window.location.href redirect to postLogoutRedirectUrl when this request returns success.
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: "success",
            redirectUrl: postLogoutRedirectUrl
        });
    }
    // If this is a redirect inside an iframe and the user is indeed logged out, render some text that makes the parent redirect to the postLogoutRedirectUrl.
    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getUser"])();
    if (!user && !!state && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$lib$2f$oauth$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["displayModeFromState"])(state, "iframe") === "iframe") {
        // User is logged out while in an iframe redirect (not a FETCH call).
        // Render some text to make the CivicLogoutIframeContainer redirect to the postLogoutRedirectUrl.
        const response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](`<html lang="en"><span style="display:none">${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$constants$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["LOGOUT_SUCCESS_TEXT"]}<a href="${[
            postLogoutRedirectUrl
        ]}" rel="civic-auth-post-logout-redirect-url"></a></span></html>`);
        response.headers.set("Content-Type", "text/html; charset=utf-8");
        logger.debug("handleLogoutComplete: iframe mode, rendering HTML with logout success text", {
            postLogoutRedirectUrl
        });
        return response;
    }
    logger.debug("handleLogoutComplete: redirecting to postLogoutRedirectUrl", {
        postLogoutRedirectUrl
    });
    revalidateUrlPath(postLogoutRedirectUrl);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].redirect(postLogoutRedirectUrl);
};
async function handleLogoutCallback(request, config) {
    const resolvedConfigs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])(config);
    const canAccessCookies = !!await getIdToken(resolvedConfigs);
    // If we have access to cookies, clear them.
    if (canAccessCookies) {
        logger.debug("handleLogoutCallback can access cookies: clearing auth cookies");
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
        return handleLogoutComplete({
            request,
            resolvedConfigs
        });
    }
    logger.debug("handleLogoutCallback cannot access cookies: generating HTML response with callback");
    // If we don't have access to cookies, render some javascript to the client that will:
    // 1. make a same-domain fetch call back to this endpoint and receive a '{status: "success"}' back.
    // 2. On status: success, set the window.location.href to the post-logout redirect URL (usually the appUrl).
    return generateHtmlResponseWithCallback(request, // The client-side JS will make a fetch call back to this URL.
    resolvedConfigs.logoutCallbackUrl);
}
const handler = (authConfig = {})=>async (request)=>{
        const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolveAuthConfig"])(authConfig);
        try {
            const pathname = request.nextUrl.pathname;
            const pathSegments = pathname.split("/");
            const lastSegment = pathSegments[pathSegments.length - 1];
            switch(lastSegment){
                case "challenge":
                    return await handleChallenge(request, config);
                case "callback":
                    return await handleCallback(request, config);
                case "refresh":
                    return await handleRefresh(request, config);
                case "logout":
                    return await handleLogout(request, config);
                case "logoutcallback":
                    return await handleLogoutCallback(request, config);
                default:
                    throw new AuthError(`Invalid auth route: ${pathname}`, 404);
            }
        } catch (error) {
            logger.error("Auth handler error:", error);
            const status = error instanceof AuthError ? error.status : 500;
            const message = error instanceof Error ? error.message : "Authentication failed";
            const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: message
            }, {
                status
            });
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearAuthCookies"])();
            return response;
        }
    }; //# sourceMappingURL=routeHandler.js.map
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/node_modules/@civic/auth/dist/nextjs/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$config$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/config.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/cookies.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/server/ServerAuthenticationResolver.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/shared/lib/session.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$routeHandler$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/routeHandler.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$providers$2f$NextAuthProvider$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/providers/NextAuthProvider.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@civic/auth/dist/nextjs/index.js [app-route] (ecmascript) <locals>");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$routeHandler$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$cookies$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$server$2f$ServerAuthenticationResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$shared$2f$lib$2f$session$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$routeHandler$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$civic$2f$auth$2f$dist$2f$nextjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=node_modules_%40civic_auth_dist_44f6f38d._.js.map