"use client";

import { useEffect, useRef } from "react";
import { Bricolage_Grotesque } from "next/font/google";

const brico = Bricolage_Grotesque({ subsets: ["latin"] });
import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, <PERSON> } from "lucide-react";

const vertShader = `
precision mediump float;
varying vec2 vUv;
attribute vec2 a_position;
void main() {
  vUv = .5 * (a_position + 1.);
  gl_Position = vec4(a_position, 0.0, 1.0);
}`;

const fragShader = `
precision mediump float;
varying vec2 vUv;
uniform float u_time;
uniform float u_ratio;
uniform vec2 u_pointer_position;
uniform float u_scroll_progress;

vec2 rotate(vec2 uv, float th) {
  return mat2(cos(th), sin(th), -sin(th), cos(th)) * uv;
}

float neuro_shape(vec2 uv, float t, float p) {
  vec2 sine_acc = vec2(0.);
  vec2 res = vec2(0.);
  float scale = 8.;
  for (int j = 0; j < 15; j++) {
    uv = rotate(uv, 1.);
    sine_acc = rotate(sine_acc, 1.);
    vec2 layer = uv * scale + float(j) + sine_acc - t;
    sine_acc += sin(layer) + 2.4 * p;
    res += (.5 + .5 * cos(layer)) / scale;
    scale *= (1.2);
  }
  return res.x + res.y;
}

void main() {
  vec2 uv = .5 * vUv;
  uv.x *= u_ratio;

  vec2 pointer = vUv - u_pointer_position;
  pointer.x *= u_ratio;
  float p = clamp(length(pointer), 0., 1.);
  p = .5 * pow(1. - p, 2.);

  float t = .001 * u_time;
  vec3 color = vec3(0.);

  float noise = neuro_shape(uv, t, p);

  noise = 1.2 * pow(noise, 3.);
  noise += pow(noise, 10.);
  noise = max(.0, noise - .5);
  noise *= (1. - length(vUv - .5));

  color = vec3(0.5, 0.15, 0.65);
  color += vec3(0.3, 0.0, 0.25) * sin(3.0 * u_scroll_progress + 1.5);

  color = color * noise;

  gl_FragColor = vec4(color, noise);
}`;

const NeuralAI = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const pointer = useRef({ x: 0, y: 0, tX: 0, tY: 0 });
  const uniforms = useRef<any>({});

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const gl = (canvas.getContext("webgl") ||
      canvas.getContext("experimental-webgl")) as WebGLRenderingContext | null;
    if (!gl) {
      console.error("WebGL not supported by your browser.");
      return;
    }

    const devicePixelRatio = Math.min(window.devicePixelRatio, 2);

    function createShader(
      gl: WebGLRenderingContext,
      source: string,
      type: number
    ) {
      const shader = gl.createShader(type)!;
      gl.shaderSource(shader, source);
      gl.compileShader(shader);
      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error("Shader error: " + gl.getShaderInfoLog(shader));
        return null;
      }
      return shader;
    }

    const vertexShader = createShader(gl, vertShader, gl.VERTEX_SHADER)!;
    const fragmentShader = createShader(gl, fragShader, gl.FRAGMENT_SHADER)!;

    const program = gl.createProgram()!;
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error("Shader link error: " + gl.getProgramInfoLog(program));
      return;
    }

    const u: any = {};
    const uniformCount = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);
    for (let i = 0; i < uniformCount; i++) {
      const uniformName = gl.getActiveUniform(program, i)?.name;
      if (uniformName) {
        u[uniformName] = gl.getUniformLocation(program, uniformName);
      }
    }
    uniforms.current = u;

    const vertices = new Float32Array([-1, -1, 1, -1, -1, 1, 1, 1]);
    const vertexBuffer = gl.createBuffer()!;
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

    gl.useProgram(program);
    const positionLocation = gl.getAttribLocation(program, "a_position");
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

    function resizeCanvas() {
      if (!canvas || !gl) return;
      canvas.width = window.innerWidth * devicePixelRatio;
      canvas.height = window.innerHeight * devicePixelRatio;
      gl.viewport(0, 0, canvas.width, canvas.height);
      gl.uniform1f(uniforms.current.u_ratio, canvas.width / canvas.height);
    }

    function updateMouse(x: number, y: number) {
      pointer.current.tX = x;
      pointer.current.tY = y;
    }

    function render() {
      if (!gl) return;
      const time = performance.now();
      const { x, y, tX, tY } = pointer.current;
      pointer.current.x += (tX - x) * 0.2;
      pointer.current.y += (tY - y) * 0.2;

      gl.uniform1f(uniforms.current.u_time, time);
      gl.uniform2f(
        uniforms.current.u_pointer_position,
        pointer.current.x / window.innerWidth,
        1 - pointer.current.y / window.innerHeight
      );
      gl.uniform1f(
        uniforms.current.u_scroll_progress,
        window.scrollY / (2 * window.innerHeight)
      );
      gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
      requestAnimationFrame(render);
    }

    window.addEventListener("pointermove", (e) =>
      updateMouse(e.clientX, e.clientY)
    );
    window.addEventListener("touchmove", (e) =>
      updateMouse(e.touches[0].clientX, e.touches[0].clientY)
    );
    window.addEventListener("click", (e) => updateMouse(e.clientX, e.clientY));
    window.addEventListener("resize", resizeCanvas);

    resizeCanvas();
    render();
  }, []);

  return (
    <section className="relative z-10 min-h-screen overflow-hidden flex items-center pt-36 pb-20 px-4 md:px-8">
      <canvas
        ref={canvasRef}
        className="absolute z-10 inset-0 w-full h-full opacity-10 dark:opacity-30 pointer-events-none"
      />
      <div className="max-w-7xl mx-auto w-full grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <div className="inline-block px-4 py-2 rounded-full dark:bg-white/5 bg-white/80 border dark:border-white/10 border-white/50 text-muted-foreground text-sm mb-6">
            <span className="flex items-center">
              <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
              Introducing Neural AI Platform 2.0
            </span>
          </div>

          <h1
            className={cn(
              "text-4xl md:text-7xl mb-6 font-bold tracking-tighter",
              brico.className
            )}
          >
            Learn Smarter,
            <br/>
            Not Harder
          </h1>

          <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl">
            Our AI turns any topic into a short animated explainer, visual
            notes, and smart Q&As — all personalized to your syllabus and level.
            No clutter, just clear learning in seconds.
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <a
              href="#"
              className="py-4 px-8 rounded-xl bg-white text-gray-900 font-medium text-center transition-all hover:bg-white/90"
            >
              Start Building Free
            </a>
            <a
              href="#"
              className="py-4 px-8 rounded-xl bg-white/10 hover:bg-white/15 text-white font-medium text-center border border-white/10 transition-all"
            >
              Watch Demo
            </a>
          </div>

          <div className="mt-10 flex items-center gap-2">
            <div className="flex -space-x-2">
              <img
                src="https://randomuser.me/api/portraits/women/44.jpg"
                className="w-10 h-10 rounded-full border-2 border-gray-900"
                alt="User"
              />
              <img
                src="https://randomuser.me/api/portraits/men/86.jpg"
                className="w-10 h-10 rounded-full border-2 border-gray-900"
                alt="User"
              />
              <img
                src="https://randomuser.me/api/portraits/women/63.jpg"
                className="w-10 h-10 rounded-full border-2 border-gray-900"
                alt="User"
              />
            </div>
            <div className="text-muted-foreground text-sm">
              Trusted by{" "}
              <span className="text-foreground font-medium">1,000+</span>{" "}
              educators and students worldwide
            </div>
          </div>
        </div>

        <div className="relative h-[500px] flex items-center justify-center">
          <div className="z-10 backdrop-blur bg-gray-400/5 shadow-2xl dark:bg-white/5 border border-border/50 rounded-2xl w-80 h-80 absolute transform rotate-6 floating-delay">
            <div className="absolute top-6 left-6 right-6 bottom-6 flex flex-col">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 rounded-lg bg-black/10 dark:bg-white/10 flex items-center justify-center">
                  <Brain className="w-5 h-5" />
                </div>
                <span className="ml-3 text-lg">DBMS concepts</span>
              </div>
              <div className="w-full relative max-w-xs">
                <div className="w-full h-48 rounded-xl gradient-border inner-glow overflow-hidden relative">
                  <div className="absolute inset-0 opacity-10">
                    <div
                      className="w-full h-full animate-pulse"
                      style={{
                        backgroundImage:
                          "linear-gradient(90deg, rgba(255,255,255,0.3) 1px, transparent 1px), linear-gradient(rgba(255,255,255,0.3) 1px, transparent 1px)",
                        backgroundSize: "15px 15px",
                        // Light mode by default
                      }}
                    ></div>
                  </div>

                  <svg
                    className="absolute inset-0 w-full h-full pointer-events-none"
                    viewBox="0 0 320 180"
                  >
                    <defs>
                      <linearGradient
                        id="connectionGradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="0%"
                      >
                        <stop
                          offset="0%"
                          style={{
                            stopColor: "#4f46e5",
                            stopOpacity: 0.8,
                          }}
                        />
                        <stop
                          offset="50%"
                          style={{ stopColor: "#3b82f6", stopOpacity: 1 }}
                        />
                        <stop
                          offset="100%"
                          style={{
                            stopColor: "#8b5cf6",
                            stopOpacity: 0.8,
                          }}
                        />
                      </linearGradient>
                    </defs>

                    <g
                      stroke="url(#connectionGradient)"
                      strokeWidth="1.5"
                      fill="none"
                    >
                      <path
                        className="connector"
                        d="M80,60 L140,60 L140,90 L200,90"
                      />
                      <path
                        className="connector"
                        d="M200,90 L240,90 L240,60 L280,60"
                      />
                      <path
                        className="connector"
                        d="M140,90 L140,120 L200,120"
                      />
                      <path
                        className="connector"
                        d="M200,120 L240,120 L240,150 L200,150"
                      />

                      <circle cx="80" cy="60" r="3" fill="#4f46e5" />
                      <circle cx="200" cy="90" r="3" fill="#3b82f6" />
                      <circle cx="280" cy="60" r="3" fill="#8b5cf6" />
                      <circle cx="200" cy="120" r="3" fill="#f59e0b" />
                      <circle cx="200" cy="150" r="3" fill="#ef4444" />
                    </g>
                  </svg>

                  <div className="absolute inset-0 w-full h-full">
                    <div className="absolute top-3 left-1/2 transform -translate-x-1/2 animate-schema-pulse">
                      <div className="w-8 h-8 glass rounded-xl flex items-center justify-center border border-indigo-400/30 inner-glow">
                        <svg
                          className="w-4 h-4 text-indigo-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                        </svg>
                      </div>
                    </div>

                    <div className="absolute left-3 top-12 table-float">
                      <div className="w-16 h-12 glass rounded-lg gradient-border shadow-lg overflow-hidden">
                        <div className="bg-gradient-to-r from-indigo-500/20 to-blue-500/20 text-[7px] px-1.5 py-0.5 font-medium border-b border-white/10">
                          users
                        </div>
                        <div className="px-1.5 py-0.5 space-y-0.5">
                          <div className="flex items-center space-x-0.5">
                            <div className="w-1 h-1 bg-yellow-400 rounded-full"></div>
                            <div className="h-0.5 w-6 bg-white/30 rounded"></div>
                          </div>
                          <div className="h-0.5 w-4 bg-white/20 rounded"></div>
                          <div className="h-0.5 w-7 bg-white/20 rounded"></div>
                        </div>
                      </div>
                    </div>

                    <div className="absolute right-3 top-12 table-float">
                      <div className="w-16 h-12 glass rounded-lg gradient-border shadow-lg overflow-hidden">
                        <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white text-[7px] px-1.5 py-0.5 font-medium border-b border-white/10">
                          orders
                        </div>
                        <div className="px-1.5 py-0.5 space-y-0.5">
                          <div className="flex items-center space-x-0.5">
                            <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                            <div className="h-0.5 w-6 bg-white/30 rounded"></div>
                          </div>
                          <div className="h-0.5 w-3 bg-white/20 rounded"></div>
                          <div className="h-0.5 w-5 bg-white/20 rounded"></div>
                        </div>
                      </div>
                    </div>

                    <div className="absolute left-1/2 transform -translate-x-1/2 top-24 table-float">
                      <div className="w-16 h-12 glass rounded-lg gradient-border shadow-lg overflow-hidden">
                        <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white text-[7px] px-1.5 py-0.5 font-medium border-b border-white/10">
                          products
                        </div>
                        <div className="px-1.5 py-0.5 space-y-0.5">
                          <div className="flex items-center space-x-0.5">
                            <div className="w-1 h-1 bg-purple-400 rounded-full"></div>
                            <div className="h-0.5 w-6 bg-white/30 rounded"></div>
                          </div>
                          <div className="h-0.5 w-6 bg-white/20 rounded"></div>
                          <div className="h-0.5 w-4 bg-white/20 rounded"></div>
                        </div>
                      </div>
                    </div>

                    <div className="absolute left-1/2 transform -translate-x-1/2 bottom-3 table-float">
                      <div className="w-16 h-12 glass rounded-lg gradient-border shadow-lg overflow-hidden">
                        <div className="bg-gradient-to-r from-orange-500/20 to-red-500/20 text-white text-[7px] px-1.5 py-0.5 font-medium border-b border-white/10">
                          analytics
                        </div>
                        <div className="px-1.5 py-0.5 space-y-0.5">
                          <div className="flex items-center space-x-0.5">
                            <div className="w-1 h-1 bg-orange-400 rounded-full"></div>
                            <div className="h-0.5 w-6 bg-white/30 rounded"></div>
                          </div>
                          <div className="h-0.5 w-3 bg-white/20 rounded"></div>
                          <div className="h-0.5 w-5 bg-white/20 rounded"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="backdrop-blur-xl bg-gray-400/5 shadow-2xl dark:bg-white/5 border border-border/50 rounded-2xl w-64 h-fit absolute -bottom-4 -left-4 transform -rotate-12 floating">
            <div className="p-4">
              <div className="flex justify-between items-center mb-3">
                <span className="text-white text-sm">
                  Simple Harmonic Motion
                </span>
                <Bot className="w-5 h-5 text-white/70" />
              </div>
              <div className="flex-1 flex items-center justify-center">
                <svg
                  viewBox="0 0 256 192"
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-full h-full"
                >
                  <defs>
                    <linearGradient
                      id="waveGradient"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="0%"
                    >
                      <stop offset="0%" style={{ stopColor: "#3b82f6" }} />
                      <stop offset="50%" style={{ stopColor: "#8b5cf6" }} />
                      <stop offset="100%" style={{ stopColor: "#ef4444" }} />
                    </linearGradient>
                    <linearGradient
                      id="pendulumGradient"
                      x1="0%"
                      y1="0%"
                      x2="0%"
                      y2="100%"
                    >
                      <stop offset="0%" style={{ stopColor: "#6b7280" }} />
                      <stop offset="100%" style={{ stopColor: "#374151" }} />
                    </linearGradient>
                    <radialGradient id="bobGradient" cx="30%" cy="30%" r="70%">
                      <stop offset="0%" style={{ stopColor: "#fbbf24" }} />
                      <stop offset="70%" style={{ stopColor: "#f59e0b" }} />
                      <stop offset="100%" style={{ stopColor: "#d97706" }} />
                    </radialGradient>
                    <radialGradient
                      id="pivotGradient"
                      cx="50%"
                      cy="50%"
                      r="50%"
                    >
                      <stop offset="0%" style={{ stopColor: "#e5e7eb" }} />
                      <stop offset="100%" style={{ stopColor: "#6b7280" }} />
                    </radialGradient>
                  </defs>

                  {/* Background */}
                  <rect width="256" height="192" fill="transparent" />

                  {/* Title animation */}
                  <text
                    x="128"
                    y="15"
                    fontFamily="Arial, sans-serif"
                    fontSize="12"
                    fontWeight="bold"
                    fill="currentColor"
                    textAnchor="middle"
                    className="fill-gray-800 dark:fill-white"
                  >
                    x = A cos(ωt + φ)
                    <animate
                      attributeName="opacity"
                      values="0;1;1;1"
                      dur="8s"
                      repeatCount="indefinite"
                    />
                    <animateTransform
                      attributeName="transform"
                      type="scale"
                      values="0.5;1;1;1"
                      dur="8s"
                      repeatCount="indefinite"
                    />
                  </text>

                  {/* Pendulum Section */}
                  <g transform="translate(60, 50)">
                    {/* Pendulum pivot point */}
                    <circle
                      cx="0"
                      cy="0"
                      r="3"
                      fill="url(#pivotGradient)"
                      opacity="0"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;1;1"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                    </circle>

                    {/* Pendulum string */}
                    <line
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="50"
                      stroke="url(#pendulumGradient)"
                      strokeWidth="1.5"
                      opacity="0"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;1;1"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                      <animateTransform
                        attributeName="transform"
                        type="rotate"
                        values="30;-30;30;-30;30"
                        dur="4s"
                        repeatCount="indefinite"
                        begin="2s"
                      />
                    </line>

                    {/* Pendulum bob */}
                    <circle r="4" fill="url(#bobGradient)" opacity="0">
                      <animate
                        attributeName="opacity"
                        values="0;0;1;1"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                      <animateTransform
                        attributeName="transform"
                        type="rotate"
                        values="30;-30;30;-30;30"
                        dur="4s"
                        repeatCount="indefinite"
                        begin="2s"
                      />
                      <animate
                        attributeName="cx"
                        values="0;0;0;0;0"
                        dur="4s"
                        repeatCount="indefinite"
                        begin="2s"
                      />
                      <animate
                        attributeName="cy"
                        values="50;50;50;50;50"
                        dur="4s"
                        repeatCount="indefinite"
                        begin="2s"
                      />
                    </circle>

                    {/* Arc showing pendulum motion */}
                    <path
                      d="M -25 43 A 50 50 0 0 1 25 43"
                      stroke="currentColor"
                      strokeWidth="0.8"
                      fill="none"
                      strokeDasharray="3,2"
                      opacity="0"
                      className="stroke-gray-500 dark:stroke-gray-400"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;0;0.5;0.5"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                    </path>

                    {/* Angle indicator */}
                    <path
                      d="M 0 0 L 12 0 A 12 12 0 0 0 10.4 6"
                      stroke="currentColor"
                      strokeWidth="1"
                      fill="none"
                      opacity="0"
                      className="stroke-blue-500"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;0;0.7;0.7"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                    </path>
                    <text
                      x="15"
                      y="8"
                      fontFamily="Arial, sans-serif"
                      fontSize="8"
                      fill="currentColor"
                      opacity="0"
                      className="fill-blue-600 dark:fill-blue-400"
                    >
                      θ
                      <animate
                        attributeName="opacity"
                        values="0;0;0;1;1"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                    </text>
                  </g>

                  {/* Wave Section */}
                  <g transform="translate(10, 120)">
                    {/* Time axis */}
                    <line
                      x1="0"
                      y1="0"
                      x2="200"
                      y2="0"
                      stroke="currentColor"
                      strokeWidth="1"
                      strokeDasharray="200"
                      strokeDashoffset="200"
                      className="stroke-gray-700 dark:stroke-white"
                    >
                      <animate
                        attributeName="stroke-dashoffset"
                        values="200;0;0;0"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                    </line>

                    {/* Amplitude axis */}
                    <line
                      x1="0"
                      y1="-25"
                      x2="0"
                      y2="25"
                      stroke="currentColor"
                      strokeWidth="1"
                      strokeDasharray="50"
                      strokeDashoffset="50"
                      className="stroke-gray-700 dark:stroke-white"
                    >
                      <animate
                        attributeName="stroke-dashoffset"
                        values="50;0;0;0"
                        dur="8s"
                        repeatCount="indefinite"
                        begin="0.5s"
                      />
                    </line>

                    {/* Amplitude markers */}
                    <g
                      stroke="currentColor"
                      strokeWidth="0.5"
                      opacity="0"
                      className="stroke-gray-500 dark:stroke-gray-400"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;0.6;0.6"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                      <line x1="-3" y1="-20" x2="3" y2="-20" />
                      <line x1="-3" y1="20" x2="3" y2="20" />
                      <text
                        x="-8"
                        y="-17"
                        fontFamily="Arial, sans-serif"
                        fontSize="7"
                        fill="currentColor"
                        className="fill-gray-600 dark:fill-gray-400"
                      >
                        A
                      </text>
                      <text
                        x="-8"
                        y="23"
                        fontFamily="Arial, sans-serif"
                        fontSize="7"
                        fill="currentColor"
                        className="fill-gray-600 dark:fill-gray-400"
                      >
                        -A
                      </text>
                    </g>

                    {/* Main sine wave */}
                    <path
                      d="M 0 0 Q 25 -20 50 0 Q 75 20 100 0 Q 125 -20 150 0 Q 175 20 200 0"
                      stroke="url(#waveGradient)"
                      strokeWidth="2.5"
                      fill="none"
                      strokeDasharray="300"
                      strokeDashoffset="300"
                      opacity="0"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;0;1;1"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                      <animate
                        attributeName="stroke-dashoffset"
                        values="300;0;0;0"
                        dur="2s"
                        repeatCount="indefinite"
                        begin="3s"
                      />
                    </path>

                    {/* Moving point on wave */}
                    <circle r="2.5" fill="#fbbf24" opacity="0">
                      <animate
                        attributeName="opacity"
                        values="0;0;0;0;1;1"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                      <animate
                        attributeName="cx"
                        values="0;50;100;150;200;0"
                        dur="4s"
                        repeatCount="indefinite"
                        begin="4s"
                      />
                      <animate
                        attributeName="cy"
                        values="0;0;0;0;0;0"
                        dur="4s"
                        repeatCount="indefinite"
                        begin="4s"
                      />
                      <animateTransform
                        attributeName="transform"
                        type="translate"
                        values="0,0;0,-20;0,0;0,20;0,0;0,0"
                        dur="4s"
                        repeatCount="indefinite"
                        begin="4s"
                      />
                    </circle>

                    {/* Connecting line from pendulum to wave */}
                    <line
                      x1="50"
                      y1="-70"
                      x2="50"
                      y2="-25"
                      stroke="currentColor"
                      strokeWidth="1"
                      strokeDasharray="3,2"
                      opacity="0"
                      className="stroke-purple-500 dark:stroke-purple-400"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;0;0;0.5;0.5"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                    </line>

                    {/* Phase indicators */}
                    <g opacity="0">
                      <animate
                        attributeName="opacity"
                        values="0;0;0;0;0.7;0.7"
                        dur="8s"
                        repeatCount="indefinite"
                      />
                      <text
                        x="25"
                        y="-30"
                        fontFamily="Arial, sans-serif"
                        fontSize="7"
                        fill="currentColor"
                        className="fill-blue-600 dark:fill-blue-400"
                      >
                        π/2
                      </text>
                      <text
                        x="75"
                        y="35"
                        fontFamily="Arial, sans-serif"
                        fontSize="7"
                        fill="currentColor"
                        className="fill-red-600 dark:fill-red-400"
                      >
                        3π/2
                      </text>
                      <text
                        x="125"
                        y="-30"
                        fontFamily="Arial, sans-serif"
                        fontSize="7"
                        fill="currentColor"
                        className="fill-blue-600 dark:fill-blue-400"
                      >
                        5π/2
                      </text>
                    </g>
                  </g>

                  {/* Frequency display */}
                  <g transform="translate(180, 45)" opacity="0">
                    <animate
                      attributeName="opacity"
                      values="0;0;0;0;1;1"
                      dur="8s"
                      repeatCount="indefinite"
                    />
                    <rect
                      x="-20"
                      y="-8"
                      width="40"
                      height="16"
                      rx="8"
                      fill="#10b981"
                      opacity="0.9"
                    />
                    <text
                      x="0"
                      y="3"
                      fontFamily="Arial, sans-serif"
                      fontSize="8"
                      fontWeight="bold"
                      fill="white"
                      textAnchor="middle"
                    >
                      f ={" "}
                      <tspan>
                        <animate
                          values="0.25;0.25;0.25;0.25;0.25"
                          dur="4s"
                          repeatCount="indefinite"
                          begin="4s"
                        />
                        0.25
                      </tspan>{" "}
                      Hz
                    </text>
                  </g>

                  {/* Energy conservation indicator */}
                  <g transform="translate(20, 170)" opacity="0">
                    <animate
                      attributeName="opacity"
                      values="0;0;0;0;1;1"
                      dur="8s"
                      repeatCount="indefinite"
                    />
                    <text
                      x="0"
                      y="0"
                      fontFamily="Arial, sans-serif"
                      fontSize="9"
                      fill="currentColor"
                      className="fill-gray-700 dark:fill-gray-300"
                    >
                      E = ½kA² = constant
                    </text>
                  </g>
                </svg>
              </div>
            </div>
          </div>

          <div className="backdrop-blur-xl bg-gray-400/5 shadow-2xl dark:bg-white/5 border border-border/50 rounded-2xl w-48 h-48 absolute top-0 right-0 transform rotate-12 floating">
            <div className="p-4 h-full flex flex-col">
              <div className="text-sm mb-2">High Definition Maths</div>
              <div className="flex-1 flex items-center justify-center">
                {/* SVG */}
                <svg
                  viewBox="0 0 256 192"
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-full h-full"
                >
                  <defs>
                    <linearGradient
                      id="curve"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="0%"
                    >
                      <stop offset="0%" style={{ stopColor: "#6366f1" }} />
                      <stop offset="100%" style={{ stopColor: "#8b5cf6" }} />
                    </linearGradient>
                    <linearGradient
                      id="tangent"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="0%"
                    >
                      <stop offset="0%" style={{ stopColor: "#f59e0b" }} />
                      <stop offset="100%" style={{ stopColor: "#ef4444" }} />
                    </linearGradient>
                    <radialGradient id="point" cx="50%" cy="50%" r="50%">
                      <stop offset="0%" style={{ stopColor: "#ffffff" }} />
                      <stop offset="70%" style={{ stopColor: "#6366f1" }} />
                    </radialGradient>
                    <radialGradient id="pointLight" cx="50%" cy="50%" r="50%">
                      <stop offset="0%" style={{ stopColor: "#1f2937" }} />
                      <stop offset="70%" style={{ stopColor: "#6366f1" }} />
                    </radialGradient>
                  </defs>

                  {/* Background */}
                  <rect width="256" height="192" fill="transparent" />

                  {/* Title animation */}
                  <text
                    x="128"
                    y="20"
                    fontFamily="Arial, sans-serif"
                    fontSize="14"
                    fontWeight="bold"
                    fill="currentColor"
                    textAnchor="middle"
                    className="fill-gray-800 dark:fill-white"
                  >
                    dy/dx = x
                    <animate
                      attributeName="opacity"
                      values="0;1;1;1"
                      dur="6s"
                      repeatCount="indefinite"
                    />
                    <animateTransform
                      attributeName="transform"
                      type="scale"
                      values="0.5;1;1;1"
                      dur="6s"
                      repeatCount="indefinite"
                    />
                  </text>

                  {/* Coordinate system */}
                  <g transform="translate(128, 96)">
                    {/* Axes with draw-on animation */}
                    <line
                      x1="-80"
                      y1="0"
                      x2="80"
                      y2="0"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeDasharray="160"
                      strokeDashoffset="160"
                      className="stroke-gray-700 dark:stroke-white"
                    >
                      <animate
                        attributeName="stroke-dashoffset"
                        values="160;0;0;0"
                        dur="6s"
                        repeatCount="indefinite"
                      />
                    </line>
                    <line
                      x1="0"
                      y1="60"
                      x2="0"
                      y2="-60"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeDasharray="120"
                      strokeDashoffset="120"
                      className="stroke-gray-700 dark:stroke-white"
                    >
                      <animate
                        attributeName="stroke-dashoffset"
                        values="120;0;0;0"
                        dur="6s"
                        repeatCount="indefinite"
                        begin="0.25s"
                      />
                    </line>

                    {/* Grid lines appearing */}
                    <g
                      stroke="currentColor"
                      strokeWidth="0.5"
                      className="stroke-gray-400 dark:stroke-white"
                    >
                      <line x1="-40" y1="-60" x2="-40" y2="60">
                        <animate
                          attributeName="opacity"
                          values="0;0;0.3;0.3"
                          dur="6s"
                          repeatCount="indefinite"
                        />
                      </line>
                      <line x1="40" y1="-60" x2="40" y2="60">
                        <animate
                          attributeName="opacity"
                          values="0;0;0.3;0.3"
                          dur="6s"
                          repeatCount="indefinite"
                        />
                      </line>
                      <line x1="-80" y1="-30" x2="80" y2="-30">
                        <animate
                          attributeName="opacity"
                          values="0;0;0.3;0.3"
                          dur="6s"
                          repeatCount="indefinite"
                        />
                      </line>
                      <line x1="-80" y1="30" x2="80" y2="30">
                        <animate
                          attributeName="opacity"
                          values="0;0;0.3;0.3"
                          dur="6s"
                          repeatCount="indefinite"
                        />
                      </line>
                    </g>

                    {/* Slope field appearing sequentially */}
                    <g
                      stroke="currentColor"
                      strokeWidth="1"
                      className="stroke-gray-500 dark:stroke-slate-400"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;0.6;0.6"
                        dur="6s"
                        repeatCount="indefinite"
                      />
                      <line x1="-60" y1="-40" x2="-50" y2="-46" />
                      <line x1="-20" y1="-40" x2="-10" y2="-42" />
                      <line x1="20" y1="-40" x2="30" y2="-42" />
                      <line x1="60" y1="-40" x2="70" y2="-46" />
                      <line x1="-60" y1="0" x2="-50" y2="-6" />
                      <line x1="-20" y1="0" x2="-10" y2="-2" />
                      <line x1="20" y1="0" x2="30" y2="2" />
                      <line x1="60" y1="0" x2="70" y2="6" />
                      <line x1="-60" y1="40" x2="-50" y2="34" />
                      <line x1="-20" y1="40" x2="-10" y2="38" />
                      <line x1="20" y1="40" x2="30" y2="42" />
                      <line x1="60" y1="40" x2="70" y2="46" />
                    </g>

                    {/* Solution curves appearing */}
                    <g opacity="0">
                      <animate
                        attributeName="opacity"
                        values="0;0;0;0.4;0.4"
                        dur="6s"
                        repeatCount="indefinite"
                      />
                      <path
                        d="M -70 24.5 Q -40 8 -20 2 Q 0 0 20 2 Q 40 8 70 24.5"
                        stroke="#8b5cf6"
                        strokeWidth="1.5"
                        fill="none"
                        strokeDasharray="100"
                        strokeDashoffset="100"
                      >
                        <animate
                          attributeName="stroke-dashoffset"
                          values="100;0;0"
                          dur="1s"
                          repeatCount="indefinite"
                          begin="3s"
                        />
                      </path>
                      <path
                        d="M -70 44.5 Q -40 28 -20 22 Q 0 20 20 22 Q 40 28 70 44.5"
                        stroke="#8b5cf6"
                        strokeWidth="1.5"
                        fill="none"
                        strokeDasharray="100"
                        strokeDashoffset="100"
                      >
                        <animate
                          attributeName="stroke-dashoffset"
                          values="100;0;0"
                          dur="1s"
                          repeatCount="indefinite"
                          begin="3.25s"
                        />
                      </path>
                    </g>

                    {/* Main curve with draw-on animation */}
                    <path
                      id="mainCurve"
                      d="M -70 4.5 Q -40 -12 -20 -18 Q 0 -20 20 -18 Q 40 -12 70 4.5"
                      stroke="url(#curve)"
                      strokeWidth="2.5"
                      fill="none"
                      strokeDasharray="120"
                      strokeDashoffset="120"
                      opacity="0"
                    >
                      <animate
                        attributeName="opacity"
                        values="0;0;0;1;1"
                        dur="6s"
                        repeatCount="indefinite"
                      />
                      <animate
                        attributeName="stroke-dashoffset"
                        values="120;0;0;0"
                        dur="1.5s"
                        repeatCount="indefinite"
                        begin="3.5s"
                      />
                    </path>

                    {/* Animated point appearing after curve */}
                    <circle r="3" opacity="0">
                      <animate
                        attributeName="opacity"
                        values="0;0;0;0;1;1"
                        dur="6s"
                        repeatCount="indefinite"
                      />
                      <animate
                        attributeName="fill"
                        values="url(#pointLight);url(#point)"
                        dur="0.1s"
                        repeatCount="1"
                      />
                      <animateMotion
                        dur="2s"
                        repeatCount="indefinite"
                        begin="4s"
                      >
                        <mpath href="#mainCurve" />
                      </animateMotion>
                    </circle>

                    {/* Tangent line animation */}
                    <line stroke="url(#tangent)" strokeWidth="2" opacity="0">
                      <animate
                        attributeName="opacity"
                        values="0;0;0;0;0.8;0.8"
                        dur="6s"
                        repeatCount="indefinite"
                      />
                      <animate
                        attributeName="x1"
                        values="-80;-55;-30;-5;20;45;70;-80"
                        dur="2s"
                        repeatCount="indefinite"
                        begin="4s"
                      />
                      <animate
                        attributeName="y1"
                        values="16;-4;-14;-18;-14;-4;16;16"
                        dur="2s"
                        repeatCount="indefinite"
                        begin="4s"
                      />
                      <animate
                        attributeName="x2"
                        values="-60;-35;-10;15;40;65;90;-60"
                        dur="2s"
                        repeatCount="indefinite"
                        begin="4s"
                      />
                      <animate
                        attributeName="y2"
                        values="-4;-24;-34;-38;-34;-24;-4;-4"
                        dur="2s"
                        repeatCount="indefinite"
                        begin="4s"
                      />
                    </line>
                  </g>

                  {/* Slope value display */}
                  <g transform="translate(200, 170)" opacity="0">
                    <animate
                      attributeName="opacity"
                      values="0;0;0;0;1;1"
                      dur="6s"
                      repeatCount="indefinite"
                    />
                    <rect
                      x="-25"
                      y="-8"
                      width="50"
                      height="16"
                      rx="8"
                      fill="#3b82f6"
                    />
                    <text
                      x="0"
                      y="3"
                      fontFamily="Arial, sans-serif"
                      fontSize="10"
                      fontWeight="bold"
                      fill="white"
                      textAnchor="middle"
                    >
                      m ={" "}
                      <tspan>
                        <animate
                          values="-1.8;-1.4;-1;-0.6;-0.2;0.2;0.6;1;1.4;1.8;-1.8"
                          dur="2s"
                          repeatCount="indefinite"
                          begin="4s"
                        />
                        -1.8
                      </tspan>
                    </text>
                  </g>

                  {/* Solution equation reveal */}
                  <text
                    x="30"
                    y="180"
                    fontFamily="Arial, sans-serif"
                    fontSize="12"
                    fill="currentColor"
                    opacity="0"
                    className="fill-gray-800 dark:fill-white"
                  >
                    y = x²/2 + C
                    <animate
                      attributeName="opacity"
                      values="0;0;0;0;1;1"
                      dur="6s"
                      repeatCount="indefinite"
                    />
                    <animateTransform
                      attributeName="transform"
                      type="scale"
                      values="0.8;1;1"
                      dur="0.5s"
                      repeatCount="indefinite"
                      begin="4.5s"
                    />
                  </text>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NeuralAI;
