import os
from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict


class QueueConfig(BaseSettings):
    """Configuration for Upstash QStash message queue."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    # QStash Configuration
    qstash_token: str = ""
    qstash_url: str = "https://qstash.upstash.io"

    # Queue Names
    render_queue_name: str = "render-jobs"
    batch_render_queue_name: str = "batch-render-jobs"

    # Queue Settings
    queue_parallelism: int = 1
    max_retries: int = 3

    # Worker Configuration
    worker_base_url: str = ""
    worker_secret: str = ""

    # QStash Signing Keys for webhook verification
    qstash_current_signing_key: str = ""
    qstash_next_signing_key: str = ""

    # Job Settings
    job_timeout_seconds: int = 300  # 5 minutes
    cleanup_completed_jobs_after_hours: int = 24


# Global configuration instance
_queue_config: Optional[QueueConfig] = None


def get_queue_config() -> QueueConfig:
    """Get the global queue configuration instance."""
    global _queue_config
    if _queue_config is None:
        _queue_config = QueueConfig()
    return _queue_config


def validate_config() -> bool:
    """Validate that all required configuration is present."""
    try:
        config = get_queue_config()
        required_fields = [
            config.qstash_token,
            config.worker_base_url,
            config.worker_secret,
            config.qstash_current_signing_key,
            config.qstash_next_signing_key,
        ]
        return all(field for field in required_fields)
    except Exception:
        return False
