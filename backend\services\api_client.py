import logging
import requests
from typing import Optional, Dict, Any
from pydantic import  Field
from pydantic_settings import BaseSettings,SettingsConfigDict

logger = logging.getLogger(__name__)


class ApiClientConfig(BaseSettings):
    """Configuration for API client service."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    frontend_api_base_url: str = Field(
        default="http://localhost:3000",
        description="Base URL for frontend API endpoints"
    )
    api_timeout_seconds: int = Field(
        default=30,
        description="Timeout for API requests in seconds"
    )


class ApiClient:
    """Service for making HTTP requests to frontend API endpoints."""

    def __init__(self):
        self.config = ApiClientConfig()
        self.session = requests.Session()
        self.session.timeout = self.config.api_timeout_seconds

        # Set up default headers
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": "ClarifAI-Backend/1.0"
        })

    def update_entry_video_url(self, entry_id: str, video_url: str) -> bool:
        """
        Update an entry's video URL via the frontend API.

        Args:
            entry_id: The ID of the entry to update
            video_url: The URL of the uploaded video

        Returns:
            True if the update was successful, False otherwise
        """
        try:
            url = f"{self.config.frontend_api_base_url}/api/entries/update"

            payload = {
                "entryId": entry_id,
                "videoURL": video_url
            }

            logger.info(f"Updating entry {entry_id} with video URL: {video_url}")

            response = self.session.post(url, json=payload)
            response.raise_for_status()

            data = response.json()

            if data.get("success"):
                logger.info(f"Successfully updated entry {entry_id} with video URL")
                return True
            else:
                logger.error(f"API returned success=false for entry {entry_id}: {data}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP error updating entry {entry_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating entry {entry_id}: {e}")
            return False

    def get_entry_details(self, entry_id: str) -> Optional[Dict[str, Any]]:
        """
        Get entry details via the frontend API.

        Args:
            entry_id: The ID of the entry to retrieve

        Returns:
            Entry details if successful, None otherwise
        """
        try:
            url = f"{self.config.frontend_api_base_url}/api/entries/update"
            params = {"entryId": entry_id}

            response = self.session.get(url, params=params)
            response.raise_for_status()

            data = response.json()

            if data.get("success"):
                return data.get("entry")
            else:
                logger.error(f"API returned success=false for entry {entry_id}: {data}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP error retrieving entry {entry_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving entry {entry_id}: {e}")
            return None

    def health_check(self) -> bool:
        """
        Check if the frontend API is accessible.

        Returns:
            True if the API is accessible, False otherwise
        """
        try:
            # Try to make a simple request to check connectivity
            url = f"{self.config.frontend_api_base_url}/api/entries/update"
            params = {"entryId": "health-check"}

            response = self.session.get(url, params=params, timeout=5)

            # We expect a 400 or 404 response for invalid entry ID, which means API is working
            return response.status_code in [400, 404, 200]

        except Exception as e:
            logger.error(f"Frontend API health check failed: {e}")
            return False


# Global service instance
_api_client: Optional[ApiClient] = None


def get_api_client() -> ApiClient:
    """Get the global API client instance."""
    global _api_client
    if _api_client is None:
        _api_client = ApiClient()
    return _api_client
